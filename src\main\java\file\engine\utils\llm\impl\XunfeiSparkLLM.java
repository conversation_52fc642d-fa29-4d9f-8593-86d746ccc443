//package file.engine.utils.llm.impl;
//
//import file.engine.entity.ApiDescriptor;
//import file.engine.entity.Args;
//import file.engine.utils.llm.LLMInterface;
//import io.github.briqt.spark4j.SparkClient;
//import io.github.briqt.spark4j.constant.SparkApiVersion;
//import io.github.briqt.spark4j.exception.SparkException;
//import io.github.briqt.spark4j.model.SparkMessage;
//import io.github.briqt.spark4j.model.SparkRequestBuilder;
//import io.github.briqt.spark4j.model.SparkSyncChatResponse;
//import io.github.briqt.spark4j.model.request.SparkRequest;
//import io.github.briqt.spark4j.model.request.function.SparkFunctionBuilder;
//import io.github.briqt.spark4j.model.response.SparkResponseFunctionCall;
//import lombok.extern.slf4j.Slf4j;
//
//import java.io.File;
//import java.io.PrintWriter;
//import java.io.StringWriter;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.concurrent.ConcurrentHashMap;
//import java.util.function.Function;
//
//@Slf4j
//public class XunfeiSparkLLM implements LLMInterface {
//    private final SparkClient sparkClient = new SparkClient();
//    private final ConcurrentHashMap<ApiDescriptor, Function<Map<String, Object>, String>> apiMap = new ConcurrentHashMap<>();
//    private static final String SYSTEM_DESCRIPTION = "请你作为一个搜索助手，当用户告诉你需要进行搜索的时候，调用相应的工具进行搜索";
//
//    private final String appId;
//    private final String apiKey;
//    private final String apiSecret;
//
//    public XunfeiSparkLLM(String appId, String apiKey, String apiSecret) {
//        this.appId = appId;
//        this.apiKey = apiKey;
//        this.apiSecret = apiSecret;
//    }
//
//    @Override
//    public void init() {
//        sparkClient.appid = appId;
//        sparkClient.apiKey = apiKey;
//        sparkClient.apiSecret = apiSecret;
//    }
//
//    @Override
//    public String chat(String message) {
//        // 消息列表，可以在此列表添加历史对话记录
//        List<SparkMessage> messages = new ArrayList<>();
//        messages.add(SparkMessage.systemContent(SYSTEM_DESCRIPTION));
//        messages.add(SparkMessage.userContent(message));
//        // 构造请求
//        SparkRequestBuilder sparkRequestBuilder = SparkRequest.builder()
//                .messages(messages)
//                .maxTokens(2048)
//                .temperature(0.2)
//                .apiVersion(SparkApiVersion.V3_5);
//        apiMap.keySet().forEach(apiDescriptor -> {
//            SparkFunctionBuilder sparkFunctionBuilder = SparkFunctionBuilder.functionName(apiDescriptor.getApiName())
//                    .description(apiDescriptor.getApiDescription());
//            apiDescriptor.getArgs().forEach(arg -> {
//                sparkFunctionBuilder.addParameterProperty(arg.name(), arg.type(), arg.description());
//                if (arg.required()) {
//                    sparkFunctionBuilder.addParameterRequired(arg.name());
//                }
//            });
//            sparkRequestBuilder.addFunction(sparkFunctionBuilder.build());
//        });
//
//        try {
//            // 同步调用
//            SparkSyncChatResponse chatResponse = sparkClient.chatSync(sparkRequestBuilder.build());
//            SparkResponseFunctionCall functionCall = chatResponse.getFunctionCall();
//            if (null != functionCall) {
//                String functionName = functionCall.getName();
//                Map<String, Object> arguments = functionCall.getMapArguments();
//
//                final String[] response = new String[1];
//                apiMap.entrySet().stream()
//                        .filter(api -> api.getKey().getApiName().equals(functionName))
//                        .findFirst()
//                        .ifPresent(api -> response[0] = api.getValue().apply(arguments));
//                return response[0];
//            } else {
//                log.info("LLM response: {}", chatResponse.getContent());
//            }
//        } catch (SparkException e) {
//            StringWriter stringWriter = new StringWriter();
//            e.printStackTrace(new PrintWriter(stringWriter));
//            log.error("error {}", stringWriter);
//        }
//        return "";
//    }
//
//    @Override
//    public String chatWithImage(String message, List<File> images) {
//        return "";
//    }
//
//    @Override
//    public void registerApi(String apiName,
//                            String apiFunctionName,
//                            String apiDescription,
//                            String apiVersion,
//                            List<Args> args,
//                            Function<Map<String, Object>, String> func) {
//        ApiDescriptor apiDescriptor = new ApiDescriptor(apiName, apiFunctionName, apiDescription, apiVersion, args);
//        apiMap.put(apiDescriptor, func);
//    }
//}
