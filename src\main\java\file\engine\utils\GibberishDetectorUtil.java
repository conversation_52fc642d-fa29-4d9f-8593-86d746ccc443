package file.engine.utils;

import com.paypal.gibberishdetector.GibberishDetector;
import com.paypal.gibberishdetector.GibberishDetectorExtended;
import com.paypal.gibberishdetector.GibberishDetectorFactory;

public class GibberishDetectorUtil {
    private static final String alphabet = "abcdefghijklmnopqrstuvwxyz ";

    private static final GibberishDetectorFactory factory = new GibberishDetectorFactory(GibberishDetectorExtended.class);

    private static final GibberishDetector gibberishDetector = factory.createGibberishDetectorFromLocalFile("bigEnglish.txt",
            "goodEnglish.txt", "badEnglish.txt", alphabet);

    public static double getMeaningfulProbability(String word) {
        return gibberishDetector.getMeaningfulProbability(word);
    }
}
