/* DO NOT EDIT THIS FILE - it is machine generated */
#include "jni.h"
/* Header for class file_engine_dllInterface_ParentProcessCheck */

#ifndef _Included_file_engine_dllInterface_ParentProcessCheck
#define _Included_file_engine_dllInterface_ParentProcessCheck
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     file_engine_dllInterface_ParentProcessCheck
 * Method:    isParentProcessExist
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_file_engine_dllInterface_ParentProcessCheck_isParentProcessExist
  (JNIEnv *, jobject);

/*
 * Class:     file_engine_dllInterface_ParentProcessCheck
 * Method:    checkRequestAndParentProcessSignature
 * Signature: (S)Z
 */
JNIEXPORT jboolean JNICALL Java_file_engine_dllInterface_ParentProcessCheck_checkRequestAndParentProcessSignature
  (JNIEnv *, jobject, jshort);

#ifdef __cplusplus
}
#endif
#endif
