package file.engine.services.utils.reader.impl;

import file.engine.services.utils.reader.ContentReader;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.jetbrains.annotations.NotNull;

import javax.xml.namespace.QName;
import javax.xml.stream.XMLEventReader;
import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.events.EndElement;
import javax.xml.stream.events.StartElement;
import javax.xml.stream.events.XMLEvent;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.*;

@Slf4j
public class WordFileReader implements ContentReader {

    private String readDoc(File file) {
        StringBuilder stringBuilder = new StringBuilder();
        try (FileInputStream fis = new FileInputStream(file.getAbsolutePath());
             HWPFDocument document = new HWPFDocument(fis);
             var extractor = new WordExtractor(document)) {
            String[] fileData = extractor.getParagraphText();
            for (String fileDatum : fileData) {
                if (fileDatum != null) {
                    stringBuilder.append(fileDatum);
                }
            }
        } catch (Exception exep) {
            throw new RuntimeException(exep);
        }
        return stringBuilder.toString();
    }

    @NotNull
    private static String readDocx(String fileAbsPath) {
        Path source = Paths.get(fileAbsPath);
        try (FileSystem fs = FileSystems.newFileSystem(source)) {
            Path document = fs.getPath("/word/document.xml");
            XMLEventReader reader = XMLInputFactory.newInstance().createXMLEventReader(Files.newInputStream(document));
            var content = new StringBuilder();

            boolean inParagraph = false;
            String paragraphText = "";
            while (reader.hasNext()) {
                XMLEvent event = (XMLEvent) reader.next();
                if (event.isStartElement()) {
                    StartElement startElement = (StartElement) event;
                    QName startElementName = startElement.getName();
                    if (startElementName.getLocalPart().equalsIgnoreCase("p")) { //start element of paragraph
                        inParagraph = true;
                        paragraphText = "";
                    }
                } else if (event.isCharacters() && inParagraph) { //characters in elements of this paragraph
                    String characters = event.asCharacters().getData();
                    paragraphText += characters; // can be splitted into different run elements
                } else if (event.isEndElement() && inParagraph) {
                    EndElement endElement = (EndElement) event;
                    QName endElementName = endElement.getName();
                    if (endElementName.getLocalPart().equalsIgnoreCase("p")) { //end element of paragraph
                        inParagraph = false;
                        content.append(paragraphText);
                        content.append("\n");
                    }
                }
            }
            return content.toString();
        } catch (IOException | XMLStreamException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String readContent(String fileAbsPath) {
        String lowerCase = fileAbsPath.toLowerCase();
        if (lowerCase.endsWith(".doc")) {
            return readDoc(new File(fileAbsPath));
        } else if (lowerCase.endsWith(".docx")) {
            return readDocx(fileAbsPath);
        } else {
            return "";
        }
    }
}
