package file.engine.utils;

import com.google.gson.*;
import com.google.gson.stream.JsonReader;

import java.io.StringReader;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

public class JsonExtractUtil {

    public static String extractJsonString(String input) throws IllegalArgumentException {
        String preprocessed = preprocess(input);
        List<String> candidates = findJsonCandidates(preprocessed);

        // 尝试候选字符串
        for (String candidate : candidates) {
            if (isValidJson(candidate)) {
                return candidate;
            }
        }

        // 最后尝试整个字符串
        if (isValidJson(preprocessed)) {
            return preprocessed;
        }

        throw new IllegalArgumentException("No valid JSON found");
    }

    private static String preprocess(String s) {
        // 移除代码块标记（包含可选的 json 标识）
        s = s.trim().replaceAll("^```(json)?", "").replaceAll("```$", "");
        // 将单引号转为双引号（处理常见错误格式）
        return s.replace("'", "\"");
    }

    private static List<String> findJsonCandidates(String s) {
        List<String> candidates = new ArrayList<>();
        List<Character> stack = new ArrayList<>();
        int start = -1;

        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);
            if (c == '{' || c == '[') {
                if (stack.isEmpty()) {
                    start = i; // 记录起始位置
                }
                stack.add(c);
            } else if (c == '}' || c == ']') {
                if (!stack.isEmpty()) {
                    char last = stack.removeLast();
                    if ((c == '}' && last != '{') || (c == ']' && last != '[')) {
                        stack.clear();
                        continue;
                    }
                    if (stack.isEmpty()) {
                        String candidate = s.substring(start, i + 1);
                        candidates.add(candidate);
                        start = -1;
                    }
                }
            }
        }

        // 按长度降序排序（优先尝试最长的候选）
        candidates.sort(Comparator.comparingInt(String::length).reversed());
        return candidates;
    }

    private static boolean isValidJson(String json) {
        try {
            // 使用严格模式验证
            JsonReader reader = new JsonReader(new StringReader(json));
            reader.setStrictness(Strictness.STRICT);
            JsonParser.parseReader(reader);
            return true;
        } catch (JsonSyntaxException e) {
            return false;
        }
    }
}