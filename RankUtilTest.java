import file.engine.utils.RankUtil;

public class RankUtilTest {
    
    public static void main(String[] args) {
        testConsecutiveMatchScore();
        testRankingLogic();
    }
    
    public static void testConsecutiveMatchScore() {
        System.out.println("=== 测试连续匹配分数 ===");
        String[] keywords = {"geekexe"};
        
        // 测试 geek.exe - 更连续的匹配
        double score1 = RankUtil.getConsecutiveMatchScore("geek.exe", keywords);
        
        // 测试 geek64.exe - 较分散的匹配
        double score2 = RankUtil.getConsecutiveMatchScore("geek64.exe", keywords);
        
        System.out.println("geek.exe score: " + score1);
        System.out.println("geek64.exe score: " + score2);
        
        if (score1 > score2) {
            System.out.println("✓ 正确：geek.exe 得分更高（连续匹配更好）");
        } else {
            System.out.println("✗ 错误：geek.exe 应该得分更高");
        }
        System.out.println();
    }
    
    public static void testRankingLogic() {
        System.out.println("=== 测试排序逻辑：分数越大越好 ===");
        String[] keywords = {"test"};
        
        // 连续匹配好的文件应该得到更高分数
        double score1 = RankUtil.getConsecutiveMatchScore("test.txt", keywords);
        double score2 = RankUtil.getConsecutiveMatchScore("txexsxt.txt", keywords);
        
        System.out.println("连续匹配 'test.txt': " + score1);
        System.out.println("分散匹配 'txexsxt.txt': " + score2);
        
        if (score1 > score2) {
            System.out.println("✓ 正确：连续匹配得分更高");
        } else {
            System.out.println("✗ 错误：连续匹配应该得分更高");
        }
        
        // 测试异常值处理
        double score3 = RankUtil.getConsecutiveMatchScore("", keywords);
        double score4 = RankUtil.getConsecutiveMatchScore("test.txt", new String[]{});
        
        System.out.println("空文件名分数: " + score3);
        System.out.println("空关键词分数: " + score4);
        
        if (!Double.isNaN(score3) && !Double.isInfinite(score3) && 
            !Double.isNaN(score4) && !Double.isInfinite(score4)) {
            System.out.println("✓ 正确：没有产生NaN或无穷大值");
        } else {
            System.out.println("✗ 错误：产生了异常值");
        }
        System.out.println();
    }
}
