package file.engine.utils;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import file.engine.utils.system.properties.IsDebug;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import me.marnic.jiconextract2.JIconExtract;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.Base64;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class IconUtil {
    private static final ConcurrentHashMap<String, ImageIcon> constantIconMap = new ConcurrentHashMap<>();
    private static final Cache<ImageIconCacheKey, ImageIcon> iconCache = Caffeine.newBuilder()
            .softValues()
            .maximumSize(10)
            .build();
    private static final Cache<ImageIconCacheKey, String> iconBase64Cache = Caffeine.newBuilder()
            .softValues()
            .maximumSize(10)
            .build();

    private record ImageIconCacheKey(String path, int width, int height) {
    }

    static {
        initIconCache();
    }

    public static ImageIcon changeIconSize(ImageIcon icon, int width, int height) {
        if (icon == null) {
            return null;
        }
        Image image = icon.getImage().getScaledInstance(width, height, Image.SCALE_FAST);
        return new ImageIcon(image);
    }

    private static void initIconCache() {
        BufferedImage folderImage = JIconExtract.getIconForFile(256, 256, new File(""));
        constantIconMap.put("folderImageIcon", new ImageIcon(folderImage));
        constantIconMap.put("blankIcon", new ImageIcon(Objects.requireNonNull(IconUtil.class.getResource("/icons/blank.png"))));
    }

    public static ImageIcon getBigIcon(String path,
                                       boolean isUwp,
                                       int width,
                                       int height) {
        if (path == null || path.isEmpty()) {
            return changeIconSize(constantIconMap.get("blankIcon"), width, height);
        }
        if (constantIconMap.containsKey(path)) {
            return changeIconSize(constantIconMap.get(path), width, height);
        }
        File f = new File(path);
        if (!f.exists()) {
            return changeIconSize(constantIconMap.get("blankIcon"), width, height);
        }
        //已保存的常量图标
        //检测是否为文件夹
        if (f.isDirectory() && !isUwp) {
            return changeIconSize(constantIconMap.get("folderImageIcon"), width, height);
        }
        var imageIconCacheKey = new ImageIconCacheKey(path, width, height);
        var ifPresent = iconCache.getIfPresent(imageIconCacheKey);
        if (ifPresent != null) {
            if (IsDebug.isDebug) {
                log.info("icon cache hit, path: {}", path);
            }
            return ifPresent;
        }
        try {
            if (isUwp) {
                ImageIcon uwpIcon = getUwpIcon(path, width, height);
                if (uwpIcon == null) {
                    return changeIconSize(constantIconMap.get("blankIcon"), width, height);
                }
                iconCache.put(imageIconCacheKey, uwpIcon);
                return uwpIcon;
            } else {
                BufferedImage iconForFile = JIconExtract.getIconForFile(width, height, f);
                if (iconForFile == null) {
                    return changeIconSize(constantIconMap.get("blankIcon"), width, height);
                }
                ImageIcon imageIcon = new ImageIcon(iconForFile);
                iconCache.put(imageIconCacheKey, imageIcon);
                return imageIcon;
            }
        } catch (Exception e) {
            return changeIconSize(constantIconMap.get("blankIcon"), width, height);
        }
    }

    public static String getBase64Image(String path, ImageIcon icon, int width, int height) {
        var base64Key = new ImageIconCacheKey(path, width, height);
        String ifPresent = iconBase64Cache.getIfPresent(base64Key);
        if (ifPresent != null) {
            if (IsDebug.isDebug) {
                log.info("icon base64 cache hit, path: {}", path);
            }
            return ifPresent;
        }
        BufferedImage bi = new BufferedImage(
                icon.getIconWidth(),
                icon.getIconHeight(),
                BufferedImage.TYPE_INT_RGB);
        Graphics2D graphics = bi.createGraphics();
        bi = graphics.getDeviceConfiguration().createCompatibleImage(width, height, Transparency.TRANSLUCENT);
        // Draw the image on to the buffered image
        Graphics2D bGr = bi.createGraphics();
        bGr.drawImage(icon.getImage(), 0, 0, null);
        bGr.dispose();
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            ImageIO.write(bi, "png", bos);
            byte[] imageBytes = bos.toByteArray();
            String base64Image = "data:image/png;base64," + Base64.getEncoder().encodeToString(imageBytes);
            iconBase64Cache.put(base64Key, base64Image);
            return base64Image;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @SneakyThrows
    private static ImageIcon getUwpIcon(String installLocation, int width, int height) {
        if (installLocation.isEmpty()) {
            return null;
        }
        File appManifest = new File(installLocation, "AppxManifest.xml");
        return getIconFromManifest(appManifest, width, height);
    }

    private static ImageIcon getIconFromManifest(File appxManifest, int width, int height) throws DocumentException {
        String installLocation = appxManifest.getParentFile().getAbsolutePath();
        SAXReader saxReader = new SAXReader();
        Document doc = saxReader.read(appxManifest);
        Element rootElement = doc.getRootElement();
        Element properties = rootElement.element("Properties");
        Element logo = properties.element("Logo");
        String logoPath = logo.getStringValue();
        File file = new File(installLocation, logoPath);
        File logoLocation = file.getParentFile();
        String logoName = file.getName();
        String logoNamePrefix = removeFileNameSuffix(logoName);
        String logoNamePrefixWithScale = logoNamePrefix + ".scale-";
        File[] logoFiles = logoLocation.listFiles();
        if (logoFiles != null) {
            for (File listFile : logoFiles) {
                String logoFileName = listFile.getName();
                if (logoFileName.startsWith(logoNamePrefixWithScale)) {
                    return changeIconSize(new ImageIcon(listFile.getAbsolutePath()), width, height);
                }
            }
            for (File listFile : logoFiles) {
                String logoFileName = listFile.getName();
                if (logoFileName.startsWith(logoNamePrefix)) {
                    return changeIconSize(new ImageIcon(listFile.getAbsolutePath()), width, height);
                }
            }
        }
        return null;
    }

    private static String removeFileNameSuffix(String fileName) {
        int pos = fileName.lastIndexOf('.');
        return fileName.substring(0, pos);
    }
}
