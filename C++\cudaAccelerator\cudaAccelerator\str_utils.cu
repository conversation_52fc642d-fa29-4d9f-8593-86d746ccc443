﻿#include "framework.h"
#include "cuda_runtime.h"
#include "str_utils.cuh"
#include "constans.h"

__device__ int strcmp_cuda(const char* str1, const char* str2)
{
	while (*str1)
	{
		if (*str1 > *str2)return 1;
		if (*str1 < *str2)return -1;
		++str1;
		++str2;
	}
	if (*str1 < *str2)return -1;
	return 0;
}


__device__ char* strlwr_cuda(char* src)
{
	while (*src != '\0')
	{
		if (*src >= 'A' && *src <= 'Z')
		{
			*src += 32;
		}
		++src;
	}
	return src;
}


__device__ const char* strstr_cuda(const char* s1, const char* s2)
{
	int n;
	if (*s2) //两种情况考虑
	{
		while (*s1)
		{
			for (n = 0; *(s1 + n) == *(s2 + n); ++n)
			{
				if (!*(s2 + n + 1)) //查找的下一个字符是否为'\0'
				{
					return s1;
				}
			}
			++s1;
		}
		return nullptr;
	}
	return s1;
}

__device__ char* strncpy_cuda(char* dst, const char* src, size_t n)
{
	if (n != 0)
	{
		char* d = dst;
		const char* s = src;

		do
		{
			if ((*d++ = *s++) == 0)
			{
				/* NUL pad the remaining n-1 bytes */
				while (--n != 0)
					*d++ = 0;
				break;
			}
		} while (--n != 0);
	}
	return dst;
}

__device__ void substring(const char* str, const size_t start, const size_t length, char* output)
{
	if (start + length > strlen_cuda(str))
	{
		return;
	}

	strncpy_cuda(output, str + start, length); // 复制子字符串
	output[length] = '\0'; // 添加终止符
}

__device__ char* strrchr_cuda(const char* s, int c)
{
	if (s == nullptr)
	{
		return nullptr;
	}

	char* p_char = nullptr;
	while (*s != '\0')
	{
		if (*s == static_cast<char>(c))
		{
			p_char = const_cast<char*>(s);
		}
		++s;
	}

	return p_char;
}

__device__ char* strcpy_cuda(char* dst, const char* src)
{
	char* ret = dst;
	while ((*dst++ = *src++) != '\0')
	{
	}
	return ret;
}

__device__ size_t strlen_cuda(const char* str)
{
	size_t count = 0;
	while (*str != '\0')
	{
		count++;
		++str;
	}
	return count;
}

__device__ char* strcat_cuda(char* dst, char const* src)
{
	if (dst == nullptr || src == nullptr)
	{
		return nullptr;
	}

	char* tmp = dst;

	while (*dst != '\0') //这个循环结束之后，dst指向'\0'
	{
		dst++;
	}

	while (*src != '\0')
	{
		*dst++ = *src++; //把src指向的内容赋值给dst
	}

	*dst = '\0'; //这句一定要加，否则最后一个字符会乱码
	return tmp;
}

__device__ void str_add_single(char* dst, const char c, size_t* current_len, const size_t max_size)
{
	if (*current_len < max_size - 1)
	{
		// 保留最后一位给'\0'
		dst[*current_len] = c;
		(*current_len)++;
		dst[*current_len] = '\0';
	}
}

__device__ bool is_str_contains_chinese(const char* source)
{
	int i = 0;
	while (source[i] != 0)
	{
		if (source[i] & 0x80 && source[i] & 0x40 && source[i] & 0x20)
		{
			return true;
		}
		if (source[i] & 0x80 && source[i] & 0x40)
		{
			i += 2;
		}
		else
		{
			i += 1;
		}
	}
	return false;
}

__device__ bool pattern_char_match(const char* pattern, const char* str)
{
	int pattern_index = 0;
	int last_matched_index = -1;  // 上一个匹配的字符在str中的位置
	int str_index = 0;
	int continuous_match_length = 0;  // 当前连续匹配的长度
	int max_continuous_match = 0;     // 最大连续匹配长度

	const int pattern_length = static_cast<int>(strlen_cuda(pattern));
	const int str_length = static_cast<int>(strlen_cuda(str));
	const bool enable_distance_check = str_length > 35;  // 只有超长文件名才启用距离检查

	while (pattern_index < pattern_length && str_index < str_length)
	{
		const char current_char = str[str_index];

		if (current_char == ' ')
		{
			// 遇到空格时重置last_matched_index为空格的位置
			last_matched_index = str_index;
		}

		// 如果字符匹配，则移动pattern的指针
		if (pattern[pattern_index] == current_char)
		{
			// 只有在超长文件名时才检查距离，如果不是第一个字符，检查距离是否超过3个字符
			if (enable_distance_check && last_matched_index != -1 && str_index - last_matched_index > 4)  // 距离超过3个字符
			{
				return false;
			}

			// 检查是否连续匹配
			if (last_matched_index != -1 && str_index == last_matched_index + 1)
			{
				continuous_match_length++;
			}
			else
			{
				continuous_match_length = 1;  // 重新开始计算连续匹配
			}

			// 更新最大连续匹配长度
			max_continuous_match = max(max_continuous_match, continuous_match_length);

			last_matched_index = str_index;
			pattern_index++;
		}
		// 无论是否匹配，移动目标字符串的指针
		str_index++;
	}
	// 如果匹配完了整个pattern，并且至少有2个字符连续匹配，说明匹配成功
	return pattern_index == pattern_length && max_continuous_match >= 2;
}

__device__ bool fuzzy_match(const char* pattern, const char* str)
{
	const size_t pattern_len = strlen_cuda(pattern);
	const size_t str_len = strlen_cuda(str);
	if (str_len < pattern_len)
	{
		return false;
	}
	size_t max_distance;
	if (pattern_len > 9)
	{
		max_distance = 2;
	}
	else
	{
		max_distance = 1;
	}
	size_t start_pos = 0;
	while (start_pos <= str_len - pattern_len)
	{
		char sub_str[MAX_PATH_LENGTH]{};
		const size_t sub_str_len = min(pattern_len, str_len - start_pos);
		substring(str, start_pos, sub_str_len, sub_str);
		const size_t distance = calc_str_distance(pattern, sub_str);
		if (distance <= max_distance)
		{
			return true;
		}
		++start_pos;
	}
	return false;
}

/**
 * 计算字符串相差的错误字符
 * @param pattern 匹配规则
 * @param str 待匹配字符串
 * @return str与pattern相差几个错误字符
 */
__device__ size_t calc_str_distance(const char* pattern, const char* str)
{
	const size_t m = strlen_cuda(pattern);
	const size_t n = strlen_cuda(str);
	if (m > n)
	{
		return 0xFFFFFFFF;
	}

	constexpr size_t MAX_PATTERN_LENGTH = 6;

	if (n > MAX_PATTERN_LENGTH)
	{
		return 0xFFFFFFFF;
	}

	size_t dp[MAX_PATTERN_LENGTH][MAX_PATTERN_LENGTH]{};

	for (size_t i = 0; i <= m; i++)
	{
		for (size_t j = 0; j <= n; j++)
		{
			if (i == 0)
			{
				dp[i][j] = j; // 将pattern转换为空字符串的步骤数
			}
			else if (j == 0)
			{
				dp[i][j] = i; // 将str转换为空字符串的步骤数
			}
			else if (pattern[i - 1] == str[j - 1])
			{
				dp[i][j] = dp[i - 1][j - 1]; // 如果字符相同，不需要额外步骤
			}
			else
			{
				dp[i][j] = 1 + min(min(dp[i - 1][j],
					dp[i][j - 1]),
					dp[i - 1][j - 1]);
			}
		}
	}
	return dp[m][n];
}

__device__ void get_file_name(const char* path, char* output)
{
	const char* p = strrchr_cuda(path, '\\');
	strcpy_cuda(output, p + 1);
}

__device__ void get_parent_path(const char* path, char* output)
{
	strcpy_cuda(output, path);
	char* p = strrchr_cuda(output, '\\');
	*p = '\0';
}
