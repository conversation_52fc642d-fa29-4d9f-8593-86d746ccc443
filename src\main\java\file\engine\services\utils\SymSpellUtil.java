package file.engine.services.utils;

import io.gitlab.rxp90.jsymspell.SymSpell;
import io.gitlab.rxp90.jsymspell.SymSpellBuilder;
import io.gitlab.rxp90.jsymspell.api.Bigram;
import io.gitlab.rxp90.jsymspell.api.SuggestItem;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public enum SymSpellUtil {
    INSTANCE;

    private final int MAX_DISTANCE = 3;
    @Getter
    private final SymSpell symSpell;

    SymSpellUtil() {
        var bigramsStream = SymSpellUtil.class.getResourceAsStream("/bigrams.txt");
        if (bigramsStream == null) {
            throw new NullPointerException("bigram.txt not found in resources");
        }
        var wordsStream = SymSpellUtil.class.getResourceAsStream("/words.txt");
        if (wordsStream == null) {
            throw new NullPointerException("words.txt not found in resources");
        }
        try (var bigramReader = new BufferedReader(new InputStreamReader(bigramsStream, StandardCharsets.UTF_8));
             var wordsReader = new BufferedReader(new InputStreamReader(wordsStream, StandardCharsets.UTF_8))) {
            ArrayList<String> bigramList = new ArrayList<>();
            ArrayList<String> wordsList = new ArrayList<>();
            String eachLine;

            while ((eachLine = bigramReader.readLine()) != null) {
                bigramList.add(eachLine);
            }
            while ((eachLine = wordsReader.readLine()) != null) {
                wordsList.add(eachLine);
            }

            Map<Bigram, Long> bigrams = bigramList.stream()
                    .map(line -> line.split(" "))
                    .collect(Collectors.toMap(tokens -> new Bigram(tokens[0], tokens[1]), tokens -> Long.parseLong(tokens[2])));
            Map<String, Long> unigrams = wordsList.stream()
                    .map(line -> line.split(","))
                    .collect(Collectors.toMap(tokens -> tokens[0], tokens -> Long.parseLong(tokens[1])));

            this.symSpell = new SymSpellBuilder().setUnigramLexicon(unigrams)
                    .setBigramLexicon(bigrams)
                    .setMaxDictionaryEditDistance(MAX_DISTANCE)
                    .createSymSpell();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public String lookup(String word) {
        try {
            List<SuggestItem> suggestItems = this.symSpell.lookupCompound(word, MAX_DISTANCE, false);
            if (suggestItems == null || suggestItems.isEmpty()) {
                return word;
            }
            SuggestItem first = suggestItems.getFirst();
            return first.getSuggestion();
        } catch (Exception e) {
            log.error("Error: {}", e.getMessage(), e);
            return word;
        }
    }
}
