<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>github.fileengine</groupId>
    <artifactId>File-Engine-Core</artifactId>
    <version>1.0.0</version>
    <repositories>
        <repository>
            <id>mulesoft</id>
            <url>https://repository.mulesoft.org/nexus/content/repositories/public/</url>
        </repository>
        <repository>
            <id>projectlombok.org</id>
            <url>https://projectlombok.org/edge-releases</url>
        </repository>
        <repository>
            <id>github</id>
            <name>GitHub Apache Maven Packages</name>
            <url>https://maven.pkg.github.com/ollama4j/ollama4j</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <build>
        <finalName>File-Engine-Core</finalName>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>properties-maven-plugin</artifactId>
                <version>1.1.0</version>
                <executions>
                    <execution>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>write-project-properties</goal>
                        </goals>
                        <configuration>
                            <outputFile>${project.build.outputDirectory}/project-info.properties</outputFile>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>modify-sources</id>
                        <phase>generate-sources</phase> <!-- 在 compile 之前执行 -->
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <replaceregexp
                                        file="${debug.file}"
                                        match="(?s)public\s+static\s+final\s+boolean\s+isDebug\s*;\s*static\s*\{\s*String\s+res\s*=\s*System\.getProperty\(&quot;File_Engine_Debug&quot;\);\s*isDebug\s*=\s*&quot;true&quot;\.equalsIgnoreCase\(res\);\s*\}"
                                        replace="public static final boolean isDebug = false;"
                                        byline="false"/>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.8.0</version> <!-- 使用最新版本 -->
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <!-- 指定依赖项的复制目录 -->
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <!-- 复制所有的运行时依赖 -->
                            <includeScope>runtime</includeScope>
                            <!-- 可选：排除某些依赖项 -->
                            <excludeArtifactIds>okio-jvm</excludeArtifactIds>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.github.wvengen</groupId>
                <artifactId>proguard-maven-plugin</artifactId>
                <version>2.6.1</version>
                <executions>
                    <execution>
                        <id>obfuscation-packaging</id>
                        <phase>package</phase>
                        <goals>
                            <goal>proguard</goal>
                        </goals>
                        <configuration>
                            <proguardVersion>${tool.proguard.version}</proguardVersion>
                            <obfuscate>true</obfuscate>
                            <attach>true</attach>
                            <appendClassifier>false</appendClassifier>
                            <addMavenDescriptor>true</addMavenDescriptor>
                            <injar>${project.build.finalName}.jar</injar>
                            <injarNotExistsSkip>true</injarNotExistsSkip>
                            <libs>
                                <lib>${java.home}/jmods</lib>
                            </libs>

                            <options>
                                <option>-dontshrink</option>
                                <option>-dontoptimize</option>
                                <option>-keep class !file.engine.** { *; }</option>
                                <option>-keep class file.engine.dllInterface.Encryption { *; }</option>
                                <option>-keepdirectories [win32-native/**, file.engine.dllInterface,
                                    file.engine.entity.*, file.engine.event.handler.impl.*]
                                </option>
                                <option>-keepnames class file.engine.entity.*</option>
                                <option>-keepnames class file.engine.controller.Core</option>
                                <option>-keepnames class file.engine.services.DatabaseService</option>
                                <option>-keepnames class file.engine.configs.AllConfigs</option>
                                <option>-keepnames class file.engine.services.DatabaseService$GPUCacheService</option>
                                <option>-keepnames class file.engine.services.DaemonService</option>
                                <option>-keepnames class file.engine.services.LuceneIndexService</option>
                                <option>-keepclassmembers class file.engine.entity.* {
                                    private <![CDATA[<fields>]]>;
                                    private <![CDATA[<methods>]]>;
                                    }
                                </option>
                                <option>-keepclassmembers class file.engine.entity.SearchResult {
                                    private <![CDATA[<fields>]]>;
                                    private <![CDATA[<methods>]]>;
                                    }
                                </option>
                                <option>-keepnames class file.engine.MainClass</option>
                                <option>-keepclassmembers,allowoptimization class file.engine.MainClass {
                                    public static void main(java.lang.String[]);
                                    }
                                </option>
                                <option>-keepnames class file.engine.controller.Core</option>
                                <option>-keepattributes Exceptions,InnerClasses,Signature,Deprecated,
                                    SourceFile,LineNumberTable,*Annotation*,EnclosingMethod
                                </option>
                                <option>-keepclasseswithmembernames,includedescriptorclasses class * {
                                    native <![CDATA[<methods>]]>;
                                    }
                                </option>
                                <option>-keepclasseswithmembernames,includedescriptorclasses class
                                    file.engine.dllInterface.gpu.GPUAccelerator {
                                    public static void sendRestartOnError0();
                                    }
                                </option>
                                <option>-keepclassmembers,allowoptimization enum * {
                                    public static **[] values();
                                    public static ** valueOf(java.lang.String);
                                    }
                                </option>
                                <option>-keepclassmembers class * implements java.io.Serializable {
                                    static final long serialVersionUID;
                                    private static final java.io.ObjectStreamField[] serialPersistentFields;
                                    private void writeObject(java.io.ObjectOutputStream);
                                    private void readObject(java.io.ObjectInputStream);
                                    java.lang.Object writeReplace();
                                    java.lang.Object readResolve();
                                    }
                                </option>
                            </options>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>com.guardsquare</groupId>
                        <artifactId>proguard-base</artifactId>
                        <version>${tool.proguard.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.6.0</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <createDependencyReducedPom>false</createDependencyReducedPom>
                            <transformers>
                                <!-- 处理 ServiceLoader 文件合并 -->
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer"/>
                                <!-- 处理 META-INF/services 下的其他文件 -->
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                    <resource>META-INF/services/*</resource>
                                </transformer>
                                <!-- 处理 manifest 文件 -->
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <mainClass>file.engine.MainClass</mainClass>
                                    <manifestEntries>
                                        <Multi-Release>true</Multi-Release>
                                    </manifestEntries>
                                </transformer>
                            </transformers>
                            <filters>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.version>${project.version}</project.version>
        <maven.build.timestamp.format>yyyyMMddHHmmss</maven.build.timestamp.format>
        <project.build.version>${maven.build.timestamp}</project.build.version>
        <tool.proguard.version>7.5.0</tool.proguard.version>
        <antrun.encoding>UTF-8</antrun.encoding>
        <debug.file>src/main/java/file/engine/utils/system/properties/IsDebug.java</debug.file>
        <debug.file.name>IsDebug.java</debug.file.name>
        <lombok.version>1.18.38</lombok.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.13.1</version>
        </dependency>
        <dependency>
            <groupId>io.github.willena</groupId>
            <artifactId>sqlite-jdbc</artifactId>
            <version>3.50.1.0</version>
        </dependency>
        <dependency>
            <groupId>io.github.biezhi</groupId>
            <artifactId>TinyPinyin</artifactId>
            <version>2.0.3.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>2.0.17</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-reload4j</artifactId>
            <version>2.0.17</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>2.23.1</version>
        </dependency>
        <dependency>
            <groupId>io.javalin</groupId>
            <artifactId>javalin</artifactId>
            <version>6.6.0</version>
        </dependency>
        <dependency>
            <groupId>io.javalin.community.ssl</groupId>
            <artifactId>ssl-plugin</artifactId>
            <version>6.6.0</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk18on</artifactId>
            <version>1.78.1</version>
        </dependency>
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
            <version>5.17.0</version>
        </dependency>
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna-platform</artifactId>
            <version>5.17.0</version>
        </dependency>
        <!-- Lucene核心库 -->
        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-core</artifactId>
            <version>9.11.1</version>
        </dependency>

        <!-- Lucene解析库 -->
        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-queryparser</artifactId>
            <version>9.11.1</version>
        </dependency>

        <!-- Lucene附加的分析库 -->
        <dependency>
            <groupId>org.apache.lucene</groupId>
            <artifactId>lucene-analysis-common</artifactId>
            <version>9.11.1</version>
        </dependency>

        <dependency>
            <groupId>com.github.magese</groupId>
            <artifactId>ik-analyzer</artifactId>
            <version>8.5.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>5.3.0</version>
        </dependency>
        <dependency>
            <groupId>net.sourceforge.tess4j</groupId>
            <artifactId>tess4j</artifactId>
            <version>5.12.0</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>io.github.briqt</groupId>-->
        <!--            <artifactId>xunfei-spark4j</artifactId>-->
        <!--            <version>1.3.0</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>io.github.ollama4j</groupId>
            <artifactId>ollama4j</artifactId>
            <version>1.0.100</version>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>3.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>3.0.4</version>
        </dependency>
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>2.1.4</version>
        </dependency>
        <dependency>
            <groupId>io.gitlab.rxp90</groupId>
            <artifactId>jsymspell</artifactId>
            <version>1.0</version>
        </dependency>
    </dependencies>
</project>