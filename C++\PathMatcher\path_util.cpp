#include "framework.h"
#include "path_util.h"
#include <chrono>
#include <regex>
#include "str_convert.h"

int not_matched(const char parent_path[MAX_PATH_LENGTH],
	const char file_name[MAX_PATH_LENGTH],
	const bool is_ignore_case,
	const std::vector<std::string>& keywords,
	const std::vector<std::string>& keywords_lower_case,
	const int keywords_length,
	const std::vector<bool>* is_keyword_path,
	const bool enable_fuzzy_match)
{
	bool full_matched = false;
	bool has_matched_optional_keyword = false;  // 添加标志判断是否有可选关键词匹配成功
	int optional_keyword_count = 0;  // 统计可选关键字的数量

	// 先统计可选关键字数量
	for (const auto& each_keyword : keywords) {
		if (!each_keyword.empty() && each_keyword[0] == '?') {
			optional_keyword_count++;
		}
	}

	// 线程局部存储，避免重复分配
	thread_local std::string lowercase_buffer;

	for (int i = 0; i < keywords_length; ++i)
	{
		const bool is_keyword_path_val = is_keyword_path->at(i);

		// 使用string_view避免拷贝
		std::string_view match_str = is_keyword_path_val ?
			std::string_view(parent_path) : std::string_view(file_name);

		std::string_view each_keyword;
		if (is_ignore_case) [[likely]]
		{
			each_keyword = keywords_lower_case.at(i);
			// 只在需要时进行大小写转换
			lowercase_buffer.resize(match_str.size());
			std::ranges::transform(match_str,
				lowercase_buffer.begin(), ::tolower);
			match_str = std::string_view(lowercase_buffer);
		}
		else [[unlikely]]
		{
			each_keyword = keywords.at(i);
		}

		if (each_keyword.empty())
		{
			continue;
		}

		// 检查可选关键词
		const bool is_optional_keyword = each_keyword[0] == '?';
		if (is_optional_keyword)
		{
			each_keyword.remove_prefix(1);  // 移除?前缀
			if (match_str.find(each_keyword) != std::string_view::npos)
			{
				has_matched_optional_keyword = true;
				full_matched = true;
				continue;
			}
			// 尝试模糊匹配
			if (enable_fuzzy_match && pattern_char_match(each_keyword, match_str))
			{
				has_matched_optional_keyword = true;
				continue;
			}
			// 如果关键字长度大于4且包含中文，尝试拼音匹配
			if (is_str_contains_chinese(match_str.data()))
			{
				char gbk_buffer[MAX_PATH_LENGTH * 2]{ 0 };
				char* gbk_buffer_ptr = gbk_buffer;
				utf8_to_gbk(match_str.data(), static_cast<unsigned>(strlen(match_str.data())), &gbk_buffer_ptr, nullptr);
				char converted_pinyin[MAX_PATH_LENGTH * 6]{ 0 };
				char converted_pinyin_initials[MAX_PATH_LENGTH]{ 0 };
				convert_to_pinyin(gbk_buffer, converted_pinyin, MAX_PATH_LENGTH * 6,
					converted_pinyin_initials, MAX_PATH_LENGTH);

				// 尝试拼音直接匹配或拼音首字母匹配
				if (pattern_char_match(each_keyword, converted_pinyin) ||
					strstr(converted_pinyin_initials, each_keyword.data()) != nullptr)
				{
					has_matched_optional_keyword = true;
				}
			}
			// 如果是可选关键词但没匹配上，继续检查下一个关键词
			continue;
		}

		// 必要关键词匹配
		if (match_str.find(each_keyword) == std::string_view::npos)
		{
			if (is_keyword_path_val)
			{
				return -1;  // 路径关键词必须精确匹配
			}

			if (!enable_fuzzy_match || !pattern_char_match(each_keyword, match_str))
			{
				if (!is_str_contains_chinese(match_str.data()) || each_keyword.length() <= 4)
				{
					return -1;
				}
				char gbk_buffer[MAX_PATH_LENGTH * 2]{ 0 };
				char* gbk_buffer_ptr = gbk_buffer;
				utf8_to_gbk(match_str.data(), static_cast<unsigned>(strlen(match_str.data())), &gbk_buffer_ptr, nullptr);
				char converted_pinyin[MAX_PATH_LENGTH * 6]{ 0 };
				char converted_pinyin_initials[MAX_PATH_LENGTH]{ 0 };
				convert_to_pinyin(gbk_buffer, converted_pinyin, MAX_PATH_LENGTH * 6,
					converted_pinyin_initials, MAX_PATH_LENGTH);
				if (pattern_char_match(each_keyword, converted_pinyin))
				{
					full_matched = true;
				}
				else
				{
					if (strstr(converted_pinyin_initials, each_keyword.data()) == nullptr)
					{
						return -1;
					}
				}
			}
		}
		else
		{
			full_matched = true;
		}
	}

	// 修改返回逻辑，如果有可选关键字，则必须至少有一个匹配成功
	if (optional_keyword_count > 0) {
		// 如果一个都没匹配
		if (!has_matched_optional_keyword) {
			return -1;
		}
	}

	return full_matched ? 0 : 1;
}

bool pattern_char_match(const std::string_view& pattern, const std::string_view& str)
{
	int pattern_index = 0;
	int last_matched_index = -1;  // 上一个匹配的字符在str中的位置
	int str_index = 0;
	int continuous_match_length = 0;  // 当前连续匹配的长度
	int max_continuous_match = 0;     // 最大连续匹配长度

	const auto pattern_length = static_cast<int>(pattern.length());
	const auto str_length = static_cast<int>(str.length());
	const bool enable_distance_check = str_length > 35;  // 只有超长文件名才启用距离检查

	while (pattern_index < pattern_length && str_index < str_length)
	{
		const char current_char = str[str_index];

		if (current_char == ' ')
		{
			// 遇到空格时重置last_matched_index为空格的位置
			last_matched_index = str_index;
		}

		// 如果字符匹配，则移动pattern的指针
		if (pattern[pattern_index] == current_char)
		{
			// 只有在超长文件名时才检查距离，如果不是第一个字符，检查距离是否超过3个字符
			if (enable_distance_check && last_matched_index != -1 && str_index - last_matched_index > 4)  // 距离超过3个字符
			{
				return false;
			}

			// 检查是否连续匹配
			if (last_matched_index != -1 && str_index == last_matched_index + 1)
			{
				continuous_match_length++;
			}
			else
			{
				continuous_match_length = 1;  // 重新开始计算连续匹配
			}

			// 更新最大连续匹配长度
			max_continuous_match = max(max_continuous_match, continuous_match_length);

			last_matched_index = str_index;
			pattern_index++;
		}
		// 无论是否匹配，移动目标字符串的指针
		str_index++;
	}
	// 如果匹配完了整个pattern，并且至少有2个字符连续匹配，说明匹配成功
	return pattern_index == pattern_length && max_continuous_match >= 2;
}

bool fuzzy_match(const std::string& pattern, const std::string& str)
{
	if (str.length() < pattern.length())
	{
		return false;
	}
	size_t max_distance;
	if (pattern.length() > 9)
	{
		max_distance = 2;
	}
	else
	{
		max_distance = 1;
	}
	size_t start_pos = 0;
	while (start_pos <= str.length() - pattern.length())
	{
		if (const size_t distance = calc_str_distance(pattern, str.substr(start_pos, pattern.length()));
			distance <= max_distance)
		{
			return true;
		}
		++start_pos;
	}
	return false;
}

/**
 * 计算字符串相差的错误字符
 * @param pattern 匹配规则
 * @param str 待匹配字符串
 * @return str与pattern相差几个错误字符
 */
size_t calc_str_distance(const std::string& pattern, const std::string& str)
{
	const size_t m = pattern.length();
	const size_t n = str.length();
	if (m > n)
	{
		return 0xFFFFFFFF;
	}

	size_t** dp = new size_t * [m + 1];
	for (size_t i = 0; i <= m; i++) {
		dp[i] = new size_t[n + 1];
	}

	for (size_t i = 0; i <= m; i++)
	{
		for (size_t j = 0; j <= n; j++)
		{
			if (i == 0)
			{
				dp[i][j] = j; // 将pattern转换为空字符串的步骤数
			}
			else if (j == 0)
			{
				dp[i][j] = i; // 将str转换为空字符串的步骤数
			}
			else if (pattern[i - 1] == str[j - 1])
			{
				dp[i][j] = dp[i - 1][j - 1]; // 如果字符相同，不需要额外步骤
			}
			else
			{
				dp[i][j] = 1 + min(min(dp[i - 1][j],
					dp[i][j - 1]),
					dp[i - 1][j - 1]);
			}
		}
	}
	const size_t ret = dp[m][n];
	for (size_t i = 0; i <= m; i++) {
		delete[] dp[i];
	}
	delete[] dp;
	return ret;
}

void str_add_single(char* dst, const char c, size_t* current_len, const size_t max_size)
{
	if (*current_len < max_size - 1)
	{
		// 保留最后一位给'\0'
		dst[*current_len] = c;
		(*current_len)++;
		dst[*current_len] = '\0';
	}
}

void convert_to_pinyin(const char* chinese_str, char* output_str,
	const size_t output_size, char* pinyin_initials, const size_t pinyin_init_size)
{
	const auto length = strlen(chinese_str);
	size_t out_len = 0;       // 记录output_str当前长度
	size_t initials_len = 0; // 记录pinyin_initials当前长度

	for (size_t j = 0; j < length;)
	{
		const unsigned char val = chinese_str[j];
		if (val < 128)
		{
			str_add_single(output_str, chinese_str[j], &out_len, output_size);
			str_add_single(pinyin_initials, chinese_str[j], &initials_len, pinyin_init_size);
			++j;
			continue;
		}

		// 检查剩余长度是否足够处理双字节字符
		if (j + 1 >= length) break;

		if (const int chrasc = chinese_str[j] * 256 + chinese_str[j + 1] + 256; chrasc > 0 && chrasc < 160)
		{
			str_add_single(output_str, chinese_str[j], &out_len, output_size);
			str_add_single(pinyin_initials, chinese_str[j], &initials_len, pinyin_init_size);
			++j;
		}
		else
		{
			for (int i = sizeof spell_value / sizeof spell_value[0] - 1; i >= 0; --i)
			{
				if (spell_value[i] <= chrasc)
				{
					// 一次性追加整个拼音字符串
					const char* pinyin = spell_dict[i];
					const size_t pinyin_len = strlen(pinyin);

					// 追加到output_str
					if (out_len + pinyin_len < output_size - 1)
					{
						strcpy_s(output_str + out_len, output_size - out_len, pinyin);
						out_len += pinyin_len;
					}

					// 追加首字母到initials
					if (initials_len < pinyin_init_size - 1)
					{
						pinyin_initials[initials_len++] = pinyin[0];
						pinyin_initials[initials_len] = '\0';
					}
					break;
				}
			}
			j += 2;
		}
	}
}

bool is_str_contains_chinese(const char* source)
{
	int i = 0;
	while (source[i] != 0)
	{
		if (source[i] & 0x80 && source[i] & 0x40 && source[i] & 0x20)
		{
			return true;
		}
		if (source[i] & 0x80 && source[i] & 0x40)
		{
			i += 2;
		}
		else
		{
			i += 1;
		}
	}
	return false;
}

std::wstring string2wstring(const std::string& str)
{
	std::wstring result;
	const int len = MultiByteToWideChar(CP_UTF8, 0, str.c_str(), static_cast<int>(str.size()), nullptr, 0);
	const auto buffer = new TCHAR[len + 1];
	MultiByteToWideChar(CP_UTF8, 0, str.c_str(), static_cast<int>(str.size()), buffer, len);
	buffer[len] = '\0';
	result.append(buffer);
	delete[] buffer;
	return result;
}

inline bool is_file_exist(const std::string& path)
{
	return GetFileAttributes(string2wstring(path).c_str()) != INVALID_FILE_ATTRIBUTES;
}

int is_dir_or_file(const char* path)
{
	const auto w_path = string2wstring(path);
	const DWORD dwAttrib = GetFileAttributes(w_path.c_str());
	if (dwAttrib != INVALID_FILE_ATTRIBUTES)
	{
		if (dwAttrib & FILE_ATTRIBUTE_DIRECTORY)
		{
			return 0;
		}
		return 1;
	}
	return -1;
}

int match_func(const char parent_path_arr[MAX_PATH_LENGTH],
	const char file_name_arr[MAX_PATH_LENGTH],
	const search_info* info)
{
	const std::string_view parent_path(parent_path_arr);
	const std::string_view file_name(file_name_arr);
	int match_type;
	if (parent_path.empty() || file_name.empty())
	{
		return -1;
	}
	const auto search_case = info->get_search_case_num();
	if (search_case & 1 << 3) [[unlikely]]
	{
		// regex
		const std::regex& re = info->re;
		const auto& path = std::string(parent_path) + file_name_arr;
		if (auto&& search_res = std::regex_search(path, re); !search_res)
		{
			return -1;
		}
		else
		{
			match_type = 0;
		}
	}
	else [[likely]]
	{
		match_type = not_matched(parent_path_arr, file_name_arr, info->is_ignore_search_case(), *info->get_keywords(),
			*info->get_keywords_lower_case(),
			static_cast<int>(info->get_keywords()->size()),
			info->is_keyword_path_value(),
			info->is_enable_fuzzy_match_value());
		if (match_type == -1)
		{
			return -1;
		}
	}
	if (search_case == 0)
	{
		return match_type;
	}
	short is_all_case_matched = 0;
	if (search_case & 1)
	{
		// file
		const auto& path = std::string(parent_path) + file_name_arr;
		if (is_dir_or_file(path.c_str()) == 1)
		{
			is_all_case_matched |= 1;
		}
	}
	else
	{
		is_all_case_matched |= 1;
	}
	if (is_all_case_matched != 1)
	{
		return -1;
	}
	if (search_case & 1 << 1)
	{
		// dir
		const auto& path = std::string(parent_path) + file_name_arr;
		if (is_dir_or_file(path.c_str()) == 0)
		{
			is_all_case_matched |= 2;
		}
	}
	else
	{
		is_all_case_matched |= 2;
	}
	if (is_all_case_matched != 3)
	{
		return -1;
	}
	if (search_case & 1 << 2)
	{
		// full
		char search_text_arr[MAX_PATH_LENGTH]{};
		strcpy_s(search_text_arr, info->get_search_text().c_str());
		char file_name_str[MAX_PATH_LENGTH]{};
		strcpy_s(file_name_str, file_name_arr);

		_strlwr_s(search_text_arr);
		_strlwr_s(file_name_str);
		if (strcmp(search_text_arr, file_name_str) == 0)
		{
			is_all_case_matched |= 4;
		}
	}
	else
	{
		is_all_case_matched |= 4;
	}
	return is_all_case_matched == 7 ? match_type : -1;
}

std::chrono::system_clock::time_point decode_file_time(const uint64_t timestamp)
{
	// FILETIME timestamp starts from January 1, 1601

	constexpr int year = 1601;
	constexpr int month = 1;
	constexpr int day = 1;

	constexpr std::chrono::sys_days timestamp1601 = std::chrono::year{ year } / month / day;
	constexpr auto file_time_base = std::chrono::time_point(timestamp1601);

	const auto duration_since_file_time_base = std::chrono::microseconds(timestamp / 10);

	return file_time_base + duration_since_file_time_base;
}
