package file.engine.utils;

import file.engine.configs.Constants;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
public enum ThreadPoolUtil {
    INSTANCE;
    @Getter
    private final ExecutorService cachedThreadPool;
    @Getter
    private final ExecutorService virtualThreadPool;
    private final AtomicBoolean isShutdown = new AtomicBoolean(false);

    ThreadPoolUtil() {
        cachedThreadPool = new ThreadPoolExecutor(
                0,
                1000,
                60,
                TimeUnit.SECONDS,
                new SynchronousQueue<>(),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
        virtualThreadPool = Executors.newVirtualThreadPerTaskExecutor();
    }

    public static ThreadPoolUtil getInstance() {
        return INSTANCE;
    }

    public boolean isShutdown() {
        return isShutdown.get();
    }

    private <T> Future<T> executeTaskPlatform(Callable<T> task) {
        return cachedThreadPool.submit(wrapTask(task));
    }

    private <T> Future<T> executeTaskVirtual(Callable<T> task) {
        return virtualThreadPool.submit(wrapTask(task));
    }

    private <T> Callable<T> wrapTask(Callable<T> task) {
        return () -> {
            try {
                return task.call();
            } catch (Exception e) {
                log.error("error {}", e.getMessage(), e);
                throw new RuntimeException(e);
            }
        };
    }

    private Runnable wrapTask(Runnable task) {
        return () -> {
            try {
                task.run();
            } catch (Exception e) {
                log.error("error {}", e.getMessage(), e);
                throw new RuntimeException(e);
            }
        };
    }

    private void executeTaskPlatform(Runnable task) {
        cachedThreadPool.submit(wrapTask(task));
    }

    private void executeTaskVirtual(Runnable task) {
        virtualThreadPool.submit(wrapTask(task));
    }

    /**
     * 提交任务
     *
     * @param task            任务
     * @param isVirtualThread 是否使用虚拟线程
     * @return Future
     */
    public <T> Future<T> executeTask(Callable<T> task, boolean isVirtualThread) {
        if (isShutdown.get()) {
            return null;
        }
        if (isVirtualThread) {
            return executeTaskVirtual(task);
        } else {
            return executeTaskPlatform(task);
        }
    }

    public <T> Future<T> executeTask(Callable<T> task) {
        return executeTask(task, true);
    }

    /**
     * 提交任务
     *
     * @param task            任务
     * @param isVirtualThread 是否使用虚拟线程
     */
    public void executeTask(Runnable task, boolean isVirtualThread) {
        if (isShutdown.get()) {
            return;
        }
        if (isVirtualThread) {
            executeTaskVirtual(task);
        } else {
            executeTaskPlatform(task);
        }
    }

    /**
     * 使用虚拟线程提交任务
     *
     * @param task 任务
     */
    public void executeTask(Runnable task) {
        if (isShutdown.get()) {
            return;
        }
        executeTask(task, true);
    }

    /**
     * 关闭线程池病等待
     */
    public void shutdown() {
        isShutdown.set(true);
        cachedThreadPool.shutdownNow();
        virtualThreadPool.shutdownNow();
        printInfo((ThreadPoolExecutor) cachedThreadPool);
    }

    /**
     * 等待线程池关闭并打印线程池信息
     *
     * @param threadPoolExecutor 线程池
     */
    private void printInfo(ThreadPoolExecutor threadPoolExecutor) {
        try {
            if (!threadPoolExecutor.awaitTermination(Constants.THREAD_POOL_AWAIT_TIMEOUT, TimeUnit.SECONDS)) {
                System.err.println("线程池等待超时");
                int queueSize = threadPoolExecutor.getQueue().size();
                System.err.println("当前排队线程数：" + queueSize);

                int activeCount = threadPoolExecutor.getActiveCount();
                System.err.println("当前活动线程数：" + activeCount);

                long completedTaskCount = threadPoolExecutor.getCompletedTaskCount();
                System.err.println("执行完成线程数：" + completedTaskCount);

                long taskCount = threadPoolExecutor.getTaskCount();
                System.err.println("总线程数：" + taskCount);
            }
        } catch (InterruptedException e) {
            log.error("error: {}", e.getMessage(), e);
        }
    }
}
