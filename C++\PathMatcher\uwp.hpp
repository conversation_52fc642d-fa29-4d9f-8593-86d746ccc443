﻿#pragma once
#include <string>
#include <vector>

using UWP_STRUCT = struct _UWP_STRUCT
{
	std::wstring DisplayName;
	std::wstring Name;
	std::wstring Version;
	int Architecture = 0;
	std::wstring ResourceId;
	std::wstring Publisher;
	std::wstring PublisherId;
	std::wstring FullName;
	std::wstring FamilyName;
	std::wstring InstallLocation;
	std::wstring AppUserModelId;
};

// 主要接口函数 - 基于超时的缓存机制
std::vector<UWP_STRUCT> get_all_uwp();

// 缓存管理函数
void cleanup_uwp_cache();        // 清理缓存数据
void refresh_uwp_cache();        // 强制刷新缓存
