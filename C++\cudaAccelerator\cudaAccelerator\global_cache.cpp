﻿#include "framework.h"
#include "global_cache.h"
#include "kernels.cuh"
#include "constans.h"
#include <shared_mutex>
#include <set>

static global_cache::global_cache_data global_cache_obj;
static std::shared_mutex shared_mutex_;
static std::hash<std::string> str_hasher;

//插入一个缓存
void global_cache::insert(const std::vector<std::string>& str_vec)
{
	std::unique_lock wck(shared_mutex_);
	cudaStream_t stream;
	gpuErrchk(cudaStreamCreate(&stream), true, nullptr);
	size_t i = 0;
	std::vector<std::string> remain_str_to_insert;
	size_t remain_str_total_bytes = 0;
	for (const auto& str : str_vec) 
	{
		const auto str_length = str.length();
		if (global_cache_obj.blank_start_address + str_length + 1 >= 
			reinterpret_cast<size_t>(global_cache_obj.dev_cache) + global_cache_obj.total_bytes)
		{
			remain_str_total_bytes += (str_length + 1);
			remain_str_to_insert.emplace_back(str);
		}
		else
		{
			const auto str_hash = str_hasher(str);
			if (global_cache_obj.unique_hash_index_map.find(str_hash) ==
				global_cache_obj.unique_hash_index_map.end())
			{
				global_cache_obj.unique_hash_index_map.insert(std::make_pair(str_hash, i));
				gpuErrchk(cudaMemcpyAsync(reinterpret_cast<void*>(global_cache_obj.blank_start_address),
					str.c_str(), str_length, cudaMemcpyHostToDevice, stream), true, nullptr);
				global_cache_obj.dev_str_len_vec.emplace_back(static_cast<unsigned short>(str_length));
				global_cache_obj.dev_str_start_address.emplace_back(global_cache_obj.blank_start_address);
				global_cache_obj.blank_start_address += (str_length + 1);
				++i;
			}
		}
	}
	gpuErrchk(cudaStreamSynchronize(stream), true, nullptr);
	gpuErrchk(cudaStreamDestroy(stream), true, nullptr);
	if (!remain_str_to_insert.empty())
	{
		const auto new_total_bytes = global_cache_obj.total_bytes + remain_str_total_bytes +
			CACHE_REMAIN_BLANK_SIZE_IN_BYTES;
		char* new_ptr = nullptr;
		gpuErrchk(cudaMalloc(&new_ptr, new_total_bytes), true, nullptr);
		gpuErrchk(cudaMemcpy(new_ptr, global_cache_obj.dev_cache, global_cache_obj.total_bytes, cudaMemcpyDeviceToDevice),
			true, nullptr);
		size_t new_start_address = reinterpret_cast<size_t>(new_ptr);
		std::vector<size_t> new_start_address_vec;
		for (size_t each_old_obj = 0;
			each_old_obj < global_cache_obj.dev_str_start_address.size(); 
			++each_old_obj)
		{
			const auto old_str_len = global_cache_obj.dev_str_len_vec[each_old_obj];
			new_start_address_vec.emplace_back(new_start_address);
			new_start_address += (static_cast<size_t>(old_str_len) + 1);
		}
		cudaFree(global_cache_obj.dev_cache);
		global_cache_obj.dev_cache = new_ptr;
		global_cache_obj.dev_str_start_address = new_start_address_vec;
		global_cache_obj.total_bytes = new_total_bytes;
		global_cache_obj.blank_start_address = new_start_address;

		cudaStream_t stream2;
		gpuErrchk(cudaStreamCreate(&stream2), true, nullptr);
		for (const auto& remain_str : remain_str_to_insert)
		{
			const auto remain_str_hash = str_hasher(remain_str);
			if (global_cache_obj.unique_hash_index_map.find(remain_str_hash) ==
				global_cache_obj.unique_hash_index_map.end())
			{
				const auto remain_str_len = remain_str.length();
				global_cache_obj.unique_hash_index_map.insert(std::make_pair(remain_str_hash, i));
				gpuErrchk(cudaMemcpyAsync(reinterpret_cast<void*>(global_cache_obj.blank_start_address),
					remain_str.c_str(), remain_str_len, cudaMemcpyHostToDevice, stream2), true, nullptr);
				global_cache_obj.dev_str_len_vec.emplace_back(static_cast<unsigned short>(remain_str_len));
				global_cache_obj.dev_str_start_address.emplace_back(global_cache_obj.blank_start_address);
				global_cache_obj.blank_start_address += (remain_str_len + 1);
				++i;
			}
		}
		gpuErrchk(cudaStreamSynchronize(stream2), true, nullptr);
		gpuErrchk(cudaStreamDestroy(stream2), true, nullptr);
	}
}

//删除一个缓存
void global_cache::remove(const std::vector<std::string>& str_vec)
{
	if (global_cache_obj.dev_str_start_address.empty())
	{
		return;
	}

	size_t total_bytes = 0;
	std::vector<std::string> remain_strs;

	{
		std::shared_lock lck(shared_mutex_);
		cudaStream_t stream;
		gpuErrchk(cudaStreamCreate(&stream), true, nullptr);

		std::set<size_t> str_index_to_remove;
		for (const auto& each_str_to_remove : str_vec)
		{
			const auto str_hash = str_hasher(each_str_to_remove);
			const auto hash_id_iter = global_cache_obj.unique_hash_index_map.find(str_hash);
			if (hash_id_iter != global_cache_obj.unique_hash_index_map.end())
			{
				str_index_to_remove.insert(hash_id_iter->second);
			}
		}

		const auto buffer_size = global_cache_obj.dev_str_start_address.size() * MAX_PATH_LENGTH;
		char* tmp_str_buffer = new char[buffer_size];
		memset(tmp_str_buffer, 0, buffer_size);

		size_t i = 0;
		for (const auto& each_dev_str_address : global_cache_obj.dev_str_start_address)
		{
			char* tmp = tmp_str_buffer + i * MAX_PATH_LENGTH;
			if (std::find(str_index_to_remove.begin(), str_index_to_remove.end(), i) ==
				str_index_to_remove.end())
			{
				const size_t each_str_len = global_cache_obj.dev_str_len_vec[i];
				//拷贝GPU中的字符串到tmp
				gpuErrchk(cudaMemcpyAsync(tmp,
					reinterpret_cast<void*>(each_dev_str_address),
					each_str_len,
					cudaMemcpyDeviceToHost,
					stream),
					true,
					nullptr);

				total_bytes += (each_str_len + 1);
				remain_strs.emplace_back(tmp);
			}
			++i;
		}
		gpuErrchk(cudaStreamSynchronize(stream), true, nullptr);
		gpuErrchk(cudaStreamDestroy(stream), true, nullptr);
		delete[] tmp_str_buffer;
	}

	init(total_bytes + CACHE_REMAIN_BLANK_SIZE_IN_BYTES);
	insert(remain_strs);
}

void global_cache::init(const size_t init_bytes)
{
	std::unique_lock wck(shared_mutex_);
	gpuErrchk(cudaFree(global_cache_obj.dev_cache), false, nullptr);
	global_cache_obj.dev_str_len_vec.clear();
	global_cache_obj.dev_str_start_address.clear();
	global_cache_obj.unique_hash_index_map.clear();
	gpuErrchk(cudaMalloc(&(global_cache_obj.dev_cache), init_bytes), true, nullptr);
	gpuErrchk(cudaMemset(global_cache_obj.dev_cache, 0, init_bytes), true, nullptr);
	global_cache_obj.total_bytes = init_bytes;
	global_cache_obj.blank_start_address = reinterpret_cast<size_t>(global_cache_obj.dev_cache);
}

size_t global_cache::get_str_hash(const std::string& str)
{
	return str_hasher(str);
}

//通过字符串hash对应缓存字符串内存首地址
size_t global_cache::get_address_by_hash(const size_t hash)
{
	std::shared_lock lck(shared_mutex_);
	const auto iter = global_cache_obj.unique_hash_index_map.find(hash);
	if (iter ==
		global_cache_obj.unique_hash_index_map.end())
	{
		return 0;
	}
	return global_cache_obj.dev_str_start_address[iter->second];
}

size_t global_cache::size()
{
	std::shared_lock lck(shared_mutex_);
	return global_cache_obj.dev_str_start_address.size();
}

std::string global_cache::copy_to_host_by_hash(const size_t hash)
{
	std::shared_lock lck(shared_mutex_);
	char tmp[MAX_PATH_LENGTH]{ 0 };
	const auto addr = get_address_by_hash(hash);
	if (addr == 0)
	{
		return tmp;
	}
	const auto index = global_cache_obj.unique_hash_index_map.at(hash);
	const size_t str_length = global_cache_obj.dev_str_len_vec[index];
	//拷贝GPU中的字符串到tmp
	gpuErrchk(cudaMemcpy(tmp,
		reinterpret_cast<void*>(addr),
		str_length,
		cudaMemcpyDeviceToHost),
		true,
		nullptr);
	return tmp;
}

bool global_cache::get_file_name(const char* path, char* output)
{
	const char* p = strrchr(path, '\\');
	if (p == nullptr)
	{
		return false;
	}
	strcpy_s(output, MAX_PATH_LENGTH, p + 1);
	return true;
}

bool global_cache::get_parent_path(const char* path, char* output)
{
	strcpy_s(output, MAX_PATH_LENGTH, path);
	char* p = strrchr(output, '\\');
	if (p == nullptr)
	{
		return false;
	}
	//保留 \ 字符
	*(p + 1) = '\0';
	return true;
}

void global_cache::clear()
{
	std::shared_lock lck(shared_mutex_);
	init(1);
}