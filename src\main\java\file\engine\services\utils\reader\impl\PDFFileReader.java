package file.engine.services.utils.reader.impl;

import file.engine.services.utils.reader.ContentReader;

import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.text.PDFTextStripper;

import java.io.File;
import java.io.IOException;

@Slf4j
public class PDFFileReader implements ContentReader {
    @Override
    public String readContent(String fileAbsPath) {
        try (var doc = Loader.loadPDF(new File(fileAbsPath))) {
            PDFTextStripper pdfTextStripper = new PDFTextStripper();
            return pdfTextStripper.getText(doc);
        } catch (IOException e) {
            log.error("error {}", e.getMessage(), e);
            return "";
        }
    }
}
