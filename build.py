import hashlib
import os
import shutil
import subprocess
import sys
import zipfile
import platform
import winreg
import argparse

MULTI_RELEASE_VERSION = 21

# 注意必须在末尾加上 \\ 
# 否则可能勿删文件夹
rulesToDelete = set([
    'module-info.class',
    'com\\sun\\jna\\aix-ppc\\',
    'com\\sun\\jna\\darwin\\',
    'com\\sun\\jna\\freebsd\\',
    'com\\sun\\jna\\linux\\',
    'com\\sun\\jna\\openbsd\\',
    'com\\sun\\jna\\sunos\\',
    'com\\sun\\jna\\win32-aarch64\\',
    'com\\sun\\jna\\win32-x86\\',
    'com\\sun\\jna\\platform\\linux\\',
    'com\\sun\\jna\\platform\\mac\\',
    'com\\sun\\jna\\platform\\unix\\',
    'org\\sqlite\\native\\',
    'oshi\\driver\\linux\\',
    'oshi\\driver\\mac\\',
    'oshi\\driver\\unix\\',
    'oshi\\hardware\\platform\\linux\\',
    'oshi\\hardware\\platform\\mac\\',
    'oshi\\hardware\\platform\\unix\\',
    'oshi\\jna\\platform\\linux\\',
    'oshi\\jna\\platform\\mac\\',
    'oshi\\jna\\platform\\unix\\',
    'oshi\\software\\os\\linux\\',
    'oshi\\software\\os\\mac\\',
    'oshi\\software\\os\\unix\\',
    'oshi\\util\\platform\\linux\\',
    'oshi\\util\\platform\\mac\\',
    'oshi\\util\\platform\\unix\\',
    'win32-x86\\'
])

def unzipFile(zip_src, dst_dir):  # 解压函数，将zip_src解压到dst_dir
    r = zipfile.is_zipfile(zip_src)
    if r:
        fz = zipfile.ZipFile(zip_src, 'r')
        for file in fz.namelist():
            fz.extract(file, dst_dir)
    else:
        print('This is not zip......')


def delFileInZip():
    print("UnZip:" + 'File-Engine-Core.jar')
    pathName = 'File-Engine-Core'
    if os.path.exists(pathName):
        shutil.rmtree(pathName)
    unzipFile('File-Engine-Core.jar', pathName)
    for root, _, files in os.walk(pathName):  # 遍历pathName文件夹
        for f in files:
            fileName = os.path.join(root, f)
            for deleteRule in rulesToDelete:
                if fileName.find(deleteRule) != -1 and os.path.exists(fileName):
                    print('delete: ' + fileName)
                    os.remove(fileName)
    os.remove('File-Engine-Core.jar')
    delDir(pathName)
    shutil.make_archive(pathName, 'zip', pathName)  # 压缩
    shutil.rmtree(pathName)  # 删除临时文件
    os.rename('File-Engine-Core.zip', 'File-Engine-Core.jar')
    print('=======Finish!======')


def delDir(path):
    """
    清理空文件夹和空文件
    param path: 文件路径，检查此文件路径下的子文件
    """
    try:
        files = os.listdir(path)  # 获取路径下的子文件(夹)列表
        for file in files:
            if os.path.isdir(os.fspath(path+'/'+file)):  # 如果是文件夹
                if not os.listdir(os.fspath(path+'/'+file)):  # 如果子文件为空
                    os.rmdir(os.fspath(path+'/'+file))  # 删除这个空文件夹
                else:
                    delDir(os.fspath(path+'/'+file))  # 遍历子文件
                    if not os.listdir(os.fspath(path+'/'+file)):  # 如果子文件为空
                        os.rmdir(os.fspath(path+'/'+file))  # 删除这个空文件夹
            elif os.path.isfile(os.fspath(path+'/'+file)):  # 如果是文件
                if os.path.getsize(os.fspath(path+'/'+file)) == 0:  # 文件大小为0
                    os.remove(os.fspath(path+'/'+file))  # 删除这个文件
        return
    except FileNotFoundError:
        print(path + "文件夹清空失败")


def getFileMd5(file_name):
    """
    计算文件的md5
    :param file_name:
    :return:
    """
    m = hashlib.md5()  # 创建md5对象
    with open(file_name, 'rb') as fobj:
        while True:
            data = fobj.read(4096)
            if not data:
                break
            m.update(data)  # 更新md5对象

    return m.hexdigest()  # 返回md5对象


def get_java_path():
    if platform.system() == "Windows":
        try:
            registry_key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, "SOFTWARE\\JavaSoft\\Java Development Kit")
            i = 0
            while True:
                sub_key_name = winreg.EnumKey(registry_key, i)
                sub_key = winreg.OpenKey(registry_key, sub_key_name)
                java_home = winreg.QueryValueEx(sub_key, "JavaHome")[0]
                if os.path.exists(java_home):
                    return java_home
                i += 1
        except WindowsError:
            return None
    return None

def compile_cpp_deps(sln_path, sln_file_name, compiled_file_relative_name):
    import vswhere
    # 编译启动器
    vs_path_list = vswhere.find(
        latest=True, requires='Microsoft.Component.MSBuild', find=r'MSBuild\**\Bin\MSBuild.exe')

    if not vs_path_list:
        raise RuntimeError("Cannot find visual studio installation or MSBuild.exe")
    
    sln_path = os.path.abspath(sln_path)
    
    vs_path = vs_path_list[0]
    vs_path = vs_path[0:1] + '\"' + vs_path[1:] + "\""
    os.system(vs_path + ' ' + sln_path + '\\' + sln_file_name + ' /p:Configuration=Release')
    
    compiled_file_path = os.path.join(sln_path, compiled_file_relative_name)

    if not os.path.exists(compiled_file_path):
        raise RuntimeError("Compile failed.")
    shutil.copy(compiled_file_path, './src/main/resources/win32-native/')


if __name__ == '__main__':
    buildDir = 'build'
    if not os.path.exists(buildDir):
        os.mkdir(buildDir)

    jdkPath: str
    additionalModule = []
    
    parser = argparse.ArgumentParser(description='Build the project')
    parser.add_argument('--jdk-home', type=str, help='The path of JDK')
    parser.add_argument('--additional-module', nargs='*', help='The additional module to build')
    parser.add_argument('--compile-native', action='store_true', help='Compile the native code')

    args = parser.parse_args()
    jdkPath = args.jdk_home

    if args.additional_module is not None:    
        for module in args.additional_module:
            print("additional module added: " + module)
            additionalModule.append(module)

    isCompileNative = args.compile_native

    if jdkPath is None:
        jdkPath = get_java_path()
        if (jdkPath is None):
            raise EnvironmentError('未找到JAVA_HOME')
    print("JAVA_HOME is set to " + jdkPath)
    
    if isCompileNative:
        print('Compiling native code...')
        compile_cpp_deps('./C++/cudaAccelerator', 'cudaAccelerator.sln', 'x64/Release/cudaAccelerator.dll')
        compile_cpp_deps('./C++/fileMonitor', 'fileMonitor.sln', 'x64/Release/fileMonitor.dll')
        compile_cpp_deps('./C++/fileSearcherUSN', 'fileSearcherUSN.sln', 'x64/Release/fileSearcherUSN.exe')
        compile_cpp_deps('./C++/getWindowsKnownFolder', 'getWindowsKnownFolder.sln', 'x64/Release/getWindowsKnownFolder.dll')
        compile_cpp_deps('./C++/isLocalDisk', 'isLocalDisk.sln', 'x64/Release/isLocalDisk.dll')
        compile_cpp_deps('./C++/openclAccelerator', 'openclAccelerator.sln', 'bin/openclAccelerator.dll')
        compile_cpp_deps('./C++/parentProcessCheck', 'parentProcessCheck.sln', 'x64/Release/parentProcessCheck.dll')
        compile_cpp_deps('./C++/PathMatcher', 'PathMatcher.sln', 'x64/Release/pathMatcher.dll')
        compile_cpp_deps('./C++/sqliteJDBC', 'sqliteJDBC.sln', 'x64/Release/sqliteJDBC.dll')
        compile_cpp_deps('./C++/windowCheck', 'windowCheck.sln', 'x64/Release/windowCheck.dll')

    # 编译jar
    if os.system('set JAVA_HOME=' + jdkPath + '&& mvn clean compile package') != 0:
        print('maven compile failed')
        exit()

    # 切换到build
    os.chdir(buildDir)

    if os.system(r'xcopy ..\target\File-Engine-Core.jar . /Y') != 0:
        print('xcopy File-Engine-Core.jar failed.')
        exit()

    # 精简jar
    delFileInZip()

    # 生成jre
    binPath = os.path.join(jdkPath, 'bin')
    jdepExe = os.path.join(binPath, 'jdeps.exe')
    try:
        deps = subprocess.check_output([jdepExe, '--multi-release', str(MULTI_RELEASE_VERSION), '--ignore-missing-deps', '--print-module-deps', os.path.abspath('File-Engine-Core.jar')])
    except subprocess.CalledProcessError as grepexc:                                                                                                   
        print("error code", grepexc.returncode, grepexc.output)
        exit(-1)
    depsStr = deps.decode().strip()
    modulesFromJar = depsStr.split(',')

    moduleList = []
    for eachModule in modulesFromJar:
        moduleList.append(eachModule)
    #将additionalModule添加到moduleList
    for eachModule in additionalModule:
        moduleList.append(eachModule.strip())
    moduleList = set(moduleList)

    print("deps: " + str(moduleList))
    depsStr = ','.join(moduleList)
    #判断jre文件夹是否存在
    if os.path.exists('jre'):
        shutil.rmtree('jre')
    jlinkExe = os.path.join(binPath, 'jlink.exe')
    jlinkExe = jlinkExe[0:1] + '\"' + jlinkExe[1:] + '\"'
    if os.system(jlinkExe + r' --no-header-files --no-man-pages --module-path jmods --add-modules ' + depsStr + ' --output jre') != 0:
        print('Generate jre failed.')
        exit()
