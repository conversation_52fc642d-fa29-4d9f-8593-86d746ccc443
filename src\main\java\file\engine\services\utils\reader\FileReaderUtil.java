package file.engine.services.utils.reader;

import file.engine.configs.Constants;
import file.engine.services.utils.reader.impl.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class FileReaderUtil {
    private static final ConcurrentHashMap<Constants.Enums.CONTENT_READER_TYPE, ContentReader> contentReaderMap = new ConcurrentHashMap<>();

    static {
        for (Constants.Enums.CONTENT_READER_TYPE contentReaderType : Constants.Enums.CONTENT_READER_TYPE.values()) {
            ContentReader reader;
            switch (contentReaderType) {
                case TEXT -> reader = new TextFileReader();
                case EXCEL -> reader = new ExcelFileReader();
                case WORD -> reader = new WordFileReader();
                case PDF -> reader = new PDFFileReader();
                case PICTURE -> reader = new PictureReader();
                default -> reader = null;
            }
            if (reader != null) {
                contentReaderMap.put(contentReaderType, reader);
            }
        }
    }

    @SneakyThrows
    public static String readFile(String filePath) {
        File f = new File(filePath);
        if (!f.isFile()) {
            return "";
        }
        Path path = Path.of(filePath);
        long size = Files.size(path);
        if (size > Constants.MAX_SINGLE_FILE_SIZE_LUCENE) {
            return "";
        }

        Constants.Enums.CONTENT_READER_TYPE type;
        String fileName = f.getName();
        if (fileName.endsWith(".doc") || fileName.endsWith(".docx")) {
            type = Constants.Enums.CONTENT_READER_TYPE.WORD;
        } else if (fileName.endsWith("xlsx") || fileName.endsWith("xls")) {
            type = Constants.Enums.CONTENT_READER_TYPE.EXCEL;
        } else if (fileName.endsWith("pdf")) {
            type = Constants.Enums.CONTENT_READER_TYPE.PDF;
        } else if (isImage(f)) {
            type = Constants.Enums.CONTENT_READER_TYPE.PICTURE;
        } else {
            type = Constants.Enums.CONTENT_READER_TYPE.TEXT;
        }
        ContentReader contentReader = contentReaderMap.get(type);
        if (contentReader != null) {
            return contentReader.readContent(filePath);
        }
        return "";
    }

    private static boolean isImage(File file) {
        try {
            Image image = ImageIO.read(file);
            var fileName = file.getName().toLowerCase();
            return image != null && fileName.endsWith(".png") &&
                   fileName.endsWith(".jpg") &&
                   fileName.endsWith(".jpeg");
        } catch (IOException ex) {
            return false;
        }
    }
}
