package file.engine.entity;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.attribute.FileTime;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.Optional;

public class SearchResult {
    private String path;
    private String modifyDate;
    private long fileSize;
    private boolean contentMatch;
    private String highlightPath;
    private String highlightFileName;
    private transient boolean fuzzyMatched;
    private transient boolean prioritizeResult;

    public SearchResult(String path,
                        LocalDateTime modifyTime,
                        long fileSize,
                        boolean contentMatch,
                        boolean fuzzyMatched,
                        boolean prioritizeResult) {
        this.path = path;
        this.modifyDate = modifyTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        if (fileSize != 0) {
            this.fileSize = fileSize;
        } else {
            getFileSizeByPath(path);
        }
        this.contentMatch = contentMatch;
        this.fuzzyMatched = fuzzyMatched;
        this.prioritizeResult = prioritizeResult;
    }

    private void getFileSizeByPath(String path) {
        try {
            if (Files.isDirectory(Path.of(path))) {
                this.fileSize = 0;
            } else {
                this.fileSize = Files.size(Path.of(path));
            }
        } catch (Exception e) {
            this.fileSize = 0;
        }
    }

    /**
     * DO NOT REMOVE
     * 用于PathMatcher生成SearchResult对象
     *
     * @param path            path
     * @param modifyDateMills 最后修改时间戳
     * @param fileSize        文件大小
     */
    @SuppressWarnings("unused")
    public SearchResult(String path, long modifyDateMills, long fileSize, boolean fuzzyMatched) {
        this.path = path;
        if (modifyDateMills != -11644473600L && modifyDateMills != 0) {
            this.modifyDate = LocalDateTime.ofEpochSecond(modifyDateMills, 0, ZoneOffset.UTC).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        } else {
            try {
                FileTime lastModifiedTime = Files.getLastModifiedTime(Path.of(path));
                LocalDateTime lastModifiedTimeLocalDateTime = LocalDateTime.ofInstant(lastModifiedTime.toInstant(), ZoneId.systemDefault());
                this.modifyDate = lastModifiedTimeLocalDateTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            } catch (Exception e) {
                this.modifyDate = LocalDateTime.ofEpochSecond(modifyDateMills, 0, ZoneOffset.UTC).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            }
        }
        if (fileSize != 0) {
            this.fileSize = fileSize;
        } else {
            getFileSizeByPath(path);
        }
        this.contentMatch = false;
        this.fuzzyMatched = fuzzyMatched;
        this.prioritizeResult = false;
    }

    public static Optional<SearchResult> buildFromPath(boolean prioritizeResult,
                                                       String path,
                                                       boolean contentMatch,
                                                       boolean fuzzyMatched) {
        try {
            Path pathVar = Path.of(path);
            FileTime lastModifiedTime = Files.getLastModifiedTime(pathVar);
            LocalDateTime lastModifiedTimeLocalDateTime = LocalDateTime.ofInstant(lastModifiedTime.toInstant(), ZoneId.systemDefault());
            SearchResult searchResult = new SearchResult(path, lastModifiedTimeLocalDateTime, Files.size(pathVar), contentMatch, fuzzyMatched, prioritizeResult);
            return Optional.of(searchResult);
        } catch (IOException e) {
            return Optional.empty();
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SearchResult that = (SearchResult) o;
        return fileSize == that.fileSize && Objects.equals(path, that.path) && Objects.equals(modifyDate, that.modifyDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(path, modifyDate, fileSize);
    }

    public String getPath() {
        return path;
    }

    public String getModifyDate() {
        return modifyDate;
    }

    public long getFileSize() {
        return fileSize;
    }

    public boolean isContentMatch() {
        return contentMatch;
    }

    public boolean isFuzzyMatched() {
        return fuzzyMatched;
    }

    public boolean isPrioritizeResult() {
        return prioritizeResult;
    }

    public String getHighlightPath() {
        return highlightPath;
    }

    public void setContentMatch(boolean contentMatch) {
        this.contentMatch = contentMatch;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public void setFuzzyMatched(boolean fuzzyMatched) {
        this.fuzzyMatched = fuzzyMatched;
    }

    public void setModifyDate(String modifyDate) {
        this.modifyDate = modifyDate;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public void setPrioritizeResult(boolean prioritizeResult) {
        this.prioritizeResult = prioritizeResult;
    }

    public void setHighlightPath(String highlightPath) {
        this.highlightPath = highlightPath;
    }

    public String getHighlightFileName() {
        return highlightFileName;
    }
}
