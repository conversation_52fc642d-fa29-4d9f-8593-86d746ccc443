// dllmain.cpp : Defines the entry point for the DLL application.
#include "pch.h"

#include <Windows.h>
#include "file_engine_dllInterface_ParentProcessCheck.h"
#include <tlhelp32.h>
#include <iostream>
#include <wincrypt.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <iphlpapi.h>
#include <Softpub.h>
#include <wintrust.h>

// Link with the Wintrust.lib file.
#pragma comment (lib, "wintrust")
#pragma comment(lib, "iphlpapi.lib")
#pragma comment(lib, "ws2_32.lib")

extern "C" {
	bool is_parent_process_exist();
	bool check_parent_process_by_port(unsigned short port);
}

DWORD get_parent_process_id(DWORD);
DWORD get_pid_by_port(unsigned short port);
bool get_process_path(DWORD pid, LPWSTR path, PDWORD dw_size);
BOOL verify_embedded_signature(LPCWSTR pwsz_source_file);
DWORD get_pid_by_port_and_family(const unsigned short port, ADDRESS_FAMILY family);

JNIEXPORT jboolean JNICALL Java_file_engine_dllInterface_ParentProcessCheck_isParentProcessExist
(JNIEnv*, jobject)
{
	return is_parent_process_exist();
}

JNIEXPORT jboolean JNICALL Java_file_engine_dllInterface_ParentProcessCheck_checkRequestAndParentProcessSignature
(JNIEnv*, jobject, jshort port)
{
	return check_parent_process_by_port(port);
}

bool is_parent_process_exist()
{
	const auto pid = GetCurrentProcessId();
	const auto&& ppid = get_parent_process_id(pid);
	if (ppid == 0)
	{
		return false;
	}
	const auto&& p_handle = OpenProcess(PROCESS_QUERY_INFORMATION, false, ppid);
	if (p_handle == nullptr)
	{
		return false;
	}
	DWORD exit_code{};
	// check for status
	const bool still_exist = GetExitCodeProcess(p_handle, &exit_code) &&
		exit_code == STILL_ACTIVE;
	CloseHandle(p_handle);
	return still_exist;
}

bool check_parent_process_by_port(const unsigned short port)
{
	const auto pid = GetCurrentProcessId();
	const auto ppid = get_parent_process_id(pid);

	const auto pid_from_port = get_pid_by_port(port);

	if (ppid == pid_from_port)
	{
		// check signature
		TCHAR process_path[1000]{ 0 };
		DWORD dw_size = 1000;
		if (!get_process_path(ppid, process_path, &dw_size))
		{
			return false;
		}
		return verify_embedded_signature(process_path);
	}
	return false;
}

bool get_process_path(const DWORD pid, LPWSTR path, PDWORD dw_size)
{
	const HANDLE h_process = OpenProcess(PROCESS_TERMINATE | PROCESS_QUERY_INFORMATION, FALSE, pid);
	if (h_process == nullptr)
	{
		return false;
	}
	if (QueryFullProcessImageName(h_process, 0, path, dw_size))
	{
		CloseHandle(h_process);
		return true;
	}
	CloseHandle(h_process);
	return false;
}

DWORD get_pid_by_port_and_family(const unsigned short port, ADDRESS_FAMILY family)
{
	if (family == AF_INET)
	{
		PMIB_TCPTABLE_OWNER_PID tcpTable = reinterpret_cast<PMIB_TCPTABLE_OWNER_PID>(malloc(sizeof(MIB_TCPTABLE)));
		DWORD size = sizeof(MIB_TCPTABLE);

		if (tcpTable == nullptr)
		{
			return 0;
		}

		const auto ret = GetExtendedTcpTable(tcpTable, &size, false, family, TCP_TABLE_OWNER_PID_ALL, 0);

		// 获取表的大小
		if (ret == ERROR_INSUFFICIENT_BUFFER)
		{
			free(tcpTable);
			tcpTable = reinterpret_cast<PMIB_TCPTABLE_OWNER_PID>(malloc(size));

			if (tcpTable != nullptr && GetExtendedTcpTable(tcpTable, &size, false, family, TCP_TABLE_OWNER_PID_ALL, 0) == NO_ERROR)
			{
				for (DWORD i = 0; i < tcpTable->dwNumEntries; i++)
				{
					MIB_TCPROW_OWNER_PID row = tcpTable->table[i];
					if (row.dwState == MIB_TCP_STATE_ESTAB)
					{
						const auto local_port = ntohs(static_cast<u_short>(row.dwLocalPort));
						//const auto remote_port = ntohs(static_cast<u_short>(row.dwRemotePort));
						if (local_port == port)
						{
							free(tcpTable);
							return row.dwOwningPid;
						}
					}
				}
			}
		}
		else if (ret == NO_ERROR)
		{
			for (DWORD i = 0; i < tcpTable->dwNumEntries; i++)
			{
				MIB_TCPROW_OWNER_PID row = tcpTable->table[i];
				if (row.dwState == MIB_TCP_STATE_ESTAB)
				{
					const auto local_port = ntohs(static_cast<u_short>(row.dwLocalPort));
					//const auto remote_port = ntohs(static_cast<u_short>(row.dwRemotePort));
					if (local_port == port)
					{
						free(tcpTable);
						return row.dwOwningPid;
					}
				}
			}
		}

		if (tcpTable != nullptr)
		{
			free(tcpTable);
		}
	}
	else if (family == AF_INET6)
	{
		PMIB_TCP6TABLE_OWNER_PID tcp6Table = reinterpret_cast<PMIB_TCP6TABLE_OWNER_PID>(malloc(sizeof(MIB_TCP6TABLE_OWNER_PID)));
		DWORD size = sizeof(MIB_TCP6TABLE_OWNER_PID);

		if (tcp6Table == nullptr)
		{
			return 0;
		}

		const auto ret = GetExtendedTcpTable(tcp6Table, &size, false, family, TCP_TABLE_OWNER_PID_ALL, 0);

		// 获取表的大小
		if (ret == ERROR_INSUFFICIENT_BUFFER)
		{
			free(tcp6Table);
			tcp6Table = reinterpret_cast<PMIB_TCP6TABLE_OWNER_PID>(malloc(size));

			if (tcp6Table != nullptr && GetExtendedTcpTable(tcp6Table, &size, false, family, TCP_TABLE_OWNER_PID_ALL, 0) == NO_ERROR)
			{
				for (DWORD i = 0; i < tcp6Table->dwNumEntries; i++)
				{
					MIB_TCP6ROW_OWNER_PID row = tcp6Table->table[i];
					if (row.dwState == MIB_TCP_STATE_ESTAB)
					{
						const auto local_port = ntohs(static_cast<u_short>(row.dwLocalPort));
						//const auto remote_port = ntohs(static_cast<u_short>(row.dwRemotePort));
						if (local_port == port)
						{
							free(tcp6Table);
							return row.dwOwningPid;
						}
					}
				}
			}
		}
		else if (ret == NO_ERROR)
		{
			for (DWORD i = 0; i < tcp6Table->dwNumEntries; i++)
			{
				MIB_TCP6ROW_OWNER_PID row = tcp6Table->table[i];
				if (row.dwState == MIB_TCP_STATE_ESTAB)
				{
					const auto local_port = ntohs(static_cast<u_short>(row.dwLocalPort));
					//const auto remote_port = ntohs(static_cast<u_short>(row.dwRemotePort));
					if (local_port == port)
					{
						free(tcp6Table);
						return row.dwOwningPid;
					}
				}
			}
		}

		if (tcp6Table != nullptr)
		{
			free(tcp6Table);
		}
	}

	return 0;
}

DWORD get_pid_by_port(const unsigned short port)
{
	auto pid = get_pid_by_port_and_family(port, AF_INET);
	if (pid != 0)
	{
		return pid;
	}
	pid = get_pid_by_port_and_family(port, AF_INET6);
	return pid;
}

DWORD get_parent_process_id(const DWORD pid)
{
	const HANDLE h = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
	PROCESSENTRY32 pe{};
	pe.dwSize = sizeof(PROCESSENTRY32);

	DWORD ppid = 0;

	if (Process32First(h, &pe))
	{
		do
		{
			if (pe.th32ProcessID == pid)
			{
				ppid = pe.th32ParentProcessID;
				break;
			}
		} while (Process32Next(h, &pe));
	}
	CloseHandle(h);
	return ppid;
}


BOOL verify_embedded_signature(const LPCWSTR pwsz_source_file)
{
	// Initialize the WINTRUST_FILE_INFO structure.

	WINTRUST_FILE_INFO file_data = {};
	file_data.cbStruct = sizeof(WINTRUST_FILE_INFO);
	file_data.pcwszFilePath = pwsz_source_file;
	file_data.hFile = nullptr;
	file_data.pgKnownSubject = nullptr;

	/*
	WVTPolicyGUID specifies the policy to apply on the file
	WINTRUST_ACTION_GENERIC_VERIFY_V2 policy checks:

	1) The certificate used to sign the file chains up to a root
	certificate located in the trusted root certificate store. This
	implies that the identity of the publisher has been verified by
	a certification authority.

	2) In cases where user interface is displayed (which this example
	does not do), WinVerifyTrust will check for whether the
	end entity certificate is stored in the trusted publisher store,
	implying that the user trusts content from this publisher.

	3) The end entity certificate has sufficient permission to sign
	code, as indicated by the presence of a code signing EKU or no
	EKU.
	*/

	GUID wvt_policy_guid = WINTRUST_ACTION_GENERIC_VERIFY_V2;
	WINTRUST_DATA win_trust_data = {};

	// Initialize the WinVerifyTrust input data structure.

	win_trust_data.cbStruct = sizeof(win_trust_data);

	// Use default code signing EKU.
	win_trust_data.pPolicyCallbackData = nullptr;

	// No data to pass to SIP.
	win_trust_data.pSIPClientData = nullptr;

	// Disable WVT UI.
	win_trust_data.dwUIChoice = WTD_UI_NONE;

	// No revocation checking.
	win_trust_data.fdwRevocationChecks = WTD_REVOKE_NONE;

	// Verify an embedded signature on a file.
	win_trust_data.dwUnionChoice = WTD_CHOICE_FILE;

	// Verify action.
	win_trust_data.dwStateAction = WTD_STATEACTION_VERIFY;

	// Verification sets this value.
	win_trust_data.hWVTStateData = nullptr;

	// Not used.
	win_trust_data.pwszURLReference = nullptr;

	// This is not applicable if there is no UI because it changes
	// the UI to accommodate running applications instead of
	// installing applications.
	win_trust_data.dwUIContext = 0;

	// Set pFile.
	win_trust_data.pFile = &file_data;

	// WinVerifyTrust verifies signatures as specified by the GUID
	// and Wintrust_Data.
	LONG l_status = WinVerifyTrust(
		nullptr,
		&wvt_policy_guid,
		&win_trust_data);

	bool is_signed;
	switch (l_status)
	{
	case ERROR_SUCCESS:
		/*
		Signed file:
			- Hash that represents the subject is trusted.

			- Trusted publisher without any verification errors.

			- UI was disabled in dwUIChoice. No publisher or
				time stamp chain errors.

			- UI was enabled in dwUIChoice and the user clicked
				"Yes" when asked to install and run the signed
				subject.
		*/
		is_signed = true;
		break;

	case TRUST_E_NOSIGNATURE:
		// The file was not signed or had a signature
		// that was not valid.
		is_signed = false;
		break;

	case TRUST_E_EXPLICIT_DISTRUST:
		// The hash that represents the subject or the publisher
		// is not allowed by the admin or user.
		is_signed = false;
		break;

	case TRUST_E_SUBJECT_NOT_TRUSTED:
		// The user clicked "No" when asked to install and run.
		is_signed = false;
		break;

	case CRYPT_E_SECURITY_SETTINGS:
		/*
		The hash that represents the subject or the publisher
		was not explicitly trusted by the admin and the
		admin policy has disabled user trust. No signature,
		publisher or time stamp errors.
		*/
		is_signed = false;
		break;

	default:
		// The UI was disabled in dwUIChoice or the admin policy
		// has disabled user trust. lStatus contains the
		// publisher or time stamp chain error.
		is_signed = false;
		break;
	}

	// Any hWVTStateData must be released by a call with close.
	win_trust_data.dwStateAction = WTD_STATEACTION_CLOSE;

	l_status = WinVerifyTrust(
		nullptr,
		&wvt_policy_guid,
		&win_trust_data);
	return is_signed;
}
