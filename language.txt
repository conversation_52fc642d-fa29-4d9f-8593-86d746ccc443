#frame_width=
#frame_height=
General=
Search settings=
Search bar settings=
Hotkey settings=
My commands=
Color settings=
Language settings=
About=
Add to startup=
Backup and remove all desktop files=
Set the maximum number of caches:=
Choose=
File update detection interval:=
Seconds=
Priority search folder location (double-click to clear):=
Separate different paths with commas, and ignore C:\Windows by default=
Set ignore folder:=
Close search bar when focus lost=
Open other programs as an administrator (provided that the software has privileges)=
Search bar opacity:=
Open search bar:=
Run as administrator:=
Open the parent folder:=
Copy path:=
You can add custom commands here. After adding, you can enter ": + your set identifier" in the search box to quickly open=
Add=
Delete=
Please enter the hexadecimal value of RGB color=
Search bar Color:=
Chosen label color:=
Chosen label font Color:=
Default background Color:=
Unchosen label Color:=
Reset to default=
This is an open source software,GitHub:=
If you find a bug or have some suggestions, welcome to GitHub for feedback=
Check for update=
Thanks for the following project=
Save=
Warning=
Restart failed=
Execute failed=
Info=
Updating file index=
Current Version:=
Are you sure you want to empty the recycle bin=
Successfully empty the recycle bin=
Failed to empty the recycle bin=
Run command=
No result=
Search Done=
Failed to create cache file, program is exiting=
Not administrator, file monitoring function is turned off=
New version can be updated=
The translation might not be 100% accurate=
The program is detected on the desktop and cannot be moved=
Whether to remove and backup all files on the desktop,they will be in the program's Files folder, which may take a few minutes=
Please enter the ID of the command, then you can enter ": identifier" in the search box to execute the command directly=
Conflict with existing commands=
Please select the location of the executable file (a folder is also acceptable)=
Choose Color=
Detected that the cache does not exist and is searching again=
Check update failed=
New Version available=
Whether to update=
update content=
Downloaded=
Download failed=
The download is complete and the update will start at the next boot=
Latest version:=
The current version is the latest=
The file index update setting is wrong, please change=
The cache capacity is set incorrectly, please change=
Hotkey setting is wrong, please change=
Transparency setting error=
HotKey conflict=
Chosen label color is set incorrectly=
Chosen label font color is set incorrectly=
Incorrect default background color setting=
Unchosen label font color is set incorrectly=
The color of the search bar is set incorrectly=
Add to startup failed, please try to run as administrator=
Delete startup failed, please try to run as administrator=
Files with the same name are detected, please move them by yourself=
Choose a language=
Cancel=
Save successfully=
Installed plugins num:=
The current Version is the latest.=
New version available, do you want to update?=
Download failed.=
Plugins=
Plugin Market=
Installed=
Plugin Api is too old=
Version=
Author=
Install=
Duplicate plugin, please delete it in plugins folder=
Downloading:=
Click to organize the database and reduce the size of the database=
but it will consume a lot of time.=
Database is not usable yet, please wait...=
Task is still running.=
Proxy settings=
No proxy=
Configure proxy=
Address=
Port=
User name=
Password=
If you need a proxy to access the Internet, You can add a proxy here.=
Proxy port is set incorrectly.=
Confirm whether to start optimizing the database?=
Optimizing...=
Optimized=
Settings=
Exit=
Text copied to clipboard=
Choose update address=
Clear the recycle bin=
Update file index=
View help=
View Version=
Failed to obtain plugin information=
Cache Settings=
You can edit the saved caches here=
Delete cache=
Cache=
The cache is automatically generated by the software and will be displayed first when searching.=
The operation is irreversible. Are you sure you want to clear the cache?=
Delete all=
SearchBar Font Color:=
The font color of the search bar is set incorrectly=
New versions of these plugins can be updated=
Optimize database=
Border Color:=
Border color is set incorrectly=
Current Caches Num:=
The result has been copied to the clipboard=
Loading plugins error=
Tip:=
If you need to delete a plug-in, just delete it under the "plugins" folder in the software directory.=
Shortcut created=
Show tip on creating shortcut=
Errors=
Failed to save settings, do you still close the window=
The identifier you entered is too short, continue=
Update=
File not exist=
change theme=
Putting the software on the C drive may cause index failure issue=
Preview=
Close preview window=
Search=
Whether to view help=
Welcome to the tutorial of File-Engine=
The default Ctrl + Alt + K calls out the search bar, which can be changed in the settings.=
You can enter the keywords you want to search here=
Let's see an example=
When you enter "test" in the search bar=
files with "test" in the name will be displayed below the search bar=
If you know multiple keywords of a file=
(for example, the file name contains both "file" and "test")=
you can separate them with ";" (semicolon) to search together as keywords.=
When entering "/test" in the search bar=
the file containing "test" in the path will be displayed below the search bar=
Add "|" + suffix after the keyword to achieve a more precise search=
The program has the following four suffixes=
not case sensitive=
"|d" is the suffix for searching only folders=
"|f" is the suffix to search only for files=
"|full" means full word matching, but case insensitive=
"|case" means case sensitive=
You can also combine different suffixes to use=
Different keywords are separated by ";" (semicolon), suffix and keywords are separated by "|" (vertical bar)=
Click "Enter" to open the file directly=
Click "Ctrl + Enter" to open the folder where the file is located=
Click "Shift + Enter" to open the file as an administrator (use with caution)=
Click "Alt+ Enter" to copy the file path=
You can modify these hotkeys in the settings=
Enter ":" (colon) at the front of the search box to enter the command mode=
There are built-in commands, you can also add custom commands in the settings=
If you find that some files cannot be searched, you can enter ":update" in the search bar to rebuild the index.=
This is the settings window=
You can modify many settings here=
Including the color of the window, the hot key to call out the search box, the transparency of the window, custom commands and so on.=
End of the tutorial=
You can enter ":help" in the search bar at any time to enter the tutorial=
There are more detailed tutorials on the Github wiki. Would you like to check it out?=
Please wait up to 60 seconds=
Waiting overtime=
Modify suffix priority=
Modify=
suffix=
priority=
Duplicate suffix, please check=
Priority num must be positive=
Duplicate priority num, please check=
What you entered is not a number, please try again=
Priority num=
Do you sure want to delete this suffix=
Are you sure to delete all the suffixes=
Modifying the suffix priority requires rebuilding the index (input ":update") to take effect=
Double-click "Ctrl" to open the search bar=
Restart=
Double click to open settings=
Language=
Suffix priority=
Interface=
Clear desktop=
Index=
You can rebuild the disk index here=
The disks listed below will be indexed=
Only supports NTFS format disks=
Only the disks on this machine are listed below, click "Add" button to add a removable disk=
Rebuild=
Check for update at startup=
Border Thickness=
Border Type=
Border thickness is too large, please change=
Attach to explorer=
Open=
Open as administrator=
Copy file path=
Open parent folder=
Do you want to download it manually=
Select disk=
Search Failed=
Priority folder does not exist=
The startup path is invalid=
Clear the database and update file index=
You can drag any search result out to create a shortcut on the desktop or any folder=
Waiting for searching disks=
Round rectangle radius=
Round rectangle radius is wrong, please change=
Searching=
Number of current results=
Currently selected=
Enable GPU acceleration=
Double-click shift to switch here=
GUI related settings=
Search related settings=