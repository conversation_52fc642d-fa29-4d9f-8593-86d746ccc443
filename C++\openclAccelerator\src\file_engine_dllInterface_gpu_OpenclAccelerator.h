/* DO NOT EDIT THIS FILE - it is machine generated */
#include "jni.h"
/* Header for class file_engine_dllInterface_gpu_OpenclAccelerator */

#ifndef _Included_file_engine_dllInterface_gpu_OpenclAccelerator
#define _Included_file_engine_dllInterface_gpu_OpenclAccelerator
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     file_engine_dllInterface_gpu_OpenclAccelerator
 * Method:    resetAllResultStatus
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_gpu_OpenclAccelerator_resetAllResultStatus
  (JNIEnv *, jobject);

/*
 * Class:     file_engine_dllInterface_gpu_OpenclAccelerator
 * Method:    match
 * Signature: ([Ljava/lang/String;ZLjava/lang/String;[Ljava/lang/String;[Ljava/lang/String;[ZIIZLjava/util/function/Consumer;)V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_gpu_OpenclAccelerator_match
  (JNIEnv *, jobject, jobjectArray, jboolean, jstring, jobjectArray, jobjectArray, jbooleanArray, jint, jint, jboolean, jobject);

/*
 * Class:     file_engine_dllInterface_gpu_OpenclAccelerator
 * Method:    isOpenCLAvailable
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_file_engine_dllInterface_gpu_OpenclAccelerator_isOpenCLAvailable
  (JNIEnv *, jobject);

/*
 * Class:     file_engine_dllInterface_gpu_OpenclAccelerator
 * Method:    isMatchDone
 * Signature: (Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_file_engine_dllInterface_gpu_OpenclAccelerator_isMatchDone
  (JNIEnv *, jobject, jstring);

/*
 * Class:     file_engine_dllInterface_gpu_OpenclAccelerator
 * Method:    matchedNumber
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_file_engine_dllInterface_gpu_OpenclAccelerator_matchedNumber
  (JNIEnv *, jobject, jstring);

/*
 * Class:     file_engine_dllInterface_gpu_OpenclAccelerator
 * Method:    stopCollectResults
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_gpu_OpenclAccelerator_stopCollectResults
  (JNIEnv *, jobject);

/*
 * Class:     file_engine_dllInterface_gpu_OpenclAccelerator
 * Method:    hasCache
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_file_engine_dllInterface_gpu_OpenclAccelerator_hasCache
  (JNIEnv *, jobject);

/*
 * Class:     file_engine_dllInterface_gpu_OpenclAccelerator
 * Method:    isCacheExist
 * Signature: (Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_file_engine_dllInterface_gpu_OpenclAccelerator_isCacheExist
  (JNIEnv *, jobject, jstring);

/*
 * Class:     file_engine_dllInterface_gpu_OpenclAccelerator
 * Method:    initCache
 * Signature: (Ljava/lang/String;[Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_gpu_OpenclAccelerator_initCache
  (JNIEnv *, jobject, jstring, jobjectArray);

/*
 * Class:     file_engine_dllInterface_gpu_OpenclAccelerator
 * Method:    addRecordsToCache
 * Signature: (Ljava/lang/String;[Ljava/lang/Object;)V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_gpu_OpenclAccelerator_addRecordsToCache
  (JNIEnv *, jobject, jstring, jobjectArray);

/*
 * Class:     file_engine_dllInterface_gpu_OpenclAccelerator
 * Method:    removeRecordsFromCache
 * Signature: (Ljava/lang/String;[Ljava/lang/Object;)V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_gpu_OpenclAccelerator_removeRecordsFromCache
  (JNIEnv *, jobject, jstring, jobjectArray);

/*
 * Class:     file_engine_dllInterface_gpu_OpenclAccelerator
 * Method:    clearCache
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_gpu_OpenclAccelerator_clearCache
  (JNIEnv *, jobject, jstring);

/*
 * Class:     file_engine_dllInterface_gpu_OpenclAccelerator
 * Method:    clearAllCache
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_gpu_OpenclAccelerator_clearAllCache
  (JNIEnv *, jobject);

/*
 * Class:     file_engine_dllInterface_gpu_OpenclAccelerator
 * Method:    isCacheValid
 * Signature: (Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_file_engine_dllInterface_gpu_OpenclAccelerator_isCacheValid
  (JNIEnv *, jobject, jstring);

/*
 * Class:     file_engine_dllInterface_gpu_OpenclAccelerator
 * Method:    getGPUMemUsage
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_file_engine_dllInterface_gpu_OpenclAccelerator_getGPUMemUsage
  (JNIEnv *, jobject);

/*
 * Class:     file_engine_dllInterface_gpu_OpenclAccelerator
 * Method:    initialize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_gpu_OpenclAccelerator_initialize
  (JNIEnv *, jobject);

/*
 * Class:     file_engine_dllInterface_gpu_OpenclAccelerator
 * Method:    release
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_gpu_OpenclAccelerator_release
  (JNIEnv *, jobject);

/*
 * Class:     file_engine_dllInterface_gpu_OpenclAccelerator
 * Method:    getDevices
 * Signature: ()[Ljava/lang/String;
 */
JNIEXPORT jobjectArray JNICALL Java_file_engine_dllInterface_gpu_OpenclAccelerator_getDevices
  (JNIEnv *, jobject);

/*
 * Class:     file_engine_dllInterface_gpu_OpenclAccelerator
 * Method:    setDevice
 * Signature: (I)Z
 */
JNIEXPORT jboolean JNICALL Java_file_engine_dllInterface_gpu_OpenclAccelerator_setDevice
  (JNIEnv *, jobject, jint);

#ifdef __cplusplus
}
#endif
#endif
