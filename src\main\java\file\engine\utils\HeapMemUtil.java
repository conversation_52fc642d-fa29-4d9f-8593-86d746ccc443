package file.engine.utils;

import file.engine.event.handler.EventManagement;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryPoolMXBean;
import java.lang.management.MemoryType;
import java.lang.management.MemoryUsage;
import java.util.concurrent.TimeUnit;

public class HeapMemUtil {

    private static volatile double percentage = 1.0;
    private static volatile HeapMemUtil instance;

    private HeapMemUtil() {
        updatePercentage();
        Thread.ofVirtual().start(() -> {
            EventManagement eventManagement = EventManagement.getInstance();
            while (eventManagement.notMainExit()) {
                updatePercentage();
                try {
                    TimeUnit.SECONDS.sleep(1);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        });
    }

    private static void updatePercentage() {
        long max = 0;
        long used = 0;
        for (MemoryPoolMXBean mpBean : ManagementFactory.getMemoryPoolMXBeans()) {
            if (mpBean.getType() == MemoryType.HEAP) {
                MemoryUsage usage = mpBean.getUsage();
                var maxValue = usage.getMax();
                if (maxValue != -1L && max < maxValue) {
                    max = maxValue;
                }
                long usedValue = usage.getUsed();
                if (usedValue > 0) {
                    used += usedValue;
                }
            }
        }
        if (max != 0) {
            percentage = (double) used / max;
        } else {
            percentage = 1.0;
        }
    }

    public static HeapMemUtil getInstance() {
        if (instance == null) {
            synchronized (HeapMemUtil.class) {
                if (instance == null) {
                    instance = new HeapMemUtil();
                }
            }
        }
        return instance;
    }

    public double getHeapUsage() {
        return percentage;
    }
}
