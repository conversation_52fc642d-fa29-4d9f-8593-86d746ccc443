package file.engine.services.utils;

import com.sun.jna.platform.win32.Kernel32Util;
import com.sun.jna.platform.win32.Win32Exception;
import file.engine.services.utils.reader.FileReaderUtil;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.document.Document;
import org.apache.lucene.document.Field;
import org.apache.lucene.document.FieldType;
import org.apache.lucene.document.TextField;
import org.apache.lucene.index.*;
import org.apache.lucene.queryparser.classic.QueryParser;
import org.apache.lucene.search.*;
import org.apache.lucene.store.Directory;
import org.apache.lucene.store.FSDirectory;
import org.wltea.analyzer.lucene.IKAnalyzer;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileTime;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class LuceneIndexUtil implements AutoCloseable {
    private final IndexWriter writer;
    private IndexReader reader;
    private volatile IndexSearcher indexSearcher;
    private final Directory directory;
    private final SearcherManager searcherManager;
    @Getter
    private volatile IndexStatus indexingFlag = IndexStatus.IDLE;

    public static final String FILE_FULL_PATH_FILED_NAME = "_fullPath";
    public static final String FILE_CONTENT_FILED_NAME = "_contents";
    public static final String FILE_MODIFY_DATE_FILED_NAME = "_modifyDate";
    public static final String FILE_SIZE_FILED_NAME = "_size";

    public enum IndexStatus {
        IDLE, STOPPING, INDEXING
    }

    public LuceneIndexUtil(String indexStoreDir) throws IOException {
        this.directory = FSDirectory.open(Paths.get(indexStoreDir));
        Analyzer analyzer = new IKAnalyzer();
        IndexWriterConfig iwConfig = new IndexWriterConfig(analyzer);
        iwConfig.setOpenMode(IndexWriterConfig.OpenMode.CREATE_OR_APPEND);
        TieredMergePolicy t = new TieredMergePolicy();
        t.setForceMergeDeletesPctAllowed(.01);
        iwConfig.setMergePolicy(t);
        writer = new IndexWriter(directory, iwConfig);
        searcherManager = new SearcherManager(writer, null);
        writer.commit();
    }

    public synchronized void setIndexingFlag(IndexStatus indexingFlag) {
        this.indexingFlag = indexingFlag;
    }

    @Override
    public void close() throws IOException {
        if (reader != null) {
            reader.close();
        }
        searcherManager.close();
        writer.close();
        directory.close();
    }

    public void commit() throws IOException {
        writer.commit();
    }

    public void maybeRefresh() throws IOException {
        searcherManager.maybeRefresh();
    }

    public int indexFile(File f) {
        try {
            if (!f.exists()) {
                return 0;
            }
            String canonicalPath = f.getCanonicalPath();
            try {
                int fileAttributes = Kernel32Util.getFileAttributes(canonicalPath);
                // 0x00400000 -> FILE_ATTRIBUTE_RECALL_ON_DATA_ACCESS
                if ((fileAttributes & 0x00400000) != 0) {
                    // 文件位于云端，并未完全下载到本地，不进行搜索
                    return 0;
                }
            } catch (Win32Exception ignored) {
            }
            String fileContent;
            try {
                fileContent = FileReaderUtil.readFile(canonicalPath);
            } catch (Exception ignored) {
                return 0;
            }
            if (fileContent == null || fileContent.isEmpty()) {
                return 0;
            }
            Document doc = new Document();
            doc.add(new TextField(FILE_CONTENT_FILED_NAME, fileContent, Field.Store.YES));

            FieldType fieldType = new FieldType();
            fieldType.setStored(true);
            fieldType.setTokenized(false);
            fieldType.setIndexOptions(IndexOptions.DOCS);

            Field fullPathField = new Field(FILE_FULL_PATH_FILED_NAME, canonicalPath, fieldType);
            doc.add(fullPathField);

            Path path = Path.of(canonicalPath);
            FileTime lastModifiedTime = Files.getLastModifiedTime(path);
            LocalDateTime localDateTime = LocalDateTime.ofInstant(lastModifiedTime.toInstant(), ZoneId.systemDefault());
            String modifiedTime = localDateTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            Field modifyDateField = new Field(FILE_MODIFY_DATE_FILED_NAME, modifiedTime, fieldType);
            doc.add(modifyDateField);

            Field fileSizeField = new Field(FILE_SIZE_FILED_NAME, String.valueOf(Files.size(path)), fieldType);
            doc.add(fileSizeField);

            Term term = new Term(FILE_FULL_PATH_FILED_NAME, f.getCanonicalPath());
            writer.updateDocument(term, doc);
            return fileContent.length();
        } catch (IOException ignored) {
        } catch (Exception e) {
            log.error("error {}", e.getMessage(), e);
        }
        return 0;
    }

    public void remove(File f) {
        try {
            Term term = new Term(FILE_FULL_PATH_FILED_NAME, f.getCanonicalPath());
            writer.deleteDocuments(term);
        } catch (IOException ignored) {
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @SneakyThrows
    public List<Document> search(String keywords, int n) {
        ArrayList<Document> documents = new ArrayList<>();
        if (indexSearcher == null) {
            synchronized (this) {
                if (indexSearcher == null) {
                    this.reader = DirectoryReader.open(this.directory);
                    this.indexSearcher = new IndexSearcher(reader);
                }
            }
        }
        Analyzer analyzer = new IKAnalyzer();
        QueryParser parser = new QueryParser(FILE_CONTENT_FILED_NAME, analyzer);
        Query query = parser.parse(keywords);
        TopDocs hits = indexSearcher.search(query, n);

        StoredFields storedFields = indexSearcher.storedFields();
        for (ScoreDoc hit : hits.scoreDocs) {
            Document doc = storedFields.document(hit.doc);
            documents.add(doc);
        }
        return documents;
    }
}
