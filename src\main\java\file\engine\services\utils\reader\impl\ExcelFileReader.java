package file.engine.services.utils.reader.impl;

import file.engine.services.utils.reader.ContentReader;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.eventusermodel.HSSFEventFactory;
import org.apache.poi.hssf.eventusermodel.HSSFListener;
import org.apache.poi.hssf.eventusermodel.HSSFRequest;
import org.apache.poi.hssf.record.Record;
import org.apache.poi.hssf.record.*;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.util.CellAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.util.XMLHelper;
import org.apache.poi.xssf.eventusermodel.ReadOnlySharedStringsTable;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.eventusermodel.XSSFSheetXMLHandler;
import org.apache.poi.xssf.model.SharedStrings;
import org.apache.poi.xssf.model.Styles;
import org.apache.poi.xssf.model.StylesTable;
import org.apache.poi.xssf.usermodel.XSSFComment;
import org.xml.sax.ContentHandler;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;
import org.xml.sax.XMLReader;

import javax.xml.parsers.ParserConfigurationException;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

@Slf4j
public class ExcelFileReader implements ContentReader {
    private static final ThreadLocal<StringBuilder> builderThreadLocal = new InheritableThreadLocal<>();

    @Override
    public String readContent(String fileAbsPath) {
        try {
            StringBuilder content = new StringBuilder();
            builderThreadLocal.set(content);
            File file = new File(fileAbsPath);
            String filePath = file.getAbsolutePath();
            if (filePath.endsWith(".xls")) {
                try (POIFSFileSystem poifs = new POIFSFileSystem(new FileInputStream(file));
                     InputStream din = poifs.createDocumentInputStream("Workbook")) {
                    // construct out HSSFRequest object
                    HSSFRequest req = new HSSFRequest();
                    // lazy listen for ALL records with the listener shown above
                    req.addListenerForAllRecords(new HSSFEventListener());
                    // create our event factory
                    HSSFEventFactory factory = new HSSFEventFactory();
                    // process our events based on the document input stream
                    factory.processEvents(req, din);
                }
            } else if (filePath.endsWith(".xlsx")) {
                try (OPCPackage opcPackage = OPCPackage.open(file)) {
                    ReadOnlySharedStringsTable strings = new ReadOnlySharedStringsTable(opcPackage);
                    XSSFReader xssfReader = new XSSFReader(opcPackage);
                    StylesTable styles = xssfReader.getStylesTable();
                    XSSFReader.SheetIterator iter = (XSSFReader.SheetIterator) xssfReader.getSheetsData();
                    while (iter.hasNext()) {
                        try (InputStream stream = iter.next()) {
                            String sheetName = iter.getSheetName();
                            try {
                                processSheet(styles, strings, new SheetContentHandler(), stream);
                            } catch (NumberFormatException e) {
                                throw new IOException("Failed to parse sheet " + sheetName, e);
                            }
                        }
                    }
                }
            }
            return content.toString();
        } catch (Exception e) {
            log.error("error {}", e.getMessage(), e);
        } finally {
            builderThreadLocal.remove();
        }
        return "";
    }

    /**
     * Parses and shows the content of one sheet
     * using the specified styles and shared-strings tables.
     *
     * @param styles           The table of styles that may be referenced by cells in the sheet
     * @param strings          The table of strings that may be referenced by cells in the sheet
     * @param sheetInputStream The stream to read the sheet-data from.
     * @throws java.io.IOException An IO exception from the parser,
     *                             possibly from a byte stream or character stream
     *                             supplied by the application.
     * @throws SAXException        if parsing the XML data fails.
     */
    private void processSheet(
            Styles styles,
            SharedStrings strings,
            XSSFSheetXMLHandler.SheetContentsHandler sheetHandler,
            InputStream sheetInputStream) throws IOException, SAXException {
        // set emulateCSV=true on DataFormatter - it is also possible to provide a Locale
        // when POI 5.2.0 is released, you can call formatter.setUse4DigitYearsInAllDateFormats(true)
        // to ensure all dates are formatted with 4 digit years
        DataFormatter formatter = new DataFormatter(true);
        InputSource sheetSource = new InputSource(sheetInputStream);
        try {
            XMLReader sheetParser = XMLHelper.newXMLReader();
            ContentHandler handler = new XSSFSheetXMLHandler(
                    styles, null, strings, sheetHandler, formatter, false);
            sheetParser.setContentHandler(handler);
            sheetParser.parse(sheetSource);
        } catch (ParserConfigurationException e) {
            throw new RuntimeException("SAX parser appears to be broken - " + e.getMessage());
        }
    }

    private static class HSSFEventListener implements HSSFListener {
        private SSTRecord sstrec;

        /**
         * This method listens for incoming records and handles them as required.
         *
         * @param record The record that was found while reading.
         */
        public void processRecord(Record record) {
            StringBuilder output = builderThreadLocal.get();
            switch (record.getSid()) {
                case BOFRecord.sid:
                case BoundSheetRecord.sid:
                case RowRecord.sid:
                    break;
                case NumberRecord.sid:
                    NumberRecord numrec = (NumberRecord) record;
                    output.append(numrec.getValue()).append(' ');
                    break;
                // SSTRecords store an array of unique strings used in Excel.
                case SSTRecord.sid:
                    sstrec = (SSTRecord) record;
                    for (int k = 0; k < sstrec.getNumUniqueStrings(); k++) {
                        output.append(sstrec.getString(k)).append(' ');
                    }
                    break;
                case LabelSSTRecord.sid:
                    LabelSSTRecord lrec = (LabelSSTRecord) record;
                    output.append(sstrec.getString(lrec.getSSTIndex())).append(' ');
                    break;
            }
        }
    }

    /**
     * <a href="https://svn.apache.org/repos/asf/poi/trunk/poi-examples/src/main/java/org/apache/poi/examples/xssf/eventusermodel/XLSX2CSV.java">example</a>
     */
    private static class SheetContentHandler implements XSSFSheetXMLHandler.SheetContentsHandler {
        private boolean firstCellOfRow;
        private int currentRow = -1;
        private int currentCol = -1;

        @Override
        public void startRow(int rowNum) {
            // Prepare for this row
            firstCellOfRow = true;
            currentRow = rowNum;
            currentCol = -1;
        }

        @Override
        public void endRow(int rowNum) {
        }

        @Override
        public void cell(String cellReference, String formattedValue,
                         XSSFComment comment) {
            StringBuilder output = builderThreadLocal.get();
            if (firstCellOfRow) {
                firstCellOfRow = false;
            }

            // gracefully handle missing CellRef here in a similar way as XSSFCell does
            if (cellReference == null) {
                cellReference = new CellAddress(currentRow, currentCol).formatAsString();
            }

            // Did we miss any cells?
            int thisCol = (new CellReference(cellReference)).getCol();

            // no need to append anything if we do not have a value
            if (formattedValue == null) {
                return;
            }

            currentCol = thisCol;
            output.append(formattedValue).append(' ');
        }
    }
}
