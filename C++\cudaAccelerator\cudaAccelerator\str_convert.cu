﻿#include "framework.h"
#include "str_convert.cuh"
#include <cstdlib>
#include "cuda_runtime.h"
#include "kernels.cuh"

#define COMPBYTE(x, y) ((unsigned char)(x) << 8 | (unsigned char)(y))

__device__ unsigned short utf162gbk[0x10000] = { 0 };
// __device__ unsigned short gbk2utf16[0x8000] = { 0 };
constexpr unsigned short gbk2_utf16_2_host[] =
{
	0x8140, 0x4E02,
	0x8144, 0x4E0F,
	0x8145, 0x4E12,
	0x8146, 0x4E17,
	0x814A, 0x4E23,
	0x814B, 0x4E26,
	0x814C, 0x4E29,
	0x814F, 0x4E31,
	0x8150, 0x4E33,
	0x8151, 0x4E35,
	0x8152, 0x4E37,
	0x8153, 0x4E3C,
	0x8157, 0x4E44,
	0x8158, 0x4E46,
	0x8159, 0x4E4A,
	0x815A, 0x4E51,
	0x815B, 0x4E55,
	0x815C, 0x4E57,
	0x816B, 0x4E72,
	0x817D, 0x4E87,
	0x817E, 0x4E8A,
	0x8180, 0x4E90,
	0x8183, 0x4E99,
	0x8187, 0x4EA3,
	0x8188, 0x4EAA,
	0x818C, 0x4EB4,
	0x8194, 0x4EC8,
	0x8195, 0x4ECC,
	0x8198, 0x4ED2,
	0x819C, 0x4EE0,
	0x819D, 0x4EE2,
	0x81A0, 0x4EE9,
	0x81A4, 0x4EF1,
	0x81A5, 0x4EF4,
	0x81A9, 0x4EFC,
	0x81AA, 0x4EFE,
	0x81AB, 0x4F00,
	0x81BC, 0x4F21,
	0x81BD, 0x4F23,
	0x81C3, 0x4F31,
	0x81C4, 0x4F33,
	0x81C5, 0x4F35,
	0x81C6, 0x4F37,
	0x81C7, 0x4F39,
	0x81C8, 0x4F3B,
	0x81D6, 0x4F52,
	0x81D7, 0x4F54,
	0x81D8, 0x4F56,
	0x81DB, 0x4F66,
	0x81DC, 0x4F68,
	0x81E3, 0x4F75,
	0x81E8, 0x4F7D,
	0x81EF, 0x4F8A,
	0x81F0, 0x4F8C,
	0x81F1, 0x4F8E,
	0x81F2, 0x4F90,
	0x81FA, 0x4F9C,
	0x8240, 0x4FA4,
	0x8241, 0x4FAB,
	0x8242, 0x4FAD,
	0x8260, 0x4FD9,
	0x8261, 0x4FDB,
	0x8262, 0x4FE0,
	0x8263, 0x4FE2,
	0x8266, 0x4FE7,
	0x8269, 0x4FF0,
	0x826A, 0x4FF2,
	0x826F, 0x4FF9,
	0x8280, 0x500B,
	0x8281, 0x500E,
	0x8284, 0x5013,
	0x8288, 0x501B,
	0x828B, 0x5020,
	0x828F, 0x5027,
	0x8290, 0x502B,
	0x829C, 0x503B,
	0x829D, 0x503D,
	0x82A8, 0x504D,
	0x82B2, 0x505B,
	0x82EE, 0x50A4,
	0x82EF, 0x50A6,
	0x82FE, 0x50BC,
	0x836E, 0x50F4,
	0x837E, 0x5108,
	0x83B3, 0x5142,
	0x83B4, 0x5147,
	0x83B5, 0x514A,
	0x83B6, 0x514C,
	0x83BF, 0x515B,
	0x83CB, 0x516F,
	0x83CC, 0x5172,
	0x83CD, 0x517A,
	0x83DC, 0x5198,
	0x83DD, 0x519A,
	0x83E1, 0x51A1,
	0x83E2, 0x51A3,
	0x83EA, 0x51B4,
	0x83F3, 0x51C5,
	0x83F4, 0x51C8,
	0x83F5, 0x51CA,
	0x83F8, 0x51D0,
	0x8443, 0x51DC,
	0x844E, 0x51EC,
	0x844F, 0x51EE,
	0x8452, 0x51F4,
	0x8453, 0x51F7,
	0x8454, 0x51FE,
	0x8457, 0x5209,
	0x845F, 0x521C,
	0x8468, 0x522A,
	0x8469, 0x522C,
	0x846A, 0x522F,
	0x846F, 0x523C,
	0x8470, 0x523E,
	0x8477, 0x524B,
	0x847C, 0x5255,
	0x8483, 0x525D,
	0x8489, 0x5266,
	0x848A, 0x5268,
	0x849B, 0x527E,
	0x849C, 0x5280,
	0x84B2, 0x529C,
	0x84CA, 0x52C8,
	0x84CB, 0x52CA,
	0x84D0, 0x52D1,
	0x84D4, 0x52D7,
	0x84F9, 0x5307,
	0x84FE, 0x530E,
	0x8544, 0x5318,
	0x8549, 0x5322,
	0x855E, 0x5340,
	0x855F, 0x5342,
	0x8560, 0x5344,
	0x8561, 0x5346,
	0x8565, 0x5350,
	0x8566, 0x5354,
	0x8569, 0x535B,
	0x856A, 0x535D,
	0x856B, 0x5365,
	0x856C, 0x5368,
	0x856D, 0x536A,
	0x8570, 0x5372,
	0x8571, 0x5376,
	0x8572, 0x5379,
	0x8579, 0x5383,
	0x857C, 0x538A,
	0x8587, 0x5399,
	0x858A, 0x539E,
	0x858D, 0x53A4,
	0x858E, 0x53A7,
	0x85A1, 0x53C0,
	0x85AC, 0x53D5,
	0x85AD, 0x53DA,
	0x85B3, 0x53E7,
	0x85B4, 0x53F4,
	0x85B5, 0x53FA,
	0x85B9, 0x5402,
	0x85BA, 0x5405,
	0x85BB, 0x5407,
	0x85BC, 0x540B,
	0x85BD, 0x5414,
	0x85C1, 0x541C,
	0x85C2, 0x5422,
	0x85C5, 0x542A,
	0x85C6, 0x5430,
	0x85C7, 0x5433,
	0x85CA, 0x543A,
	0x85CB, 0x543D,
	0x85CC, 0x543F,
	0x85D1, 0x5447,
	0x85D2, 0x5449,
	0x85D7, 0x5451,
	0x85D8, 0x545A,
	0x85DE, 0x5463,
	0x85DF, 0x5465,
	0x85E0, 0x5467,
	0x85E9, 0x5474,
	0x85EE, 0x5481,
	0x85EF, 0x5483,
	0x85F0, 0x5485,
	0x85F5, 0x548D,
	0x85F6, 0x5491,
	0x85F7, 0x5493,
	0x85FA, 0x549C,
	0x8640, 0x54A2,
	0x8641, 0x54A5,
	0x8642, 0x54AE,
	0x8643, 0x54B0,
	0x8644, 0x54B2,
	0x864A, 0x54BC,
	0x864B, 0x54BE,
	0x864C, 0x54C3,
	0x864D, 0x54C5,
	0x8650, 0x54D6,
	0x8651, 0x54D8,
	0x8652, 0x54DB,
	0x8663, 0x54FB,
	0x8664, 0x54FE,
	0x8665, 0x5500,
	0x866A, 0x5508,
	0x867C, 0x5521,
	0x8682, 0x552B,
	0x8683, 0x552D,
	0x8684, 0x5532,
	0x868C, 0x553D,
	0x868D, 0x5540,
	0x868E, 0x5542,
	0x868F, 0x5545,
	0x86A8, 0x556B,
	0x86B1, 0x557D,
	0x86B2, 0x557F,
	0x86B8, 0x5590,
	0x86C0, 0x559E,
	0x86D1, 0x55B2,
	0x86D2, 0x55B4,
	0x86D3, 0x55B6,
	0x86D4, 0x55B8,
	0x86D5, 0x55BA,
	0x86D6, 0x55BC,
	0x86E4, 0x55D5,
	0x86EA, 0x55DE,
	0x86EB, 0x55E0,
	0x86EC, 0x55E2,
	0x86ED, 0x55E7,
	0x86EE, 0x55E9,
	0x86F3, 0x55F4,
	0x86F4, 0x55F6,
	0x86FA, 0x55FF,
	0x8744, 0x560D,
	0x875D, 0x5633,
	0x875E, 0x5635,
	0x8761, 0x563A,
	0x8780, 0x5663,
	0x87E1, 0x56DC,
	0x87E2, 0x56E3,
	0x87E9, 0x56EC,
	0x87F6, 0x5705,
	0x87F7, 0x5707,
	0x8853, 0x572B,
	0x885D, 0x573F,
	0x885E, 0x5741,
	0x8865, 0x574B,
	0x886F, 0x5765,
	0x8870, 0x5767,
	0x8871, 0x576C,
	0x8872, 0x576E,
	0x8880, 0x5781,
	0x8895, 0x57A5,
	0x8896, 0x57A8,
	0x8897, 0x57AA,
	0x8898, 0x57AC,
	0x889C, 0x57B3,
	0x88B4, 0x57D3,
	0x88B9, 0x57DE,
	0x88C5, 0x57EE,
	0x88D1, 0x5801,
	0x88D8, 0x580C,
	0x88E6, 0x581F,
	0x8976, 0x587F,
	0x8977, 0x5882,
	0x8978, 0x5884,
	0x89D0, 0x58ED,
	0x89D1, 0x58EF,
	0x89E0, 0x5903,
	0x89E8, 0x590E,
	0x89EF, 0x591B,
	0x89F6, 0x5926,
	0x89F7, 0x5928,
	0x89F8, 0x592C,
	0x89F9, 0x5930,
	0x89FE, 0x593B,
	0x8A44, 0x5943,
	0x8A47, 0x594A,
	0x8A4A, 0x5950,
	0x8A4D, 0x5959,
	0x8A53, 0x5961,
	0x8A63, 0x5975,
	0x8A64, 0x5977,
	0x8A6B, 0x5985,
	0x8A6C, 0x5989,
	0x8A75, 0x5998,
	0x8A7E, 0x59A6,
	0x8A80, 0x59A7,
	0x8A8B, 0x59BA,
	0x8A9E, 0x59D9,
	0x8A9F, 0x59DB,
	0x8AA5, 0x59E4,
	0x8AB7, 0x59FA,
	0x8ABB, 0x5A00,
	0x8ABC, 0x5A02,
	0x8AC3, 0x5A12,
	0x8ACF, 0x5A24,
	0x8ADA, 0x5A33,
	0x8ADB, 0x5A35,
	0x8B40, 0x5A61,
	0x8B85, 0x5AB4,
	0x8B9C, 0x5AD3,
	0x8B9D, 0x5AD5,
	0x8B9E, 0x5AD7,
	0x8BA5, 0x5AE2,
	0x8BAA, 0x5AEA,
	0x8BED, 0x5B33,
	0x8C48, 0x5B52,
	0x8C49, 0x5B56,
	0x8C4A, 0x5B5E,
	0x8C4F, 0x5B6B,
	0x8C53, 0x5B72,
	0x8C54, 0x5B74,
	0x8C5D, 0x5B82,
	0x8C5E, 0x5B86,
	0x8C5F, 0x5B8A,
	0x8C65, 0x5B94,
	0x8C66, 0x5B96,
	0x8C67, 0x5B9F,
	0x8C71, 0x5BB7,
	0x8C77, 0x5BC3,
	0x8C80, 0x5BD1,
	0x8C8A, 0x5BE0,
	0x8C94, 0x5BEF,
	0x8C9E, 0x5C00,
	0x8CA1, 0x5C05,
	0x8CA8, 0x5C10,
	0x8CAB, 0x5C17,
	0x8CAC, 0x5C19,
	0x8CAD, 0x5C1B,
	0x8CB2, 0x5C23,
	0x8CB3, 0x5C26,
	0x8CD1, 0x5C5F,
	0x8CD2, 0x5C62,
	0x8CD3, 0x5C64,
	0x8CDB, 0x5C70,
	0x8CE7, 0x5C80,
	0x8CF4, 0x5C95,
	0x8D40, 0x5CAA,
	0x8D44, 0x5CB2,
	0x8D45, 0x5CB4,
	0x8D46, 0x5CB6,
	0x8D4B, 0x5CBE,
	0x8D4C, 0x5CC0,
	0x8D6A, 0x5CE7,
	0x8D6B, 0x5CE9,
	0x8D80, 0x5D01,
	0x8D8E, 0x5D15,
	0x8D9A, 0x5D25,
	0x8D9B, 0x5D28,
	0x8DC3, 0x5D5C,
	0x8DCF, 0x5D6A,
	0x8E77, 0x5DDC,
	0x8E7C, 0x5DEA,
	0x8E80, 0x5DF0,
	0x8E8A, 0x5E04,
	0x8E8B, 0x5E07,
	0x8E93, 0x5E17,
	0x8EAE, 0x5E43,
	0x8ED4, 0x5E75,
	0x8ED5, 0x5E77,
	0x8ED6, 0x5E79,
	0x8ED7, 0x5E7E,
	0x8EDB, 0x5E85,
	0x8EE1, 0x5E92,
	0x8EE2, 0x5E98,
	0x8EE3, 0x5E9B,
	0x8EE4, 0x5E9D,
	0x8EF3, 0x5EB4,
	0x8F5B, 0x5EE9,
	0x8F65, 0x5EF5,
	0x8F6E, 0x5F09,
	0x8F72, 0x5F10,
	0x8F73, 0x5F12,
	0x8F74, 0x5F14,
	0x8F75, 0x5F16,
	0x8F80, 0x5F28,
	0x8F83, 0x5F2E,
	0x8F84, 0x5F30,
	0x8F8C, 0x5F3B,
	0x8F9F, 0x5F51,
	0x8FA0, 0x5F54,
	0x8FA8, 0x5F63,
	0x8FA9, 0x5F65,
	0x8FAC, 0x5F6B,
	0x8FAF, 0x5F72,
	0x8FB3, 0x5F78,
	0x8FB4, 0x5F7A,
	0x8FB8, 0x5F83,
	0x8FB9, 0x5F86,
	0x8FBD, 0x5F91,
	0x8FC0, 0x5F96,
	0x8FCD, 0x5FA9,
	0x8FD6, 0x5FB6,
	0x8FE4, 0x5FCE,
	0x8FF3, 0x5FEC,
	0x8FFD, 0x5FFC,
	0x8FFE, 0x6007,
	0x9046, 0x6013,
	0x9049, 0x601A,
	0x905E, 0x6040,
	0x9066, 0x604C,
	0x9069, 0x6051,
	0x9077, 0x606E,
	0x907C, 0x6077,
	0x907D, 0x607E,
	0x907E, 0x6080,
	0x908C, 0x6093,
	0x908D, 0x6095,
	0x9091, 0x609C,
	0x9092, 0x609E,
	0x9097, 0x60A7,
	0x909A, 0x60AE,
	0x909B, 0x60B0,
	0x909C, 0x60B3,
	0x90B7, 0x60D9,
	0x90B8, 0x60DB,
	0x90B9, 0x60DE,
	0x90BF, 0x60EA,
	0x90C2, 0x60F5,
	0x90CE, 0x6107,
	0x90E1, 0x6125,
	0x9140, 0x6147,
	0x9141, 0x6149,
	0x9142, 0x614B,
	0x9143, 0x614D,
	0x9163, 0x6176,
	0x917E, 0x6195,
	0x91AA, 0x61C9,
	0x91B0, 0x61D3,
	0x91DF, 0x6207,
	0x91E0, 0x6209,
	0x91E3, 0x6219,
	0x91E7, 0x6220,
	0x91E8, 0x6223,
	0x91ED, 0x622B,
	0x91EE, 0x622D,
	0x91FA, 0x6242,
	0x91FE, 0x624A,
	0x9250, 0x6268,
	0x9259, 0x627D,
	0x9267, 0x6294,
	0x9268, 0x6299,
	0x926C, 0x62A3,
	0x927B, 0x62BA,
	0x927C, 0x62BE,
	0x9280, 0x62C3,
	0x9281, 0x62CB,
	0x9282, 0x62CF,
	0x9283, 0x62D1,
	0x9284, 0x62D5,
	0x9289, 0x62E4,
	0x928C, 0x62F0,
	0x928D, 0x62F2,
	0x928E, 0x62F5,
	0x9293, 0x6300,
	0x92A5, 0x631C,
	0x92A8, 0x6329,
	0x92BA, 0x6344,
	0x92BD, 0x634A,
	0x92CA, 0x6360,
	0x92CE, 0x6368,
	0x92DE, 0x6381,
	0x92E3, 0x638B,
	0x92E4, 0x638D,
	0x92E5, 0x6391,
	0x92E9, 0x6397,
	0x92F1, 0x63A1,
	0x92F2, 0x63A4,
	0x92F3, 0x63A6,
	0x92F4, 0x63AB,
	0x92F5, 0x63AF,
	0x92FA, 0x63B9,
	0x92FB, 0x63BB,
	0x92FC, 0x63BD,
	0x9343, 0x63C5,
	0x9349, 0x63D1,
	0x9354, 0x63DF,
	0x9355, 0x63E2,
	0x9361, 0x63F3,
	0x9362, 0x63F5,
	0x9363, 0x63F7,
	0x9368, 0x63FE,
	0x937A, 0x641D,
	0x937B, 0x641F,
	0x9380, 0x6425,
	0x9384, 0x642B,
	0x9392, 0x643E,
	0x9393, 0x6440,
	0x9396, 0x6449,
	0x939E, 0x6453,
	0x93AF, 0x6468,
	0x93C4, 0x6483,
	0x93C5, 0x6486,
	0x93E2, 0x64AF,
	0x93E7, 0x64B6,
	0x93E8, 0x64B9,
	0x93E9, 0x64BB,
	0x93ED, 0x64C1,
	0x93F7, 0x64CF,
	0x93F8, 0x64D1,
	0x9446, 0x64E3,
	0x9447, 0x64E5,
	0x948E, 0x6537,
	0x948F, 0x653A,
	0x949D, 0x6550,
	0x94A3, 0x655A,
	0x94A4, 0x655C,
	0x94B1, 0x6571,
	0x94B2, 0x6573,
	0x94CA, 0x6592,
	0x94CE, 0x6598,
	0x94CF, 0x659A,
	0x94D2, 0x65A0,
	0x94D5, 0x65A6,
	0x94D6, 0x65A8,
	0x94D7, 0x65AA,
	0x94D8, 0x65AC,
	0x94D9, 0x65AE,
	0x94E7, 0x65C2,
	0x94EC, 0x65CD,
	0x94FA, 0x65E1,
	0x954B, 0x6601,
	0x9551, 0x660B,
	0x9552, 0x660D,
	0x955C, 0x661E,
	0x9561, 0x6626,
	0x9566, 0x662E,
	0x9567, 0x6630,
	0x956F, 0x663D,
	0x9572, 0x6642,
	0x957E, 0x6658,
	0x9580, 0x6659,
	0x9585, 0x6660,
	0x9588, 0x6665,
	0x9589, 0x6667,
	0x9592, 0x6675,
	0x959B, 0x6683,
	0x95E4, 0x66DA,
	0x95F5, 0x66F1,
	0x95F8, 0x66F8,
	0x95FB, 0x66FD,
	0x9644, 0x670C,
	0x964A, 0x6716,
	0x964E, 0x671C,
	0x964F, 0x671E,
	0x9656, 0x6727,
	0x9657, 0x6729,
	0x9658, 0x672E,
	0x9659, 0x6730,
	0x9664, 0x6741,
	0x9667, 0x6747,
	0x966A, 0x674D,
	0x966B, 0x6752,
	0x9673, 0x675D,
	0x967B, 0x676E,
	0x967C, 0x6771,
	0x967D, 0x6774,
	0x967E, 0x6776,
	0x9684, 0x677D,
	0x9685, 0x6780,
	0x968A, 0x6788,
	0x968B, 0x678A,
	0x9694, 0x6796,
	0x9695, 0x6799,
	0x9696, 0x679B,
	0x969A, 0x67A4,
	0x969B, 0x67A6,
	0x969C, 0x67A9,
	0x969D, 0x67AC,
	0x969E, 0x67AE,
	0x96A1, 0x67B4,
	0x96AA, 0x67C2,
	0x96B8, 0x67DB,
	0x96B9, 0x67DF,
	0x96BA, 0x67E1,
	0x96C4, 0x67F2,
	0x96CD, 0x67FE,
	0x96D2, 0x6806,
	0x96D3, 0x680D,
	0x96D4, 0x6810,
	0x96D5, 0x6812,
	0x96F3, 0x683F,
	0x96F4, 0x6847,
	0x96F5, 0x684B,
	0x96F6, 0x684D,
	0x96F7, 0x684F,
	0x96F8, 0x6852,
	0x9744, 0x686A,
	0x974D, 0x6875,
	0x9757, 0x6882,
	0x9758, 0x6884,
	0x9778, 0x68AE,
	0x977B, 0x68B4,
	0x9787, 0x68C1,
	0x978E, 0x68CA,
	0x978F, 0x68CC,
	0x9798, 0x68D9,
	0x97AA, 0x68EF,
	0x97B1, 0x68FB,
	0x97BE, 0x690C,
	0x97BF, 0x690F,
	0x97C0, 0x6911,
	0x97E4, 0x693E,
	0x97FE, 0x695F,
	0x9856, 0x6981,
	0x9857, 0x6983,
	0x9858, 0x6985,
	0x9872, 0x69AC,
	0x988B, 0x69CB,
	0x988C, 0x69CD,
	0x988D, 0x69CF,
	0x98B4, 0x69FE,
	0x98D1, 0x6A20,
	0x98D8, 0x6A29,
	0x98DD, 0x6A30,
	0x98FE, 0x6A5A,
	0x996B, 0x6A8F,
	0x9982, 0x6AAA,
	0x9A47, 0x6B38,
	0x9A51, 0x6B48,
	0x9A78, 0x6B7A,
	0x9A7D, 0x6B85,
	0x9A7E, 0x6B88,
	0x9A80, 0x6B8C,
	0x9A9F, 0x6BB6,
	0x9AA7, 0x6BC0,
	0x9AAF, 0x6BCC,
	0x9AB0, 0x6BCE,
	0x9AB3, 0x6BD8,
	0x9AB4, 0x6BDA,
	0x9AC8, 0x6BF4,
	0x9ADB, 0x6C0E,
	0x9ADC, 0x6C12,
	0x9ADD, 0x6C17,
	0x9AE1, 0x6C20,
	0x9AE2, 0x6C23,
	0x9AE3, 0x6C25,
	0x9AE7, 0x6C31,
	0x9AE8, 0x6C33,
	0x9AF4, 0x6C48,
	0x9AFD, 0x6C56,
	0x9AFE, 0x6C58,
	0x9B4C, 0x6C71,
	0x9B4D, 0x6C73,
	0x9B4E, 0x6C75,
	0x9B56, 0x6C84,
	0x9B57, 0x6C87,
	0x9B62, 0x6C9A,
	0x9B66, 0x6CA0,
	0x9B67, 0x6CA2,
	0x9B68, 0x6CA8,
	0x9B69, 0x6CAC,
	0x9B70, 0x6CBA,
	0x9B78, 0x6CCB,
	0x9B7E, 0x6CD8,
	0x9B84, 0x6CDF,
	0x9B85, 0x6CE4,
	0x9B88, 0x6CE9,
	0x9B8B, 0x6CF2,
	0x9B8C, 0x6CF4,
	0x9B8D, 0x6CF9,
	0x9B97, 0x6D0D,
	0x9B9F, 0x6D18,
	0x9BA8, 0x6D26,
	0x9BAF, 0x6D34,
	0x9BB3, 0x6D3A,
	0x9BB6, 0x6D42,
	0x9BB7, 0x6D44,
	0x9BB8, 0x6D49,
	0x9BB9, 0x6D4C,
	0x9BBA, 0x6D50,
	0x9BBF, 0x6D5B,
	0x9BC0, 0x6D5D,
	0x9BC1, 0x6D5F,
	0x9BDF, 0x6D8D,
	0x9BE2, 0x6D92,
	0x9BE8, 0x6D9C,
	0x9BE9, 0x6DA2,
	0x9BEA, 0x6DA5,
	0x9C48, 0x6DD7,
	0x9C4C, 0x6DDF,
	0x9C4F, 0x6DE5,
	0x9C54, 0x6DED,
	0x9C57, 0x6DF2,
	0x9C5B, 0x6DF8,
	0x9C5C, 0x6DFA,
	0x9C69, 0x6E0B,
	0x9C6A, 0x6E0F,
	0x9C6D, 0x6E15,
	0x9C74, 0x6E22,
	0x9C78, 0x6E2A,
	0x9C79, 0x6E2C,
	0x9C7A, 0x6E2E,
	0x9C7D, 0x6E33,
	0x9C7E, 0x6E35,
	0x9C82, 0x6E39,
	0x9C97, 0x6E55,
	0x9C98, 0x6E57,
	0x9CBD, 0x6E84,
	0x9CD5, 0x6EA6,
	0x9CDC, 0x6EB0,
	0x9CDD, 0x6EB3,
	0x9CDE, 0x6EB5,
	0x9CE1, 0x6EBC,
	0x9CEF, 0x6ED0,
	0x9CF0, 0x6ED2,
	0x9CF1, 0x6ED6,
	0x9CF7, 0x6EE3,
	0x9CF8, 0x6EE7,
	0x9D6E, 0x6F2C,
	0x9D6F, 0x6F2E,
	0x9D70, 0x6F30,
	0x9D71, 0x6F32,
	0x9D86, 0x6F4C,
	0x9D94, 0x6F5D,
	0x9DA4, 0x6F73,
	0x9DA8, 0x6F79,
	0x9DA9, 0x6F7B,
	0x9DE1, 0x6FC1,
	0x9DFA, 0x6FDF,
	0x9EBB, 0x706E,
	0x9EC0, 0x7077,
	0x9EC4, 0x707D,
	0x9ED2, 0x7093,
	0x9EE4, 0x70B0,
	0x9EE5, 0x70B2,
	0x9EE9, 0x70BA,
	0x9EF0, 0x70C9,
	0x9EFE, 0x70DA,
	0x9F47, 0x70E5,
	0x9F48, 0x70EA,
	0x9F49, 0x70EE,
	0x9F51, 0x70F8,
	0x9F67, 0x7114,
	0x9F68, 0x7117,
	0x9F80, 0x7135,
	0x9F93, 0x714B,
	0x9F94, 0x714D,
	0x9FA2, 0x715D,
	0x9FA8, 0x7165,
	0x9FB5, 0x7179,
	0x9FE3, 0x71B4,
	0xA04E, 0x71E6,
	0xA08C, 0x7229,
	0xA08D, 0x722B,
	0xA094, 0x723A,
	0xA095, 0x723C,
	0xA096, 0x723E,
	0xA0AA, 0x725A,
	0xA0AB, 0x725C,
	0xA0AC, 0x725E,
	0xA0AD, 0x7260,
	0xA0B1, 0x7268,
	0xA0C7, 0x728C,
	0xA0C8, 0x728E,
	0xA0E3, 0x72AE,
	0xA0E7, 0x72B5,
	0xA0F6, 0x72CF,
	0xA0F7, 0x72D1,
	0xA0FC, 0x72D8,
	0xA1A4, 0x00B7,
	0xA1A5, 0x02C9,
	0xA1A6, 0x02C7,
	0xA1A7, 0x00A8,
	0xA1A8, 0x3003,
	0xA1A9, 0x3005,
	0xA1AA, 0x2014,
	0xA1AB, 0xFF5E,
	0xA1AC, 0x2016,
	0xA1AD, 0x2026,
	0xA1C0, 0x00B1,
	0xA1C1, 0x00D7,
	0xA1C2, 0x00F7,
	0xA1C3, 0x2236,
	0xA1C6, 0x2211,
	0xA1C7, 0x220F,
	0xA1C8, 0x222A,
	0xA1C9, 0x2229,
	0xA1CA, 0x2208,
	0xA1CB, 0x2237,
	0xA1CC, 0x221A,
	0xA1CD, 0x22A5,
	0xA1CE, 0x2225,
	0xA1CF, 0x2220,
	0xA1D0, 0x2312,
	0xA1D1, 0x2299,
	0xA1D2, 0x222B,
	0xA1D3, 0x222E,
	0xA1D4, 0x2261,
	0xA1D5, 0x224C,
	0xA1D6, 0x2248,
	0xA1D7, 0x223D,
	0xA1D8, 0x221D,
	0xA1D9, 0x2260,
	0xA1DE, 0x221E,
	0xA1DF, 0x2235,
	0xA1E0, 0x2234,
	0xA1E1, 0x2642,
	0xA1E2, 0x2640,
	0xA1E3, 0x00B0,
	0xA1E6, 0x2103,
	0xA1E7, 0xFF04,
	0xA1E8, 0x00A4,
	0xA1EB, 0x2030,
	0xA1EC, 0x00A7,
	0xA1ED, 0x2116,
	0xA1EE, 0x2606,
	0xA1EF, 0x2605,
	0xA1F0, 0x25CB,
	0xA1F1, 0x25CF,
	0xA1F2, 0x25CE,
	0xA1F3, 0x25C7,
	0xA1F4, 0x25C6,
	0xA1F5, 0x25A1,
	0xA1F6, 0x25A0,
	0xA1F7, 0x25B3,
	0xA1F8, 0x25B2,
	0xA1F9, 0x203B,
	0xA1FA, 0x2192,
	0xA1FD, 0x2193,
	0xA1FE, 0x3013,
	0xA3A4, 0xFFE5,
	0xA3FE, 0xFFE3,
	0xA6F2, 0xFE31,
	0xA7A7, 0x0401,
	0xA7D7, 0x0451,
	0xA842, 0x02D9,
	0xA843, 0x2013,
	0xA844, 0x2015,
	0xA845, 0x2025,
	0xA846, 0x2035,
	0xA847, 0x2105,
	0xA848, 0x2109,
	0xA84D, 0x2215,
	0xA84E, 0x221F,
	0xA84F, 0x2223,
	0xA850, 0x2252,
	0xA853, 0x22BF,
	0xA891, 0x2609,
	0xA892, 0x2295,
	0xA893, 0x3012,
	0xA8A1, 0x0101,
	0xA8A2, 0x00E1,
	0xA8A3, 0x01CE,
	0xA8A4, 0x00E0,
	0xA8A5, 0x0113,
	0xA8A6, 0x00E9,
	0xA8A7, 0x011B,
	0xA8A8, 0x00E8,
	0xA8A9, 0x012B,
	0xA8AA, 0x00ED,
	0xA8AB, 0x01D0,
	0xA8AC, 0x00EC,
	0xA8AD, 0x014D,
	0xA8AE, 0x00F3,
	0xA8AF, 0x01D2,
	0xA8B0, 0x00F2,
	0xA8B1, 0x016B,
	0xA8B2, 0x00FA,
	0xA8B3, 0x01D4,
	0xA8B4, 0x00F9,
	0xA8B5, 0x01D6,
	0xA8B6, 0x01D8,
	0xA8B7, 0x01DA,
	0xA8B8, 0x01DC,
	0xA8B9, 0x00FC,
	0xA8BA, 0x00EA,
	0xA8BB, 0x0251,
	0xA8BC, 0xE7C7,
	0xA8BD, 0x0144,
	0xA8BE, 0x0148,
	0xA8BF, 0xE7C8,
	0xA8C0, 0x0261,
	0xA949, 0x32A3,
	0xA94F, 0x33A1,
	0xA950, 0x33C4,
	0xA951, 0x33CE,
	0xA954, 0x33D5,
	0xA955, 0xFE30,
	0xA956, 0xFFE2,
	0xA957, 0xFFE4,
	0xA959, 0x2121,
	0xA95A, 0x3231,
	0xA95C, 0x2010,
	0xA960, 0x30FC,
	0xA965, 0x3006,
	0xA996, 0x3007,
	0xAA42, 0x72DF,
	0xAA4D, 0x72F9,
	0xAA52, 0x7302,
	0xAA60, 0x7314,
	0xAA6B, 0x732D,
	0xAA86, 0x7351,
	0xAA9E, 0x736E,
	0xAB53, 0x7388,
	0xAB54, 0x738A,
	0xAB6C, 0x73AA,
	0xAB6F, 0x73B1,
	0xAB79, 0x73C1,
	0xAB82, 0x73CE,
	0xAB8E, 0x73DF,
	0xAB93, 0x73E6,
	0xAB94, 0x73E8,
	0xAC4B, 0x7404,
	0xAC63, 0x7427,
	0xAC64, 0x7429,
	0xAC65, 0x742B,
	0xAC66, 0x742D,
	0xAC67, 0x742F,
	0xAC87, 0x7456,
	0xAC88, 0x7458,
	0xAC89, 0x745D,
	0xAD43, 0x747F,
	0xAD44, 0x7482,
	0xAD4D, 0x748F,
	0xAD59, 0x749D,
	0xAD93, 0x74DD,
	0xAD94, 0x74DF,
	0xAD95, 0x74E1,
	0xAD96, 0x74E5,
	0xAE40, 0x74F3,
	0xAE41, 0x74F5,
	0xAE55, 0x750E,
	0xAE56, 0x7510,
	0xAE57, 0x7512,
	0xAE5C, 0x751B,
	0xAE66, 0x752A,
	0xAE67, 0x752E,
	0xAE68, 0x7534,
	0xAE69, 0x7536,
	0xAE6A, 0x7539,
	0xAE6D, 0x753F,
	0xAE76, 0x754D,
	0xAE92, 0x7573,
	0xAEA0, 0x7587,
	0xAF46, 0x7590,
	0xAF47, 0x7593,
	0xAF48, 0x7595,
	0xAF49, 0x7598,
	0xAF4C, 0x759E,
	0xAF4D, 0x75A2,
	0xAF53, 0x75AD,
	0xAF5B, 0x75C6,
	0xAF62, 0x75D3,
	0xAF63, 0x75D7,
	0xAF6B, 0x75E5,
	0xAF6C, 0x75E9,
	0xAF7B, 0x7602,
	0xAF7C, 0x7604,
	0xAF82, 0x760B,
	0xAF8A, 0x7616,
	0xAF8B, 0x761A,
	0xAF8F, 0x7621,
	0xAF90, 0x7623,
	0xAF93, 0x762C,
	0xAF9D, 0x763D,
	0xAFA0, 0x7644,
	0xB04D, 0x7655,
	0xB053, 0x765D,
	0xB06C, 0x767C,
	0xB070, 0x7683,
	0xB071, 0x7685,
	0xB078, 0x7692,
	0xB093, 0x76B3,
	0xB0A0, 0x76C3,
	0xB0A1, 0x554A,
	0xB0A2, 0x963F,
	0xB0A3, 0x57C3,
	0xB0A4, 0x6328,
	0xB0A5, 0x54CE,
	0xB0A6, 0x5509,
	0xB0A7, 0x54C0,
	0xB0A8, 0x7691,
	0xB0A9, 0x764C,
	0xB0AA, 0x853C,
	0xB0AB, 0x77EE,
	0xB0AC, 0x827E,
	0xB0AD, 0x788D,
	0xB0AE, 0x7231,
	0xB0AF, 0x9698,
	0xB0B0, 0x978D,
	0xB0B1, 0x6C28,
	0xB0B2, 0x5B89,
	0xB0B3, 0x4FFA,
	0xB0B4, 0x6309,
	0xB0B5, 0x6697,
	0xB0B6, 0x5CB8,
	0xB0B7, 0x80FA,
	0xB0B8, 0x6848,
	0xB0B9, 0x80AE,
	0xB0BA, 0x6602,
	0xB0BB, 0x76CE,
	0xB0BC, 0x51F9,
	0xB0BD, 0x6556,
	0xB0BE, 0x71AC,
	0xB0BF, 0x7FF1,
	0xB0C0, 0x8884,
	0xB0C1, 0x50B2,
	0xB0C2, 0x5965,
	0xB0C3, 0x61CA,
	0xB0C4, 0x6FB3,
	0xB0C5, 0x82AD,
	0xB0C6, 0x634C,
	0xB0C7, 0x6252,
	0xB0C8, 0x53ED,
	0xB0C9, 0x5427,
	0xB0CA, 0x7B06,
	0xB0CB, 0x516B,
	0xB0CC, 0x75A4,
	0xB0CD, 0x5DF4,
	0xB0CE, 0x62D4,
	0xB0CF, 0x8DCB,
	0xB0D0, 0x9776,
	0xB0D1, 0x628A,
	0xB0D2, 0x8019,
	0xB0D3, 0x575D,
	0xB0D4, 0x9738,
	0xB0D5, 0x7F62,
	0xB0D6, 0x7238,
	0xB0D7, 0x767D,
	0xB0D8, 0x67CF,
	0xB0D9, 0x767E,
	0xB0DA, 0x6446,
	0xB0DB, 0x4F70,
	0xB0DC, 0x8D25,
	0xB0DD, 0x62DC,
	0xB0DE, 0x7A17,
	0xB0DF, 0x6591,
	0xB0E0, 0x73ED,
	0xB0E1, 0x642C,
	0xB0E2, 0x6273,
	0xB0E3, 0x822C,
	0xB0E4, 0x9881,
	0xB0E5, 0x677F,
	0xB0E6, 0x7248,
	0xB0E7, 0x626E,
	0xB0E8, 0x62CC,
	0xB0E9, 0x4F34,
	0xB0EA, 0x74E3,
	0xB0EB, 0x534A,
	0xB0EC, 0x529E,
	0xB0ED, 0x7ECA,
	0xB0EE, 0x90A6,
	0xB0EF, 0x5E2E,
	0xB0F0, 0x6886,
	0xB0F1, 0x699C,
	0xB0F2, 0x8180,
	0xB0F3, 0x7ED1,
	0xB0F4, 0x68D2,
	0xB0F5, 0x78C5,
	0xB0F6, 0x868C,
	0xB0F7, 0x9551,
	0xB0F8, 0x508D,
	0xB0F9, 0x8C24,
	0xB0FA, 0x82DE,
	0xB0FB, 0x80DE,
	0xB0FC, 0x5305,
	0xB0FD, 0x8912,
	0xB0FE, 0x5265,
	0xB140, 0x76C4,
	0xB141, 0x76C7,
	0xB142, 0x76C9,
	0xB145, 0x76D3,
	0xB146, 0x76D5,
	0xB159, 0x76F0,
	0xB15A, 0x76F3,
	0xB160, 0x76FD,
	0xB167, 0x770A,
	0xB168, 0x770C,
	0xB178, 0x7721,
	0xB17C, 0x7727,
	0xB180, 0x772C,
	0xB181, 0x772E,
	0xB187, 0x7739,
	0xB188, 0x773B,
	0xB18C, 0x7742,
	0xB1A0, 0x775C,
	0xB1A1, 0x8584,
	0xB1A2, 0x96F9,
	0xB1A3, 0x4FDD,
	0xB1A4, 0x5821,
	0xB1A5, 0x9971,
	0xB1A6, 0x5B9D,
	0xB1A7, 0x62B1,
	0xB1A8, 0x62A5,
	0xB1A9, 0x66B4,
	0xB1AA, 0x8C79,
	0xB1AB, 0x9C8D,
	0xB1AC, 0x7206,
	0xB1AD, 0x676F,
	0xB1AE, 0x7891,
	0xB1AF, 0x60B2,
	0xB1B0, 0x5351,
	0xB1B1, 0x5317,
	0xB1B2, 0x8F88,
	0xB1B3, 0x80CC,
	0xB1B4, 0x8D1D,
	0xB1B5, 0x94A1,
	0xB1B6, 0x500D,
	0xB1B7, 0x72C8,
	0xB1B8, 0x5907,
	0xB1B9, 0x60EB,
	0xB1BA, 0x7119,
	0xB1BB, 0x88AB,
	0xB1BC, 0x5954,
	0xB1BD, 0x82EF,
	0xB1BE, 0x672C,
	0xB1BF, 0x7B28,
	0xB1C0, 0x5D29,
	0xB1C1, 0x7EF7,
	0xB1C2, 0x752D,
	0xB1C3, 0x6CF5,
	0xB1C4, 0x8E66,
	0xB1C5, 0x8FF8,
	0xB1C6, 0x903C,
	0xB1C7, 0x9F3B,
	0xB1C8, 0x6BD4,
	0xB1C9, 0x9119,
	0xB1CA, 0x7B14,
	0xB1CB, 0x5F7C,
	0xB1CC, 0x78A7,
	0xB1CD, 0x84D6,
	0xB1CE, 0x853D,
	0xB1CF, 0x6BD5,
	0xB1D0, 0x6BD9,
	0xB1D1, 0x6BD6,
	0xB1D2, 0x5E01,
	0xB1D3, 0x5E87,
	0xB1D4, 0x75F9,
	0xB1D5, 0x95ED,
	0xB1D6, 0x655D,
	0xB1D7, 0x5F0A,
	0xB1D8, 0x5FC5,
	0xB1D9, 0x8F9F,
	0xB1DA, 0x58C1,
	0xB1DB, 0x81C2,
	0xB1DC, 0x907F,
	0xB1DD, 0x965B,
	0xB1DE, 0x97AD,
	0xB1DF, 0x8FB9,
	0xB1E0, 0x7F16,
	0xB1E1, 0x8D2C,
	0xB1E2, 0x6241,
	0xB1E3, 0x4FBF,
	0xB1E4, 0x53D8,
	0xB1E5, 0x535E,
	0xB1E8, 0x8FAB,
	0xB1E9, 0x904D,
	0xB1EA, 0x6807,
	0xB1EB, 0x5F6A,
	0xB1EC, 0x8198,
	0xB1ED, 0x8868,
	0xB1EE, 0x9CD6,
	0xB1EF, 0x618B,
	0xB1F0, 0x522B,
	0xB1F1, 0x762A,
	0xB1F2, 0x5F6C,
	0xB1F3, 0x658C,
	0xB1F4, 0x6FD2,
	0xB1F5, 0x6EE8,
	0xB1F6, 0x5BBE,
	0xB1F7, 0x6448,
	0xB1F8, 0x5175,
	0xB1F9, 0x51B0,
	0xB1FA, 0x67C4,
	0xB1FB, 0x4E19,
	0xB1FC, 0x79C9,
	0xB1FD, 0x997C,
	0xB1FE, 0x70B3,
	0xB244, 0x7764,
	0xB245, 0x7767,
	0xB26E, 0x77A1,
	0xB271, 0x77A6,
	0xB272, 0x77A8,
	0xB273, 0x77AB,
	0xB279, 0x77B4,
	0xB280, 0x77BC,
	0xB281, 0x77BE,
	0xB2A0, 0x77E4,
	0xB2A1, 0x75C5,
	0xB2A2, 0x5E76,
	0xB2A3, 0x73BB,
	0xB2A4, 0x83E0,
	0xB2A5, 0x64AD,
	0xB2A6, 0x62E8,
	0xB2A7, 0x94B5,
	0xB2A8, 0x6CE2,
	0xB2A9, 0x535A,
	0xB2AA, 0x52C3,
	0xB2AB, 0x640F,
	0xB2AC, 0x94C2,
	0xB2AD, 0x7B94,
	0xB2AE, 0x4F2F,
	0xB2AF, 0x5E1B,
	0xB2B0, 0x8236,
	0xB2B1, 0x8116,
	0xB2B2, 0x818A,
	0xB2B3, 0x6E24,
	0xB2B4, 0x6CCA,
	0xB2B5, 0x9A73,
	0xB2B6, 0x6355,
	0xB2B7, 0x535C,
	0xB2B8, 0x54FA,
	0xB2B9, 0x8865,
	0xB2BA, 0x57E0,
	0xB2BB, 0x4E0D,
	0xB2BC, 0x5E03,
	0xB2BD, 0x6B65,
	0xB2BE, 0x7C3F,
	0xB2BF, 0x90E8,
	0xB2C0, 0x6016,
	0xB2C1, 0x64E6,
	0xB2C2, 0x731C,
	0xB2C3, 0x88C1,
	0xB2C4, 0x6750,
	0xB2C5, 0x624D,
	0xB2C6, 0x8D22,
	0xB2C7, 0x776C,
	0xB2C8, 0x8E29,
	0xB2C9, 0x91C7,
	0xB2CA, 0x5F69,
	0xB2CB, 0x83DC,
	0xB2CC, 0x8521,
	0xB2CD, 0x9910,
	0xB2CE, 0x53C2,
	0xB2CF, 0x8695,
	0xB2D0, 0x6B8B,
	0xB2D1, 0x60ED,
	0xB2D2, 0x60E8,
	0xB2D3, 0x707F,
	0xB2D4, 0x82CD,
	0xB2D5, 0x8231,
	0xB2D6, 0x4ED3,
	0xB2D7, 0x6CA7,
	0xB2D8, 0x85CF,
	0xB2D9, 0x64CD,
	0xB2DA, 0x7CD9,
	0xB2DB, 0x69FD,
	0xB2DC, 0x66F9,
	0xB2DD, 0x8349,
	0xB2DE, 0x5395,
	0xB2DF, 0x7B56,
	0xB2E0, 0x4FA7,
	0xB2E1, 0x518C,
	0xB2E2, 0x6D4B,
	0xB2E3, 0x5C42,
	0xB2E4, 0x8E6D,
	0xB2E5, 0x63D2,
	0xB2E6, 0x53C9,
	0xB2E7, 0x832C,
	0xB2E8, 0x8336,
	0xB2E9, 0x67E5,
	0xB2EA, 0x78B4,
	0xB2EB, 0x643D,
	0xB2EC, 0x5BDF,
	0xB2ED, 0x5C94,
	0xB2EE, 0x5DEE,
	0xB2EF, 0x8BE7,
	0xB2F0, 0x62C6,
	0xB2F1, 0x67F4,
	0xB2F2, 0x8C7A,
	0xB2F3, 0x6400,
	0xB2F4, 0x63BA,
	0xB2F5, 0x8749,
	0xB2F6, 0x998B,
	0xB2F7, 0x8C17,
	0xB2F8, 0x7F20,
	0xB2F9, 0x94F2,
	0xB2FA, 0x4EA7,
	0xB2FB, 0x9610,
	0xB2FC, 0x98A4,
	0xB2FD, 0x660C,
	0xB2FE, 0x7316,
	0xB340, 0x77E6,
	0xB341, 0x77E8,
	0xB342, 0x77EA,
	0xB349, 0x77F7,
	0xB359, 0x7813,
	0xB35A, 0x7815,
	0xB35B, 0x7819,
	0xB35C, 0x781B,
	0xB35D, 0x781E,
	0xB361, 0x7824,
	0xB362, 0x7828,
	0xB36C, 0x783D,
	0xB36D, 0x783F,
	0xB372, 0x7846,
	0xB377, 0x784D,
	0xB378, 0x784F,
	0xB379, 0x7851,
	0xB3A1, 0x573A,
	0xB3A2, 0x5C1D,
	0xB3A3, 0x5E38,
	0xB3A4, 0x957F,
	0xB3A5, 0x507F,
	0xB3A6, 0x80A0,
	0xB3A7, 0x5382,
	0xB3A8, 0x655E,
	0xB3A9, 0x7545,
	0xB3AA, 0x5531,
	0xB3AB, 0x5021,
	0xB3AC, 0x8D85,
	0xB3AD, 0x6284,
	0xB3AE, 0x949E,
	0xB3AF, 0x671D,
	0xB3B0, 0x5632,
	0xB3B1, 0x6F6E,
	0xB3B2, 0x5DE2,
	0xB3B3, 0x5435,
	0xB3B4, 0x7092,
	0xB3B5, 0x8F66,
	0xB3B6, 0x626F,
	0xB3B7, 0x64A4,
	0xB3B8, 0x63A3,
	0xB3B9, 0x5F7B,
	0xB3BA, 0x6F88,
	0xB3BB, 0x90F4,
	0xB3BC, 0x81E3,
	0xB3BD, 0x8FB0,
	0xB3BE, 0x5C18,
	0xB3BF, 0x6668,
	0xB3C0, 0x5FF1,
	0xB3C1, 0x6C89,
	0xB3C2, 0x9648,
	0xB3C3, 0x8D81,
	0xB3C4, 0x886C,
	0xB3C5, 0x6491,
	0xB3C6, 0x79F0,
	0xB3C7, 0x57CE,
	0xB3C8, 0x6A59,
	0xB3C9, 0x6210,
	0xB3CA, 0x5448,
	0xB3CB, 0x4E58,
	0xB3CC, 0x7A0B,
	0xB3CD, 0x60E9,
	0xB3CE, 0x6F84,
	0xB3CF, 0x8BDA,
	0xB3D0, 0x627F,
	0xB3D1, 0x901E,
	0xB3D2, 0x9A8B,
	0xB3D3, 0x79E4,
	0xB3D4, 0x5403,
	0xB3D5, 0x75F4,
	0xB3D6, 0x6301,
	0xB3D7, 0x5319,
	0xB3D8, 0x6C60,
	0xB3D9, 0x8FDF,
	0xB3DA, 0x5F1B,
	0xB3DB, 0x9A70,
	0xB3DC, 0x803B,
	0xB3DD, 0x9F7F,
	0xB3DE, 0x4F88,
	0xB3DF, 0x5C3A,
	0xB3E0, 0x8D64,
	0xB3E1, 0x7FC5,
	0xB3E2, 0x65A5,
	0xB3E3, 0x70BD,
	0xB3E4, 0x5145,
	0xB3E5, 0x51B2,
	0xB3E6, 0x866B,
	0xB3E7, 0x5D07,
	0xB3E8, 0x5BA0,
	0xB3E9, 0x62BD,
	0xB3EA, 0x916C,
	0xB3EB, 0x7574,
	0xB3EC, 0x8E0C,
	0xB3ED, 0x7A20,
	0xB3EE, 0x6101,
	0xB3EF, 0x7B79,
	0xB3F0, 0x4EC7,
	0xB3F1, 0x7EF8,
	0xB3F2, 0x7785,
	0xB3F3, 0x4E11,
	0xB3F4, 0x81ED,
	0xB3F5, 0x521D,
	0xB3F6, 0x51FA,
	0xB3F7, 0x6A71,
	0xB3F8, 0x53A8,
	0xB3F9, 0x8E87,
	0xB3FA, 0x9504,
	0xB3FB, 0x96CF,
	0xB3FC, 0x6EC1,
	0xB3FD, 0x9664,
	0xB3FE, 0x695A,
	0xB443, 0x7888,
	0xB448, 0x7892,
	0xB44C, 0x7899,
	0xB44F, 0x78A0,
	0xB450, 0x78A2,
	0xB451, 0x78A4,
	0xB452, 0x78A6,
	0xB48C, 0x78F3,
	0xB4A1, 0x7840,
	0xB4A2, 0x50A8,
	0xB4A3, 0x77D7,
	0xB4A4, 0x6410,
	0xB4A5, 0x89E6,
	0xB4A6, 0x5904,
	0xB4A7, 0x63E3,
	0xB4A8, 0x5DDD,
	0xB4A9, 0x7A7F,
	0xB4AA, 0x693D,
	0xB4AB, 0x4F20,
	0xB4AC, 0x8239,
	0xB4AD, 0x5598,
	0xB4AE, 0x4E32,
	0xB4AF, 0x75AE,
	0xB4B0, 0x7A97,
	0xB4B1, 0x5E62,
	0xB4B2, 0x5E8A,
	0xB4B3, 0x95EF,
	0xB4B4, 0x521B,
	0xB4B5, 0x5439,
	0xB4B6, 0x708A,
	0xB4B7, 0x6376,
	0xB4B8, 0x9524,
	0xB4B9, 0x5782,
	0xB4BA, 0x6625,
	0xB4BB, 0x693F,
	0xB4BC, 0x9187,
	0xB4BD, 0x5507,
	0xB4BE, 0x6DF3,
	0xB4BF, 0x7EAF,
	0xB4C0, 0x8822,
	0xB4C1, 0x6233,
	0xB4C2, 0x7EF0,
	0xB4C3, 0x75B5,
	0xB4C4, 0x8328,
	0xB4C5, 0x78C1,
	0xB4C6, 0x96CC,
	0xB4C7, 0x8F9E,
	0xB4C8, 0x6148,
	0xB4C9, 0x74F7,
	0xB4CA, 0x8BCD,
	0xB4CB, 0x6B64,
	0xB4CC, 0x523A,
	0xB4CD, 0x8D50,
	0xB4CE, 0x6B21,
	0xB4CF, 0x806A,
	0xB4D0, 0x8471,
	0xB4D1, 0x56F1,
	0xB4D2, 0x5306,
	0xB4D3, 0x4ECE,
	0xB4D4, 0x4E1B,
	0xB4D5, 0x51D1,
	0xB4D6, 0x7C97,
	0xB4D7, 0x918B,
	0xB4D8, 0x7C07,
	0xB4D9, 0x4FC3,
	0xB4DA, 0x8E7F,
	0xB4DB, 0x7BE1,
	0xB4DC, 0x7A9C,
	0xB4DD, 0x6467,
	0xB4DE, 0x5D14,
	0xB4DF, 0x50AC,
	0xB4E0, 0x8106,
	0xB4E1, 0x7601,
	0xB4E2, 0x7CB9,
	0xB4E3, 0x6DEC,
	0xB4E4, 0x7FE0,
	0xB4E5, 0x6751,
	0xB4E6, 0x5B58,
	0xB4E7, 0x5BF8,
	0xB4E8, 0x78CB,
	0xB4E9, 0x64AE,
	0xB4EA, 0x6413,
	0xB4EB, 0x63AA,
	0xB4EC, 0x632B,
	0xB4ED, 0x9519,
	0xB4EE, 0x642D,
	0xB4EF, 0x8FBE,
	0xB4F0, 0x7B54,
	0xB4F1, 0x7629,
	0xB4F2, 0x6253,
	0xB4F3, 0x5927,
	0xB4F4, 0x5446,
	0xB4F5, 0x6B79,
	0xB4F6, 0x50A3,
	0xB4F7, 0x6234,
	0xB4F8, 0x5E26,
	0xB4F9, 0x6B86,
	0xB4FA, 0x4EE3,
	0xB4FB, 0x8D37,
	0xB4FC, 0x888B,
	0xB4FD, 0x5F85,
	0xB4FE, 0x902E,
	0xB569, 0x793D,
	0xB56A, 0x793F,
	0xB56F, 0x7947,
	0xB57D, 0x7961,
	0xB57E, 0x7963,
	0xB580, 0x7964,
	0xB581, 0x7966,
	0xB586, 0x796E,
	0xB58E, 0x7979,
	0xB5A1, 0x6020,
	0xB5A2, 0x803D,
	0xB5A3, 0x62C5,
	0xB5A4, 0x4E39,
	0xB5A5, 0x5355,
	0xB5A6, 0x90F8,
	0xB5A7, 0x63B8,
	0xB5A8, 0x80C6,
	0xB5A9, 0x65E6,
	0xB5AA, 0x6C2E,
	0xB5AB, 0x4F46,
	0xB5AC, 0x60EE,
	0xB5AD, 0x6DE1,
	0xB5AE, 0x8BDE,
	0xB5AF, 0x5F39,
	0xB5B0, 0x86CB,
	0xB5B1, 0x5F53,
	0xB5B2, 0x6321,
	0xB5B3, 0x515A,
	0xB5B4, 0x8361,
	0xB5B5, 0x6863,
	0xB5B6, 0x5200,
	0xB5B7, 0x6363,
	0xB5B8, 0x8E48,
	0xB5B9, 0x5012,
	0xB5BA, 0x5C9B,
	0xB5BB, 0x7977,
	0xB5BC, 0x5BFC,
	0xB5BD, 0x5230,
	0xB5BE, 0x7A3B,
	0xB5BF, 0x60BC,
	0xB5C0, 0x9053,
	0xB5C1, 0x76D7,
	0xB5C2, 0x5FB7,
	0xB5C3, 0x5F97,
	0xB5C4, 0x7684,
	0xB5C5, 0x8E6C,
	0xB5C6, 0x706F,
	0xB5C7, 0x767B,
	0xB5C8, 0x7B49,
	0xB5C9, 0x77AA,
	0xB5CA, 0x51F3,
	0xB5CB, 0x9093,
	0xB5CC, 0x5824,
	0xB5CD, 0x4F4E,
	0xB5CE, 0x6EF4,
	0xB5CF, 0x8FEA,
	0xB5D0, 0x654C,
	0xB5D1, 0x7B1B,
	0xB5D2, 0x72C4,
	0xB5D3, 0x6DA4,
	0xB5D4, 0x7FDF,
	0xB5D5, 0x5AE1,
	0xB5D6, 0x62B5,
	0xB5D7, 0x5E95,
	0xB5D8, 0x5730,
	0xB5D9, 0x8482,
	0xB5DA, 0x7B2C,
	0xB5DB, 0x5E1D,
	0xB5DC, 0x5F1F,
	0xB5DD, 0x9012,
	0xB5DE, 0x7F14,
	0xB5DF, 0x98A0,
	0xB5E0, 0x6382,
	0xB5E1, 0x6EC7,
	0xB5E2, 0x7898,
	0xB5E3, 0x70B9,
	0xB5E4, 0x5178,
	0xB5E5, 0x975B,
	0xB5E6, 0x57AB,
	0xB5E7, 0x7535,
	0xB5E8, 0x4F43,
	0xB5E9, 0x7538,
	0xB5EA, 0x5E97,
	0xB5EB, 0x60E6,
	0xB5EC, 0x5960,
	0xB5ED, 0x6DC0,
	0xB5EE, 0x6BBF,
	0xB5EF, 0x7889,
	0xB5F0, 0x53FC,
	0xB5F1, 0x96D5,
	0xB5F2, 0x51CB,
	0xB5F3, 0x5201,
	0xB5F4, 0x6389,
	0xB5F5, 0x540A,
	0xB5F6, 0x9493,
	0xB5F7, 0x8C03,
	0xB5F8, 0x8DCC,
	0xB5F9, 0x7239,
	0xB5FA, 0x789F,
	0xB5FB, 0x8776,
	0xB5FC, 0x8FED,
	0xB5FD, 0x8C0D,
	0xB5FE, 0x53E0,
	0xB663, 0x79BC,
	0xB664, 0x79BF,
	0xB665, 0x79C2,
	0xB66A, 0x79CA,
	0xB66B, 0x79CC,
	0xB67C, 0x79E5,
	0xB67D, 0x79E8,
	0xB67E, 0x79EA,
	0xB680, 0x79EC,
	0xB681, 0x79EE,
	0xB68B, 0x79FC,
	0xB68E, 0x7A01,
	0xB695, 0x7A0C,
	0xB6A1, 0x4E01,
	0xB6A2, 0x76EF,
	0xB6A3, 0x53EE,
	0xB6A4, 0x9489,
	0xB6A5, 0x9876,
	0xB6A6, 0x9F0E,
	0xB6A7, 0x952D,
	0xB6A8, 0x5B9A,
	0xB6A9, 0x8BA2,
	0xB6AA, 0x4E22,
	0xB6AB, 0x4E1C,
	0xB6AC, 0x51AC,
	0xB6AD, 0x8463,
	0xB6AE, 0x61C2,
	0xB6AF, 0x52A8,
	0xB6B0, 0x680B,
	0xB6B1, 0x4F97,
	0xB6B2, 0x606B,
	0xB6B3, 0x51BB,
	0xB6B4, 0x6D1E,
	0xB6B5, 0x515C,
	0xB6B6, 0x6296,
	0xB6B7, 0x6597,
	0xB6B8, 0x9661,
	0xB6B9, 0x8C46,
	0xB6BA, 0x9017,
	0xB6BB, 0x75D8,
	0xB6BC, 0x90FD,
	0xB6BD, 0x7763,
	0xB6BE, 0x6BD2,
	0xB6BF, 0x728A,
	0xB6C0, 0x72EC,
	0xB6C1, 0x8BFB,
	0xB6C2, 0x5835,
	0xB6C3, 0x7779,
	0xB6C4, 0x8D4C,
	0xB6C5, 0x675C,
	0xB6C6, 0x9540,
	0xB6C7, 0x809A,
	0xB6C8, 0x5EA6,
	0xB6C9, 0x6E21,
	0xB6CA, 0x5992,
	0xB6CB, 0x7AEF,
	0xB6CC, 0x77ED,
	0xB6CD, 0x953B,
	0xB6CE, 0x6BB5,
	0xB6CF, 0x65AD,
	0xB6D0, 0x7F0E,
	0xB6D1, 0x5806,
	0xB6D2, 0x5151,
	0xB6D3, 0x961F,
	0xB6D4, 0x5BF9,
	0xB6D5, 0x58A9,
	0xB6D6, 0x5428,
	0xB6D7, 0x8E72,
	0xB6D8, 0x6566,
	0xB6D9, 0x987F,
	0xB6DA, 0x56E4,
	0xB6DB, 0x949D,
	0xB6DC, 0x76FE,
	0xB6DD, 0x9041,
	0xB6DE, 0x6387,
	0xB6DF, 0x54C6,
	0xB6E0, 0x591A,
	0xB6E1, 0x593A,
	0xB6E2, 0x579B,
	0xB6E3, 0x8EB2,
	0xB6E4, 0x6735,
	0xB6E5, 0x8DFA,
	0xB6E6, 0x8235,
	0xB6E7, 0x5241,
	0xB6E8, 0x60F0,
	0xB6E9, 0x5815,
	0xB6EA, 0x86FE,
	0xB6EB, 0x5CE8,
	0xB6EC, 0x9E45,
	0xB6ED, 0x4FC4,
	0xB6EE, 0x989D,
	0xB6EF, 0x8BB9,
	0xB6F0, 0x5A25,
	0xB6F1, 0x6076,
	0xB6F2, 0x5384,
	0xB6F3, 0x627C,
	0xB6F4, 0x904F,
	0xB6F5, 0x9102,
	0xB6F6, 0x997F,
	0xB6F7, 0x6069,
	0xB6F8, 0x800C,
	0xB6F9, 0x513F,
	0xB6FA, 0x8033,
	0xB6FB, 0x5C14,
	0xB6FC, 0x9975,
	0xB6FD, 0x6D31,
	0xB6FE, 0x4E8C,
	0xB740, 0x7A1D,
	0xB741, 0x7A1F,
	0xB756, 0x7A38,
	0xB757, 0x7A3A,
	0xB758, 0x7A3E,
	0xB78A, 0x7A75,
	0xB78F, 0x7A82,
	0xB790, 0x7A85,
	0xB791, 0x7A87,
	0xB79E, 0x7A9E,
	0xB7A1, 0x8D30,
	0xB7A2, 0x53D1,
	0xB7A3, 0x7F5A,
	0xB7A4, 0x7B4F,
	0xB7A5, 0x4F10,
	0xB7A6, 0x4E4F,
	0xB7A7, 0x9600,
	0xB7A8, 0x6CD5,
	0xB7A9, 0x73D0,
	0xB7AA, 0x85E9,
	0xB7AB, 0x5E06,
	0xB7AC, 0x756A,
	0xB7AD, 0x7FFB,
	0xB7AE, 0x6A0A,
	0xB7AF, 0x77FE,
	0xB7B0, 0x9492,
	0xB7B1, 0x7E41,
	0xB7B2, 0x51E1,
	0xB7B3, 0x70E6,
	0xB7B4, 0x53CD,
	0xB7B5, 0x8FD4,
	0xB7B6, 0x8303,
	0xB7B7, 0x8D29,
	0xB7B8, 0x72AF,
	0xB7B9, 0x996D,
	0xB7BA, 0x6CDB,
	0xB7BB, 0x574A,
	0xB7BC, 0x82B3,
	0xB7BD, 0x65B9,
	0xB7BE, 0x80AA,
	0xB7BF, 0x623F,
	0xB7C0, 0x9632,
	0xB7C1, 0x59A8,
	0xB7C2, 0x4EFF,
	0xB7C3, 0x8BBF,
	0xB7C4, 0x7EBA,
	0xB7C5, 0x653E,
	0xB7C6, 0x83F2,
	0xB7C7, 0x975E,
	0xB7C8, 0x5561,
	0xB7C9, 0x98DE,
	0xB7CA, 0x80A5,
	0xB7CB, 0x532A,
	0xB7CC, 0x8BFD,
	0xB7CD, 0x5420,
	0xB7CE, 0x80BA,
	0xB7CF, 0x5E9F,
	0xB7D0, 0x6CB8,
	0xB7D1, 0x8D39,
	0xB7D2, 0x82AC,
	0xB7D3, 0x915A,
	0xB7D4, 0x5429,
	0xB7D5, 0x6C1B,
	0xB7D6, 0x5206,
	0xB7D7, 0x7EB7,
	0xB7D8, 0x575F,
	0xB7D9, 0x711A,
	0xB7DA, 0x6C7E,
	0xB7DB, 0x7C89,
	0xB7DC, 0x594B,
	0xB7DD, 0x4EFD,
	0xB7DE, 0x5FFF,
	0xB7DF, 0x6124,
	0xB7E0, 0x7CAA,
	0xB7E1, 0x4E30,
	0xB7E2, 0x5C01,
	0xB7E3, 0x67AB,
	0xB7E4, 0x8702,
	0xB7E5, 0x5CF0,
	0xB7E6, 0x950B,
	0xB7E7, 0x98CE,
	0xB7E8, 0x75AF,
	0xB7E9, 0x70FD,
	0xB7EA, 0x9022,
	0xB7EB, 0x51AF,
	0xB7EC, 0x7F1D,
	0xB7ED, 0x8BBD,
	0xB7EE, 0x5949,
	0xB7EF, 0x51E4,
	0xB7F0, 0x4F5B,
	0xB7F1, 0x5426,
	0xB7F2, 0x592B,
	0xB7F3, 0x6577,
	0xB7F4, 0x80A4,
	0xB7F5, 0x5B75,
	0xB7F6, 0x6276,
	0xB7F7, 0x62C2,
	0xB7F8, 0x8F90,
	0xB7F9, 0x5E45,
	0xB7FA, 0x6C1F,
	0xB7FB, 0x7B26,
	0xB7FC, 0x4F0F,
	0xB7FD, 0x4FD8,
	0xB7FE, 0x670D,
	0xB842, 0x7AA7,
	0xB873, 0x7AE4,
	0xB87A, 0x7AEE,
	0xB887, 0x7AFE,
	0xB88B, 0x7B05,
	0xB88C, 0x7B07,
	0xB88D, 0x7B09,
	0xB891, 0x7B10,
	0xB897, 0x7B1A,
	0xB89A, 0x7B1F,
	0xB89E, 0x7B27,
	0xB89F, 0x7B29,
	0xB8A0, 0x7B2D,
	0xB8A1, 0x6D6E,
	0xB8A2, 0x6DAA,
	0xB8A3, 0x798F,
	0xB8A4, 0x88B1,
	0xB8A5, 0x5F17,
	0xB8A6, 0x752B,
	0xB8A7, 0x629A,
	0xB8A8, 0x8F85,
	0xB8A9, 0x4FEF,
	0xB8AA, 0x91DC,
	0xB8AB, 0x65A7,
	0xB8AC, 0x812F,
	0xB8AD, 0x8151,
	0xB8AE, 0x5E9C,
	0xB8AF, 0x8150,
	0xB8B0, 0x8D74,
	0xB8B1, 0x526F,
	0xB8B2, 0x8986,
	0xB8B3, 0x8D4B,
	0xB8B4, 0x590D,
	0xB8B5, 0x5085,
	0xB8B6, 0x4ED8,
	0xB8B7, 0x961C,
	0xB8B8, 0x7236,
	0xB8B9, 0x8179,
	0xB8BA, 0x8D1F,
	0xB8BB, 0x5BCC,
	0xB8BC, 0x8BA3,
	0xB8BD, 0x9644,
	0xB8BE, 0x5987,
	0xB8BF, 0x7F1A,
	0xB8C0, 0x5490,
	0xB8C1, 0x5676,
	0xB8C2, 0x560E,
	0xB8C3, 0x8BE5,
	0xB8C4, 0x6539,
	0xB8C5, 0x6982,
	0xB8C6, 0x9499,
	0xB8C7, 0x76D6,
	0xB8C8, 0x6E89,
	0xB8C9, 0x5E72,
	0xB8CA, 0x7518,
	0xB8CB, 0x6746,
	0xB8CC, 0x67D1,
	0xB8CD, 0x7AFF,
	0xB8CE, 0x809D,
	0xB8CF, 0x8D76,
	0xB8D0, 0x611F,
	0xB8D1, 0x79C6,
	0xB8D2, 0x6562,
	0xB8D3, 0x8D63,
	0xB8D4, 0x5188,
	0xB8D5, 0x521A,
	0xB8D6, 0x94A2,
	0xB8D7, 0x7F38,
	0xB8D8, 0x809B,
	0xB8D9, 0x7EB2,
	0xB8DA, 0x5C97,
	0xB8DB, 0x6E2F,
	0xB8DC, 0x6760,
	0xB8DD, 0x7BD9,
	0xB8DE, 0x768B,
	0xB8DF, 0x9AD8,
	0xB8E0, 0x818F,
	0xB8E1, 0x7F94,
	0xB8E2, 0x7CD5,
	0xB8E3, 0x641E,
	0xB8E4, 0x9550,
	0xB8E5, 0x7A3F,
	0xB8E6, 0x544A,
	0xB8E7, 0x54E5,
	0xB8E8, 0x6B4C,
	0xB8E9, 0x6401,
	0xB8EA, 0x6208,
	0xB8EB, 0x9E3D,
	0xB8EC, 0x80F3,
	0xB8ED, 0x7599,
	0xB8EE, 0x5272,
	0xB8EF, 0x9769,
	0xB8F0, 0x845B,
	0xB8F1, 0x683C,
	0xB8F2, 0x86E4,
	0xB8F3, 0x9601,
	0xB8F4, 0x9694,
	0xB8F5, 0x94EC,
	0xB8F6, 0x4E2A,
	0xB8F7, 0x5404,
	0xB8F8, 0x7ED9,
	0xB8F9, 0x6839,
	0xB8FA, 0x8DDF,
	0xB8FB, 0x8015,
	0xB8FC, 0x66F4,
	0xB8FD, 0x5E9A,
	0xB8FE, 0x7FB9,
	0xB942, 0x7B32,
	0xB947, 0x7B39,
	0xB948, 0x7B3B,
	0xB949, 0x7B3D,
	0xB950, 0x7B46,
	0xB951, 0x7B48,
	0xB952, 0x7B4A,
	0xB955, 0x7B53,
	0xB956, 0x7B55,
	0xB957, 0x7B57,
	0xB958, 0x7B59,
	0xB959, 0x7B5C,
	0xB95C, 0x7B61,
	0xB96C, 0x7B76,
	0xB96D, 0x7B78,
	0xB96E, 0x7B7A,
	0xB971, 0x7B7F,
	0xB983, 0x7B96,
	0xB9A1, 0x57C2,
	0xB9A2, 0x803F,
	0xB9A3, 0x6897,
	0xB9A4, 0x5DE5,
	0xB9A5, 0x653B,
	0xB9A6, 0x529F,
	0xB9A7, 0x606D,
	0xB9A8, 0x9F9A,
	0xB9A9, 0x4F9B,
	0xB9AA, 0x8EAC,
	0xB9AB, 0x516C,
	0xB9AC, 0x5BAB,
	0xB9AD, 0x5F13,
	0xB9AE, 0x5DE9,
	0xB9AF, 0x6C5E,
	0xB9B0, 0x62F1,
	0xB9B1, 0x8D21,
	0xB9B2, 0x5171,
	0xB9B3, 0x94A9,
	0xB9B4, 0x52FE,
	0xB9B5, 0x6C9F,
	0xB9B6, 0x82DF,
	0xB9B7, 0x72D7,
	0xB9B8, 0x57A2,
	0xB9B9, 0x6784,
	0xB9BA, 0x8D2D,
	0xB9BB, 0x591F,
	0xB9BC, 0x8F9C,
	0xB9BD, 0x83C7,
	0xB9BE, 0x5495,
	0xB9BF, 0x7B8D,
	0xB9C0, 0x4F30,
	0xB9C1, 0x6CBD,
	0xB9C2, 0x5B64,
	0xB9C3, 0x59D1,
	0xB9C4, 0x9F13,
	0xB9C5, 0x53E4,
	0xB9C6, 0x86CA,
	0xB9C7, 0x9AA8,
	0xB9C8, 0x8C37,
	0xB9C9, 0x80A1,
	0xB9CA, 0x6545,
	0xB9CB, 0x987E,
	0xB9CC, 0x56FA,
	0xB9CD, 0x96C7,
	0xB9CE, 0x522E,
	0xB9CF, 0x74DC,
	0xB9D0, 0x5250,
	0xB9D1, 0x5BE1,
	0xB9D2, 0x6302,
	0xB9D3, 0x8902,
	0xB9D4, 0x4E56,
	0xB9D5, 0x62D0,
	0xB9D6, 0x602A,
	0xB9D7, 0x68FA,
	0xB9D8, 0x5173,
	0xB9D9, 0x5B98,
	0xB9DA, 0x51A0,
	0xB9DB, 0x89C2,
	0xB9DC, 0x7BA1,
	0xB9DD, 0x9986,
	0xB9DE, 0x7F50,
	0xB9DF, 0x60EF,
	0xB9E0, 0x704C,
	0xB9E1, 0x8D2F,
	0xB9E2, 0x5149,
	0xB9E3, 0x5E7F,
	0xB9E4, 0x901B,
	0xB9E5, 0x7470,
	0xB9E6, 0x89C4,
	0xB9E7, 0x572D,
	0xB9E8, 0x7845,
	0xB9E9, 0x5F52,
	0xB9EA, 0x9F9F,
	0xB9EB, 0x95FA,
	0xB9EC, 0x8F68,
	0xB9ED, 0x9B3C,
	0xB9EE, 0x8BE1,
	0xB9EF, 0x7678,
	0xB9F0, 0x6842,
	0xB9F1, 0x67DC,
	0xB9F2, 0x8DEA,
	0xB9F3, 0x8D35,
	0xB9F4, 0x523D,
	0xB9F5, 0x8F8A,
	0xB9F6, 0x6EDA,
	0xB9F7, 0x68CD,
	0xB9F8, 0x9505,
	0xB9F9, 0x90ED,
	0xB9FA, 0x56FD,
	0xB9FB, 0x679C,
	0xB9FC, 0x88F9,
	0xB9FD, 0x8FC7,
	0xB9FE, 0x54C8,
	0xBA40, 0x7BC5,
	0xBA49, 0x7BD2,
	0xBA68, 0x7BFD,
	0xBAA0, 0x7C42,
	0xBAA1, 0x9AB8,
	0xBAA2, 0x5B69,
	0xBAA3, 0x6D77,
	0xBAA4, 0x6C26,
	0xBAA5, 0x4EA5,
	0xBAA6, 0x5BB3,
	0xBAA7, 0x9A87,
	0xBAA8, 0x9163,
	0xBAA9, 0x61A8,
	0xBAAA, 0x90AF,
	0xBAAB, 0x97E9,
	0xBAAC, 0x542B,
	0xBAAD, 0x6DB5,
	0xBAAE, 0x5BD2,
	0xBAAF, 0x51FD,
	0xBAB0, 0x558A,
	0xBAB1, 0x7F55,
	0xBAB2, 0x7FF0,
	0xBAB3, 0x64BC,
	0xBAB4, 0x634D,
	0xBAB5, 0x65F1,
	0xBAB6, 0x61BE,
	0xBAB7, 0x608D,
	0xBAB8, 0x710A,
	0xBAB9, 0x6C57,
	0xBABA, 0x6C49,
	0xBABB, 0x592F,
	0xBABC, 0x676D,
	0xBABD, 0x822A,
	0xBABE, 0x58D5,
	0xBABF, 0x568E,
	0xBAC0, 0x8C6A,
	0xBAC1, 0x6BEB,
	0xBAC2, 0x90DD,
	0xBAC3, 0x597D,
	0xBAC4, 0x8017,
	0xBAC5, 0x53F7,
	0xBAC6, 0x6D69,
	0xBAC7, 0x5475,
	0xBAC8, 0x559D,
	0xBAC9, 0x8377,
	0xBACA, 0x83CF,
	0xBACB, 0x6838,
	0xBACC, 0x79BE,
	0xBACD, 0x548C,
	0xBACE, 0x4F55,
	0xBACF, 0x5408,
	0xBAD0, 0x76D2,
	0xBAD1, 0x8C89,
	0xBAD2, 0x9602,
	0xBAD3, 0x6CB3,
	0xBAD4, 0x6DB8,
	0xBAD5, 0x8D6B,
	0xBAD6, 0x8910,
	0xBAD7, 0x9E64,
	0xBAD8, 0x8D3A,
	0xBAD9, 0x563F,
	0xBADA, 0x9ED1,
	0xBADB, 0x75D5,
	0xBADC, 0x5F88,
	0xBADD, 0x72E0,
	0xBADE, 0x6068,
	0xBADF, 0x54FC,
	0xBAE0, 0x4EA8,
	0xBAE1, 0x6A2A,
	0xBAE2, 0x8861,
	0xBAE3, 0x6052,
	0xBAE4, 0x8F70,
	0xBAE5, 0x54C4,
	0xBAE6, 0x70D8,
	0xBAE7, 0x8679,
	0xBAE8, 0x9E3F,
	0xBAE9, 0x6D2A,
	0xBAEA, 0x5B8F,
	0xBAEB, 0x5F18,
	0xBAEC, 0x7EA2,
	0xBAED, 0x5589,
	0xBAEE, 0x4FAF,
	0xBAEF, 0x7334,
	0xBAF0, 0x543C,
	0xBAF1, 0x539A,
	0xBAF2, 0x5019,
	0xBAF3, 0x540E,
	0xBAF4, 0x547C,
	0xBAF5, 0x4E4E,
	0xBAF6, 0x5FFD,
	0xBAF7, 0x745A,
	0xBAF8, 0x58F6,
	0xBAF9, 0x846B,
	0xBAFA, 0x80E1,
	0xBAFB, 0x8774,
	0xBAFC, 0x72D0,
	0xBAFD, 0x7CCA,
	0xBAFE, 0x6E56,
	0xBB80, 0x7C88,
	0xBB8A, 0x7C96,
	0xBB90, 0x7CA3,
	0xBBA1, 0x5F27,
	0xBBA2, 0x864E,
	0xBBA3, 0x552C,
	0xBBA4, 0x62A4,
	0xBBA5, 0x4E92,
	0xBBA6, 0x6CAA,
	0xBBA7, 0x6237,
	0xBBA8, 0x82B1,
	0xBBA9, 0x54D7,
	0xBBAA, 0x534E,
	0xBBAB, 0x733E,
	0xBBAC, 0x6ED1,
	0xBBAD, 0x753B,
	0xBBAE, 0x5212,
	0xBBAF, 0x5316,
	0xBBB0, 0x8BDD,
	0xBBB1, 0x69D0,
	0xBBB2, 0x5F8A,
	0xBBB3, 0x6000,
	0xBBB4, 0x6DEE,
	0xBBB5, 0x574F,
	0xBBB6, 0x6B22,
	0xBBB7, 0x73AF,
	0xBBB8, 0x6853,
	0xBBB9, 0x8FD8,
	0xBBBA, 0x7F13,
	0xBBBB, 0x6362,
	0xBBBC, 0x60A3,
	0xBBBD, 0x5524,
	0xBBBE, 0x75EA,
	0xBBBF, 0x8C62,
	0xBBC0, 0x7115,
	0xBBC1, 0x6DA3,
	0xBBC2, 0x5BA6,
	0xBBC3, 0x5E7B,
	0xBBC4, 0x8352,
	0xBBC5, 0x614C,
	0xBBC6, 0x9EC4,
	0xBBC7, 0x78FA,
	0xBBC8, 0x8757,
	0xBBC9, 0x7C27,
	0xBBCA, 0x7687,
	0xBBCB, 0x51F0,
	0xBBCC, 0x60F6,
	0xBBCD, 0x714C,
	0xBBCE, 0x6643,
	0xBBCF, 0x5E4C,
	0xBBD0, 0x604D,
	0xBBD1, 0x8C0E,
	0xBBD2, 0x7070,
	0xBBD3, 0x6325,
	0xBBD4, 0x8F89,
	0xBBD5, 0x5FBD,
	0xBBD6, 0x6062,
	0xBBD7, 0x86D4,
	0xBBD8, 0x56DE,
	0xBBD9, 0x6BC1,
	0xBBDA, 0x6094,
	0xBBDB, 0x6167,
	0xBBDC, 0x5349,
	0xBBDD, 0x60E0,
	0xBBDE, 0x6666,
	0xBBDF, 0x8D3F,
	0xBBE0, 0x79FD,
	0xBBE1, 0x4F1A,
	0xBBE2, 0x70E9,
	0xBBE3, 0x6C47,
	0xBBE4, 0x8BB3,
	0xBBE5, 0x8BF2,
	0xBBE6, 0x7ED8,
	0xBBE7, 0x8364,
	0xBBE8, 0x660F,
	0xBBE9, 0x5A5A,
	0xBBEA, 0x9B42,
	0xBBEB, 0x6D51,
	0xBBEC, 0x6DF7,
	0xBBED, 0x8C41,
	0xBBEE, 0x6D3B,
	0xBBEF, 0x4F19,
	0xBBF0, 0x706B,
	0xBBF1, 0x83B7,
	0xBBF2, 0x6216,
	0xBBF3, 0x60D1,
	0xBBF4, 0x970D,
	0xBBF5, 0x8D27,
	0xBBF6, 0x7978,
	0xBBF7, 0x51FB,
	0xBBF8, 0x573E,
	0xBBF9, 0x57FA,
	0xBBFA, 0x673A,
	0xBBFB, 0x7578,
	0xBBFC, 0x7A3D,
	0xBBFD, 0x79EF,
	0xBBFE, 0x7B95,
	0xBC45, 0x7CC6,
	0xBC46, 0x7CC9,
	0xBC47, 0x7CCB,
	0xBC4F, 0x7CD8,
	0xBC8F, 0x7D21,
	0xBCA1, 0x808C,
	0xBCA2, 0x9965,
	0xBCA3, 0x8FF9,
	0xBCA4, 0x6FC0,
	0xBCA5, 0x8BA5,
	0xBCA6, 0x9E21,
	0xBCA7, 0x59EC,
	0xBCA8, 0x7EE9,
	0xBCA9, 0x7F09,
	0xBCAA, 0x5409,
	0xBCAB, 0x6781,
	0xBCAC, 0x68D8,
	0xBCAD, 0x8F91,
	0xBCAE, 0x7C4D,
	0xBCAF, 0x96C6,
	0xBCB0, 0x53CA,
	0xBCB1, 0x6025,
	0xBCB2, 0x75BE,
	0xBCB3, 0x6C72,
	0xBCB4, 0x5373,
	0xBCB5, 0x5AC9,
	0xBCB6, 0x7EA7,
	0xBCB7, 0x6324,
	0xBCB8, 0x51E0,
	0xBCB9, 0x810A,
	0xBCBA, 0x5DF1,
	0xBCBB, 0x84DF,
	0xBCBC, 0x6280,
	0xBCBD, 0x5180,
	0xBCBE, 0x5B63,
	0xBCBF, 0x4F0E,
	0xBCC0, 0x796D,
	0xBCC1, 0x5242,
	0xBCC2, 0x60B8,
	0xBCC3, 0x6D4E,
	0xBCC4, 0x5BC4,
	0xBCC5, 0x5BC2,
	0xBCC6, 0x8BA1,
	0xBCC7, 0x8BB0,
	0xBCC8, 0x65E2,
	0xBCC9, 0x5FCC,
	0xBCCA, 0x9645,
	0xBCCB, 0x5993,
	0xBCCC, 0x7EE7,
	0xBCCD, 0x7EAA,
	0xBCCE, 0x5609,
	0xBCCF, 0x67B7,
	0xBCD0, 0x5939,
	0xBCD1, 0x4F73,
	0xBCD2, 0x5BB6,
	0xBCD3, 0x52A0,
	0xBCD4, 0x835A,
	0xBCD5, 0x988A,
	0xBCD6, 0x8D3E,
	0xBCD7, 0x7532,
	0xBCD8, 0x94BE,
	0xBCD9, 0x5047,
	0xBCDA, 0x7A3C,
	0xBCDB, 0x4EF7,
	0xBCDC, 0x67B6,
	0xBCDD, 0x9A7E,
	0xBCDE, 0x5AC1,
	0xBCDF, 0x6B7C,
	0xBCE0, 0x76D1,
	0xBCE1, 0x575A,
	0xBCE2, 0x5C16,
	0xBCE3, 0x7B3A,
	0xBCE4, 0x95F4,
	0xBCE5, 0x714E,
	0xBCE6, 0x517C,
	0xBCE7, 0x80A9,
	0xBCE8, 0x8270,
	0xBCE9, 0x5978,
	0xBCEA, 0x7F04,
	0xBCEB, 0x8327,
	0xBCEC, 0x68C0,
	0xBCED, 0x67EC,
	0xBCEE, 0x78B1,
	0xBCEF, 0x7877,
	0xBCF0, 0x62E3,
	0xBCF1, 0x6361,
	0xBCF2, 0x7B80,
	0xBCF3, 0x4FED,
	0xBCF4, 0x526A,
	0xBCF5, 0x51CF,
	0xBCF6, 0x8350,
	0xBCF7, 0x69DB,
	0xBCF8, 0x9274,
	0xBCF9, 0x8DF5,
	0xBCFA, 0x8D31,
	0xBCFB, 0x89C1,
	0xBCFC, 0x952E,
	0xBCFD, 0x7BAD,
	0xBCFE, 0x4EF6,
	0xBDA1, 0x5065,
	0xBDA2, 0x8230,
	0xBDA3, 0x5251,
	0xBDA4, 0x996F,
	0xBDA5, 0x6E10,
	0xBDA6, 0x6E85,
	0xBDA7, 0x6DA7,
	0xBDA8, 0x5EFA,
	0xBDA9, 0x50F5,
	0xBDAA, 0x59DC,
	0xBDAB, 0x5C06,
	0xBDAC, 0x6D46,
	0xBDAD, 0x6C5F,
	0xBDAE, 0x7586,
	0xBDAF, 0x848B,
	0xBDB0, 0x6868,
	0xBDB1, 0x5956,
	0xBDB2, 0x8BB2,
	0xBDB3, 0x5320,
	0xBDB4, 0x9171,
	0xBDB5, 0x964D,
	0xBDB6, 0x8549,
	0xBDB7, 0x6912,
	0xBDB8, 0x7901,
	0xBDB9, 0x7126,
	0xBDBA, 0x80F6,
	0xBDBB, 0x4EA4,
	0xBDBC, 0x90CA,
	0xBDBD, 0x6D47,
	0xBDBE, 0x9A84,
	0xBDBF, 0x5A07,
	0xBDC0, 0x56BC,
	0xBDC1, 0x6405,
	0xBDC2, 0x94F0,
	0xBDC3, 0x77EB,
	0xBDC4, 0x4FA5,
	0xBDC5, 0x811A,
	0xBDC6, 0x72E1,
	0xBDC7, 0x89D2,
	0xBDC8, 0x997A,
	0xBDC9, 0x7F34,
	0xBDCA, 0x7EDE,
	0xBDCB, 0x527F,
	0xBDCC, 0x6559,
	0xBDCD, 0x9175,
	0xBDCE, 0x8F7F,
	0xBDCF, 0x8F83,
	0xBDD0, 0x53EB,
	0xBDD1, 0x7A96,
	0xBDD2, 0x63ED,
	0xBDD3, 0x63A5,
	0xBDD4, 0x7686,
	0xBDD5, 0x79F8,
	0xBDD6, 0x8857,
	0xBDD7, 0x9636,
	0xBDD8, 0x622A,
	0xBDD9, 0x52AB,
	0xBDDA, 0x8282,
	0xBDDB, 0x6854,
	0xBDDC, 0x6770,
	0xBDDD, 0x6377,
	0xBDDE, 0x776B,
	0xBDDF, 0x7AED,
	0xBDE0, 0x6D01,
	0xBDE1, 0x7ED3,
	0xBDE2, 0x89E3,
	0xBDE3, 0x59D0,
	0xBDE4, 0x6212,
	0xBDE5, 0x85C9,
	0xBDE6, 0x82A5,
	0xBDE7, 0x754C,
	0xBDE8, 0x501F,
	0xBDE9, 0x4ECB,
	0xBDEA, 0x75A5,
	0xBDEB, 0x8BEB,
	0xBDEC, 0x5C4A,
	0xBDED, 0x5DFE,
	0xBDEE, 0x7B4B,
	0xBDEF, 0x65A4,
	0xBDF0, 0x91D1,
	0xBDF1, 0x4ECA,
	0xBDF2, 0x6D25,
	0xBDF3, 0x895F,
	0xBDF4, 0x7D27,
	0xBDF5, 0x9526,
	0xBDF6, 0x4EC5,
	0xBDF7, 0x8C28,
	0xBDF8, 0x8FDB,
	0xBDF9, 0x9773,
	0xBDFA, 0x664B,
	0xBDFB, 0x7981,
	0xBDFC, 0x8FD1,
	0xBDFD, 0x70EC,
	0xBDFE, 0x6D78,
	0xBEA1, 0x5C3D,
	0xBEA2, 0x52B2,
	0xBEA3, 0x8346,
	0xBEA4, 0x5162,
	0xBEA5, 0x830E,
	0xBEA6, 0x775B,
	0xBEA7, 0x6676,
	0xBEA8, 0x9CB8,
	0xBEA9, 0x4EAC,
	0xBEAA, 0x60CA,
	0xBEAB, 0x7CBE,
	0xBEAC, 0x7CB3,
	0xBEAD, 0x7ECF,
	0xBEAE, 0x4E95,
	0xBEAF, 0x8B66,
	0xBEB0, 0x666F,
	0xBEB1, 0x9888,
	0xBEB2, 0x9759,
	0xBEB3, 0x5883,
	0xBEB4, 0x656C,
	0xBEB5, 0x955C,
	0xBEB6, 0x5F84,
	0xBEB7, 0x75C9,
	0xBEB8, 0x9756,
	0xBEB9, 0x7ADF,
	0xBEBA, 0x7ADE,
	0xBEBB, 0x51C0,
	0xBEBC, 0x70AF,
	0xBEBD, 0x7A98,
	0xBEBE, 0x63EA,
	0xBEBF, 0x7A76,
	0xBEC0, 0x7EA0,
	0xBEC1, 0x7396,
	0xBEC2, 0x97ED,
	0xBEC3, 0x4E45,
	0xBEC4, 0x7078,
	0xBEC5, 0x4E5D,
	0xBEC6, 0x9152,
	0xBEC7, 0x53A9,
	0xBEC8, 0x6551,
	0xBEC9, 0x65E7,
	0xBECA, 0x81FC,
	0xBECB, 0x8205,
	0xBECC, 0x548E,
	0xBECD, 0x5C31,
	0xBECE, 0x759A,
	0xBECF, 0x97A0,
	0xBED0, 0x62D8,
	0xBED1, 0x72D9,
	0xBED2, 0x75BD,
	0xBED3, 0x5C45,
	0xBED4, 0x9A79,
	0xBED5, 0x83CA,
	0xBED6, 0x5C40,
	0xBED7, 0x5480,
	0xBED8, 0x77E9,
	0xBED9, 0x4E3E,
	0xBEDA, 0x6CAE,
	0xBEDB, 0x805A,
	0xBEDC, 0x62D2,
	0xBEDD, 0x636E,
	0xBEDE, 0x5DE8,
	0xBEDF, 0x5177,
	0xBEE0, 0x8DDD,
	0xBEE1, 0x8E1E,
	0xBEE2, 0x952F,
	0xBEE3, 0x4FF1,
	0xBEE4, 0x53E5,
	0xBEE5, 0x60E7,
	0xBEE6, 0x70AC,
	0xBEE7, 0x5267,
	0xBEE8, 0x6350,
	0xBEE9, 0x9E43,
	0xBEEA, 0x5A1F,
	0xBEEB, 0x5026,
	0xBEEC, 0x7737,
	0xBEED, 0x5377,
	0xBEEE, 0x7EE2,
	0xBEEF, 0x6485,
	0xBEF0, 0x652B,
	0xBEF1, 0x6289,
	0xBEF2, 0x6398,
	0xBEF3, 0x5014,
	0xBEF4, 0x7235,
	0xBEF5, 0x89C9,
	0xBEF6, 0x51B3,
	0xBEF7, 0x8BC0,
	0xBEF8, 0x7EDD,
	0xBEF9, 0x5747,
	0xBEFA, 0x83CC,
	0xBEFB, 0x94A7,
	0xBEFC, 0x519B,
	0xBEFD, 0x541B,
	0xBEFE, 0x5CFB,
	0xBF80, 0x7E3A,
	0xBFA1, 0x4FCA,
	0xBFA2, 0x7AE3,
	0xBFA3, 0x6D5A,
	0xBFA4, 0x90E1,
	0xBFA5, 0x9A8F,
	0xBFA6, 0x5580,
	0xBFA7, 0x5496,
	0xBFA8, 0x5361,
	0xBFA9, 0x54AF,
	0xBFAA, 0x5F00,
	0xBFAB, 0x63E9,
	0xBFAC, 0x6977,
	0xBFAD, 0x51EF,
	0xBFAE, 0x6168,
	0xBFAF, 0x520A,
	0xBFB0, 0x582A,
	0xBFB1, 0x52D8,
	0xBFB2, 0x574E,
	0xBFB3, 0x780D,
	0xBFB4, 0x770B,
	0xBFB5, 0x5EB7,
	0xBFB6, 0x6177,
	0xBFB7, 0x7CE0,
	0xBFB8, 0x625B,
	0xBFB9, 0x6297,
	0xBFBA, 0x4EA2,
	0xBFBB, 0x7095,
	0xBFBC, 0x8003,
	0xBFBD, 0x62F7,
	0xBFBE, 0x70E4,
	0xBFBF, 0x9760,
	0xBFC0, 0x5777,
	0xBFC1, 0x82DB,
	0xBFC2, 0x67EF,
	0xBFC3, 0x68F5,
	0xBFC4, 0x78D5,
	0xBFC5, 0x9897,
	0xBFC6, 0x79D1,
	0xBFC7, 0x58F3,
	0xBFC8, 0x54B3,
	0xBFC9, 0x53EF,
	0xBFCA, 0x6E34,
	0xBFCB, 0x514B,
	0xBFCC, 0x523B,
	0xBFCD, 0x5BA2,
	0xBFCE, 0x8BFE,
	0xBFCF, 0x80AF,
	0xBFD0, 0x5543,
	0xBFD1, 0x57A6,
	0xBFD2, 0x6073,
	0xBFD3, 0x5751,
	0xBFD4, 0x542D,
	0xBFD5, 0x7A7A,
	0xBFD6, 0x6050,
	0xBFD7, 0x5B54,
	0xBFD8, 0x63A7,
	0xBFD9, 0x62A0,
	0xBFDA, 0x53E3,
	0xBFDB, 0x6263,
	0xBFDC, 0x5BC7,
	0xBFDD, 0x67AF,
	0xBFDE, 0x54ED,
	0xBFDF, 0x7A9F,
	0xBFE0, 0x82E6,
	0xBFE1, 0x9177,
	0xBFE2, 0x5E93,
	0xBFE3, 0x88E4,
	0xBFE4, 0x5938,
	0xBFE5, 0x57AE,
	0xBFE6, 0x630E,
	0xBFE7, 0x8DE8,
	0xBFE8, 0x80EF,
	0xBFE9, 0x5757,
	0xBFEA, 0x7B77,
	0xBFEB, 0x4FA9,
	0xBFEC, 0x5FEB,
	0xBFED, 0x5BBD,
	0xBFEE, 0x6B3E,
	0xBFEF, 0x5321,
	0xBFF0, 0x7B50,
	0xBFF1, 0x72C2,
	0xBFF2, 0x6846,
	0xBFF3, 0x77FF,
	0xBFF4, 0x7736,
	0xBFF5, 0x65F7,
	0xBFF6, 0x51B5,
	0xBFF7, 0x4E8F,
	0xBFF8, 0x76D4,
	0xBFF9, 0x5CBF,
	0xBFFA, 0x7AA5,
	0xBFFB, 0x8475,
	0xBFFC, 0x594E,
	0xBFFD, 0x9B41,
	0xBFFE, 0x5080,
	0xC080, 0x7EAE,
	0xC081, 0x7EB4,
	0xC084, 0x7ED6,
	0xC085, 0x7EE4,
	0xC086, 0x7EEC,
	0xC087, 0x7EF9,
	0xC088, 0x7F0A,
	0xC089, 0x7F10,
	0xC08A, 0x7F1E,
	0xC08B, 0x7F37,
	0xC08C, 0x7F39,
	0xC094, 0x7F43,
	0xC0A1, 0x9988,
	0xC0A2, 0x6127,
	0xC0A3, 0x6E83,
	0xC0A4, 0x5764,
	0xC0A5, 0x6606,
	0xC0A6, 0x6346,
	0xC0A7, 0x56F0,
	0xC0A8, 0x62EC,
	0xC0A9, 0x6269,
	0xC0AA, 0x5ED3,
	0xC0AB, 0x9614,
	0xC0AC, 0x5783,
	0xC0AD, 0x62C9,
	0xC0AE, 0x5587,
	0xC0AF, 0x8721,
	0xC0B0, 0x814A,
	0xC0B1, 0x8FA3,
	0xC0B2, 0x5566,
	0xC0B3, 0x83B1,
	0xC0B4, 0x6765,
	0xC0B5, 0x8D56,
	0xC0B6, 0x84DD,
	0xC0B7, 0x5A6A,
	0xC0B8, 0x680F,
	0xC0B9, 0x62E6,
	0xC0BA, 0x7BEE,
	0xC0BB, 0x9611,
	0xC0BC, 0x5170,
	0xC0BD, 0x6F9C,
	0xC0BE, 0x8C30,
	0xC0BF, 0x63FD,
	0xC0C0, 0x89C8,
	0xC0C1, 0x61D2,
	0xC0C2, 0x7F06,
	0xC0C3, 0x70C2,
	0xC0C4, 0x6EE5,
	0xC0C5, 0x7405,
	0xC0C6, 0x6994,
	0xC0C7, 0x72FC,
	0xC0C8, 0x5ECA,
	0xC0C9, 0x90CE,
	0xC0CA, 0x6717,
	0xC0CB, 0x6D6A,
	0xC0CC, 0x635E,
	0xC0CD, 0x52B3,
	0xC0CE, 0x7262,
	0xC0CF, 0x8001,
	0xC0D0, 0x4F6C,
	0xC0D1, 0x59E5,
	0xC0D2, 0x916A,
	0xC0D3, 0x70D9,
	0xC0D4, 0x6D9D,
	0xC0D5, 0x52D2,
	0xC0D6, 0x4E50,
	0xC0D7, 0x96F7,
	0xC0D8, 0x956D,
	0xC0D9, 0x857E,
	0xC0DA, 0x78CA,
	0xC0DB, 0x7D2F,
	0xC0DC, 0x5121,
	0xC0DD, 0x5792,
	0xC0DE, 0x64C2,
	0xC0DF, 0x808B,
	0xC0E0, 0x7C7B,
	0xC0E1, 0x6CEA,
	0xC0E2, 0x68F1,
	0xC0E3, 0x695E,
	0xC0E4, 0x51B7,
	0xC0E5, 0x5398,
	0xC0E6, 0x68A8,
	0xC0E7, 0x7281,
	0xC0E8, 0x9ECE,
	0xC0E9, 0x7BF1,
	0xC0EA, 0x72F8,
	0xC0EB, 0x79BB,
	0xC0EC, 0x6F13,
	0xC0ED, 0x7406,
	0xC0EE, 0x674E,
	0xC0EF, 0x91CC,
	0xC0F0, 0x9CA4,
	0xC0F1, 0x793C,
	0xC0F2, 0x8389,
	0xC0F3, 0x8354,
	0xC0F4, 0x540F,
	0xC0F5, 0x6817,
	0xC0F6, 0x4E3D,
	0xC0F7, 0x5389,
	0xC0F8, 0x52B1,
	0xC0F9, 0x783E,
	0xC0FA, 0x5386,
	0xC0FB, 0x5229,
	0xC0FC, 0x5088,
	0xC0FD, 0x4F8B,
	0xC0FE, 0x4FD0,
	0xC140, 0x7F56,
	0xC141, 0x7F59,
	0xC146, 0x7F60,
	0xC151, 0x7F73,
	0xC164, 0x7F8B,
	0xC165, 0x7F8D,
	0xC172, 0x7FA0,
	0xC17E, 0x7FB1,
	0xC187, 0x7FBE,
	0xC188, 0x7FC0,
	0xC190, 0x7FCB,
	0xC191, 0x7FCD,
	0xC1A1, 0x75E2,
	0xC1A2, 0x7ACB,
	0xC1A3, 0x7C92,
	0xC1A4, 0x6CA5,
	0xC1A5, 0x96B6,
	0xC1A6, 0x529B,
	0xC1A7, 0x7483,
	0xC1A8, 0x54E9,
	0xC1A9, 0x4FE9,
	0xC1AA, 0x8054,
	0xC1AB, 0x83B2,
	0xC1AC, 0x8FDE,
	0xC1AD, 0x9570,
	0xC1AE, 0x5EC9,
	0xC1AF, 0x601C,
	0xC1B0, 0x6D9F,
	0xC1B1, 0x5E18,
	0xC1B2, 0x655B,
	0xC1B3, 0x8138,
	0xC1B4, 0x94FE,
	0xC1B5, 0x604B,
	0xC1B6, 0x70BC,
	0xC1B7, 0x7EC3,
	0xC1B8, 0x7CAE,
	0xC1B9, 0x51C9,
	0xC1BA, 0x6881,
	0xC1BB, 0x7CB1,
	0xC1BC, 0x826F,
	0xC1BD, 0x4E24,
	0xC1BE, 0x8F86,
	0xC1BF, 0x91CF,
	0xC1C0, 0x667E,
	0xC1C1, 0x4EAE,
	0xC1C2, 0x8C05,
	0xC1C3, 0x64A9,
	0xC1C4, 0x804A,
	0xC1C5, 0x50DA,
	0xC1C6, 0x7597,
	0xC1C7, 0x71CE,
	0xC1C8, 0x5BE5,
	0xC1C9, 0x8FBD,
	0xC1CA, 0x6F66,
	0xC1CB, 0x4E86,
	0xC1CC, 0x6482,
	0xC1CD, 0x9563,
	0xC1CE, 0x5ED6,
	0xC1CF, 0x6599,
	0xC1D0, 0x5217,
	0xC1D1, 0x88C2,
	0xC1D2, 0x70C8,
	0xC1D3, 0x52A3,
	0xC1D4, 0x730E,
	0xC1D5, 0x7433,
	0xC1D6, 0x6797,
	0xC1D7, 0x78F7,
	0xC1D8, 0x9716,
	0xC1D9, 0x4E34,
	0xC1DA, 0x90BB,
	0xC1DB, 0x9CDE,
	0xC1DC, 0x6DCB,
	0xC1DD, 0x51DB,
	0xC1DE, 0x8D41,
	0xC1DF, 0x541D,
	0xC1E0, 0x62CE,
	0xC1E1, 0x73B2,
	0xC1E2, 0x83F1,
	0xC1E3, 0x96F6,
	0xC1E4, 0x9F84,
	0xC1E5, 0x94C3,
	0xC1E6, 0x4F36,
	0xC1E7, 0x7F9A,
	0xC1E8, 0x51CC,
	0xC1E9, 0x7075,
	0xC1EA, 0x9675,
	0xC1EB, 0x5CAD,
	0xC1EC, 0x9886,
	0xC1ED, 0x53E6,
	0xC1EE, 0x4EE4,
	0xC1EF, 0x6E9C,
	0xC1F0, 0x7409,
	0xC1F1, 0x69B4,
	0xC1F2, 0x786B,
	0xC1F3, 0x998F,
	0xC1F4, 0x7559,
	0xC1F5, 0x5218,
	0xC1F6, 0x7624,
	0xC1F7, 0x6D41,
	0xC1F8, 0x67F3,
	0xC1F9, 0x516D,
	0xC1FA, 0x9F99,
	0xC1FB, 0x804B,
	0xC1FC, 0x5499,
	0xC1FD, 0x7B3C,
	0xC1FE, 0x7ABF,
	0xC240, 0x7FE4,
	0xC247, 0x7FEF,
	0xC248, 0x7FF2,
	0xC253, 0x8002,
	0xC25A, 0x8011,
	0xC25B, 0x8013,
	0xC261, 0x8021,
	0xC26A, 0x8032,
	0xC26B, 0x8034,
	0xC26E, 0x803C,
	0xC26F, 0x803E,
	0xC27B, 0x8053,
	0xC280, 0x8059,
	0xC2A1, 0x9686,
	0xC2A2, 0x5784,
	0xC2A3, 0x62E2,
	0xC2A4, 0x9647,
	0xC2A5, 0x697C,
	0xC2A6, 0x5A04,
	0xC2A7, 0x6402,
	0xC2A8, 0x7BD3,
	0xC2A9, 0x6F0F,
	0xC2AA, 0x964B,
	0xC2AB, 0x82A6,
	0xC2AC, 0x5362,
	0xC2AD, 0x9885,
	0xC2AE, 0x5E90,
	0xC2AF, 0x7089,
	0xC2B0, 0x63B3,
	0xC2B1, 0x5364,
	0xC2B2, 0x864F,
	0xC2B3, 0x9C81,
	0xC2B4, 0x9E93,
	0xC2B5, 0x788C,
	0xC2B6, 0x9732,
	0xC2B7, 0x8DEF,
	0xC2B8, 0x8D42,
	0xC2B9, 0x9E7F,
	0xC2BA, 0x6F5E,
	0xC2BB, 0x7984,
	0xC2BC, 0x5F55,
	0xC2BD, 0x9646,
	0xC2BE, 0x622E,
	0xC2BF, 0x9A74,
	0xC2C0, 0x5415,
	0xC2C1, 0x94DD,
	0xC2C2, 0x4FA3,
	0xC2C3, 0x65C5,
	0xC2C4, 0x5C65,
	0xC2C5, 0x5C61,
	0xC2C6, 0x7F15,
	0xC2C7, 0x8651,
	0xC2C8, 0x6C2F,
	0xC2C9, 0x5F8B,
	0xC2CA, 0x7387,
	0xC2CB, 0x6EE4,
	0xC2CC, 0x7EFF,
	0xC2CD, 0x5CE6,
	0xC2CE, 0x631B,
	0xC2CF, 0x5B6A,
	0xC2D0, 0x6EE6,
	0xC2D1, 0x5375,
	0xC2D2, 0x4E71,
	0xC2D3, 0x63A0,
	0xC2D4, 0x7565,
	0xC2D5, 0x62A1,
	0xC2D6, 0x8F6E,
	0xC2D7, 0x4F26,
	0xC2D8, 0x4ED1,
	0xC2D9, 0x6CA6,
	0xC2DA, 0x7EB6,
	0xC2DB, 0x8BBA,
	0xC2DC, 0x841D,
	0xC2DD, 0x87BA,
	0xC2DE, 0x7F57,
	0xC2DF, 0x903B,
	0xC2E0, 0x9523,
	0xC2E1, 0x7BA9,
	0xC2E2, 0x9AA1,
	0xC2E3, 0x88F8,
	0xC2E4, 0x843D,
	0xC2E5, 0x6D1B,
	0xC2E6, 0x9A86,
	0xC2E7, 0x7EDC,
	0xC2E8, 0x5988,
	0xC2E9, 0x9EBB,
	0xC2EA, 0x739B,
	0xC2EB, 0x7801,
	0xC2EC, 0x8682,
	0xC2ED, 0x9A6C,
	0xC2EE, 0x9A82,
	0xC2EF, 0x561B,
	0xC2F0, 0x5417,
	0xC2F1, 0x57CB,
	0xC2F2, 0x4E70,
	0xC2F3, 0x9EA6,
	0xC2F4, 0x5356,
	0xC2F5, 0x8FC8,
	0xC2F6, 0x8109,
	0xC2F7, 0x7792,
	0xC2F8, 0x9992,
	0xC2F9, 0x86EE,
	0xC2FA, 0x6EE1,
	0xC2FB, 0x8513,
	0xC2FC, 0x66FC,
	0xC2FD, 0x6162,
	0xC2FE, 0x6F2B,
	0xC340, 0x807E,
	0xC343, 0x8085,
	0xC344, 0x8088,
	0xC345, 0x808A,
	0xC34E, 0x8097,
	0xC34F, 0x8099,
	0xC350, 0x809E,
	0xC351, 0x80A3,
	0xC355, 0x80AC,
	0xC356, 0x80B0,
	0xC357, 0x80B3,
	0xC35C, 0x80BB,
	0xC35D, 0x80C5,
	0xC36A, 0x80D8,
	0xC36F, 0x80E6,
	0xC370, 0x80EE,
	0xC371, 0x80F5,
	0xC372, 0x80F7,
	0xC373, 0x80F9,
	0xC374, 0x80FB,
	0xC37E, 0x810B,
	0xC380, 0x810C,
	0xC381, 0x8115,
	0xC382, 0x8117,
	0xC383, 0x8119,
	0xC396, 0x8130,
	0xC39A, 0x8137,
	0xC3A0, 0x813F,
	0xC3A1, 0x8C29,
	0xC3A2, 0x8292,
	0xC3A3, 0x832B,
	0xC3A4, 0x76F2,
	0xC3A5, 0x6C13,
	0xC3A6, 0x5FD9,
	0xC3A7, 0x83BD,
	0xC3A8, 0x732B,
	0xC3A9, 0x8305,
	0xC3AA, 0x951A,
	0xC3AB, 0x6BDB,
	0xC3AC, 0x77DB,
	0xC3AD, 0x94C6,
	0xC3AE, 0x536F,
	0xC3AF, 0x8302,
	0xC3B0, 0x5192,
	0xC3B1, 0x5E3D,
	0xC3B2, 0x8C8C,
	0xC3B3, 0x8D38,
	0xC3B4, 0x4E48,
	0xC3B5, 0x73AB,
	0xC3B6, 0x679A,
	0xC3B7, 0x6885,
	0xC3B8, 0x9176,
	0xC3B9, 0x9709,
	0xC3BA, 0x7164,
	0xC3BB, 0x6CA1,
	0xC3BC, 0x7709,
	0xC3BD, 0x5A92,
	0xC3BE, 0x9541,
	0xC3BF, 0x6BCF,
	0xC3C0, 0x7F8E,
	0xC3C1, 0x6627,
	0xC3C2, 0x5BD0,
	0xC3C3, 0x59B9,
	0xC3C4, 0x5A9A,
	0xC3C5, 0x95E8,
	0xC3C6, 0x95F7,
	0xC3C7, 0x4EEC,
	0xC3C8, 0x840C,
	0xC3C9, 0x8499,
	0xC3CA, 0x6AAC,
	0xC3CB, 0x76DF,
	0xC3CC, 0x9530,
	0xC3CD, 0x731B,
	0xC3CE, 0x68A6,
	0xC3CF, 0x5B5F,
	0xC3D0, 0x772F,
	0xC3D1, 0x919A,
	0xC3D2, 0x9761,
	0xC3D3, 0x7CDC,
	0xC3D4, 0x8FF7,
	0xC3D5, 0x8C1C,
	0xC3D6, 0x5F25,
	0xC3D7, 0x7C73,
	0xC3D8, 0x79D8,
	0xC3D9, 0x89C5,
	0xC3DA, 0x6CCC,
	0xC3DB, 0x871C,
	0xC3DC, 0x5BC6,
	0xC3DD, 0x5E42,
	0xC3DE, 0x68C9,
	0xC3DF, 0x7720,
	0xC3E0, 0x7EF5,
	0xC3E1, 0x5195,
	0xC3E2, 0x514D,
	0xC3E3, 0x52C9,
	0xC3E4, 0x5A29,
	0xC3E5, 0x7F05,
	0xC3E6, 0x9762,
	0xC3E7, 0x82D7,
	0xC3E8, 0x63CF,
	0xC3E9, 0x7784,
	0xC3EA, 0x85D0,
	0xC3EB, 0x79D2,
	0xC3EC, 0x6E3A,
	0xC3ED, 0x5E99,
	0xC3EE, 0x5999,
	0xC3EF, 0x8511,
	0xC3F0, 0x706D,
	0xC3F1, 0x6C11,
	0xC3F2, 0x62BF,
	0xC3F3, 0x76BF,
	0xC3F4, 0x654F,
	0xC3F5, 0x60AF,
	0xC3F6, 0x95FD,
	0xC3F7, 0x660E,
	0xC3F8, 0x879F,
	0xC3F9, 0x9E23,
	0xC3FA, 0x94ED,
	0xC3FB, 0x540D,
	0xC3FC, 0x547D,
	0xC3FD, 0x8C2C,
	0xC3FE, 0x6478,
	0xC446, 0x8147,
	0xC447, 0x8149,
	0xC44B, 0x8152,
	0xC458, 0x8166,
	0xC459, 0x8168,
	0xC45D, 0x816F,
	0xC464, 0x8181,
	0xC46A, 0x8189,
	0xC46F, 0x8190,
	0xC480, 0x81A7,
	0xC481, 0x81A9,
	0xC499, 0x81CB,
	0xC4A1, 0x6479,
	0xC4A2, 0x8611,
	0xC4A3, 0x6A21,
	0xC4A4, 0x819C,
	0xC4A5, 0x78E8,
	0xC4A6, 0x6469,
	0xC4A7, 0x9B54,
	0xC4A8, 0x62B9,
	0xC4A9, 0x672B,
	0xC4AA, 0x83AB,
	0xC4AB, 0x58A8,
	0xC4AC, 0x9ED8,
	0xC4AD, 0x6CAB,
	0xC4AE, 0x6F20,
	0xC4AF, 0x5BDE,
	0xC4B0, 0x964C,
	0xC4B1, 0x8C0B,
	0xC4B2, 0x725F,
	0xC4B3, 0x67D0,
	0xC4B4, 0x62C7,
	0xC4B5, 0x7261,
	0xC4B6, 0x4EA9,
	0xC4B7, 0x59C6,
	0xC4B8, 0x6BCD,
	0xC4B9, 0x5893,
	0xC4BA, 0x66AE,
	0xC4BB, 0x5E55,
	0xC4BC, 0x52DF,
	0xC4BD, 0x6155,
	0xC4BE, 0x6728,
	0xC4BF, 0x76EE,
	0xC4C0, 0x7766,
	0xC4C1, 0x7267,
	0xC4C2, 0x7A46,
	0xC4C3, 0x62FF,
	0xC4C4, 0x54EA,
	0xC4C5, 0x5450,
	0xC4C6, 0x94A0,
	0xC4C7, 0x90A3,
	0xC4C8, 0x5A1C,
	0xC4C9, 0x7EB3,
	0xC4CA, 0x6C16,
	0xC4CB, 0x4E43,
	0xC4CC, 0x5976,
	0xC4CD, 0x8010,
	0xC4CE, 0x5948,
	0xC4CF, 0x5357,
	0xC4D0, 0x7537,
	0xC4D1, 0x96BE,
	0xC4D2, 0x56CA,
	0xC4D3, 0x6320,
	0xC4D4, 0x8111,
	0xC4D5, 0x607C,
	0xC4D6, 0x95F9,
	0xC4D7, 0x6DD6,
	0xC4D8, 0x5462,
	0xC4D9, 0x9981,
	0xC4DA, 0x5185,
	0xC4DB, 0x5AE9,
	0xC4DC, 0x80FD,
	0xC4DD, 0x59AE,
	0xC4DE, 0x9713,
	0xC4DF, 0x502A,
	0xC4E0, 0x6CE5,
	0xC4E1, 0x5C3C,
	0xC4E2, 0x62DF,
	0xC4E3, 0x4F60,
	0xC4E4, 0x533F,
	0xC4E5, 0x817B,
	0xC4E6, 0x9006,
	0xC4E7, 0x6EBA,
	0xC4E8, 0x852B,
	0xC4E9, 0x62C8,
	0xC4EA, 0x5E74,
	0xC4EB, 0x78BE,
	0xC4EC, 0x64B5,
	0xC4ED, 0x637B,
	0xC4EE, 0x5FF5,
	0xC4EF, 0x5A18,
	0xC4F0, 0x917F,
	0xC4F1, 0x9E1F,
	0xC4F2, 0x5C3F,
	0xC4F3, 0x634F,
	0xC4F4, 0x8042,
	0xC4F5, 0x5B7D,
	0xC4F6, 0x556E,
	0xC4F7, 0x954A,
	0xC4F8, 0x954D,
	0xC4F9, 0x6D85,
	0xC4FA, 0x60A8,
	0xC4FB, 0x67E0,
	0xC4FC, 0x72DE,
	0xC4FD, 0x51DD,
	0xC4FE, 0x5B81,
	0xC554, 0x81EB,
	0xC560, 0x81FD,
	0xC561, 0x81FF,
	0xC562, 0x8203,
	0xC56A, 0x8211,
	0xC56B, 0x8213,
	0xC572, 0x821D,
	0xC573, 0x8220,
	0xC578, 0x8229,
	0xC579, 0x822E,
	0xC57A, 0x8232,
	0xC57B, 0x823A,
	0xC57E, 0x823F,
	0xC586, 0x8248,
	0xC587, 0x824A,
	0xC593, 0x8259,
	0xC5A0, 0x8269,
	0xC5A1, 0x62E7,
	0xC5A2, 0x6CDE,
	0xC5A3, 0x725B,
	0xC5A4, 0x626D,
	0xC5A5, 0x94AE,
	0xC5A6, 0x7EBD,
	0xC5A7, 0x8113,
	0xC5A8, 0x6D53,
	0xC5A9, 0x519C,
	0xC5AA, 0x5F04,
	0xC5AB, 0x5974,
	0xC5AC, 0x52AA,
	0xC5AD, 0x6012,
	0xC5AE, 0x5973,
	0xC5AF, 0x6696,
	0xC5B0, 0x8650,
	0xC5B1, 0x759F,
	0xC5B2, 0x632A,
	0xC5B3, 0x61E6,
	0xC5B4, 0x7CEF,
	0xC5B5, 0x8BFA,
	0xC5B6, 0x54E6,
	0xC5B7, 0x6B27,
	0xC5B8, 0x9E25,
	0xC5B9, 0x6BB4,
	0xC5BA, 0x85D5,
	0xC5BB, 0x5455,
	0xC5BC, 0x5076,
	0xC5BD, 0x6CA4,
	0xC5BE, 0x556A,
	0xC5BF, 0x8DB4,
	0xC5C0, 0x722C,
	0xC5C1, 0x5E15,
	0xC5C2, 0x6015,
	0xC5C3, 0x7436,
	0xC5C4, 0x62CD,
	0xC5C5, 0x6392,
	0xC5C6, 0x724C,
	0xC5C7, 0x5F98,
	0xC5C8, 0x6E43,
	0xC5C9, 0x6D3E,
	0xC5CA, 0x6500,
	0xC5CB, 0x6F58,
	0xC5CC, 0x76D8,
	0xC5CD, 0x78D0,
	0xC5CE, 0x76FC,
	0xC5CF, 0x7554,
	0xC5D0, 0x5224,
	0xC5D1, 0x53DB,
	0xC5D2, 0x4E53,
	0xC5D3, 0x5E9E,
	0xC5D4, 0x65C1,
	0xC5D5, 0x802A,
	0xC5D6, 0x80D6,
	0xC5D7, 0x629B,
	0xC5D8, 0x5486,
	0xC5D9, 0x5228,
	0xC5DA, 0x70AE,
	0xC5DB, 0x888D,
	0xC5DC, 0x8DD1,
	0xC5DD, 0x6CE1,
	0xC5DE, 0x5478,
	0xC5DF, 0x80DA,
	0xC5E0, 0x57F9,
	0xC5E1, 0x88F4,
	0xC5E2, 0x8D54,
	0xC5E3, 0x966A,
	0xC5E4, 0x914D,
	0xC5E5, 0x4F69,
	0xC5E6, 0x6C9B,
	0xC5E7, 0x55B7,
	0xC5E8, 0x76C6,
	0xC5E9, 0x7830,
	0xC5EA, 0x62A8,
	0xC5EB, 0x70F9,
	0xC5EC, 0x6F8E,
	0xC5ED, 0x5F6D,
	0xC5EE, 0x84EC,
	0xC5EF, 0x68DA,
	0xC5F0, 0x787C,
	0xC5F1, 0x7BF7,
	0xC5F2, 0x81A8,
	0xC5F3, 0x670B,
	0xC5F4, 0x9E4F,
	0xC5F5, 0x6367,
	0xC5F6, 0x78B0,
	0xC5F7, 0x576F,
	0xC5F8, 0x7812,
	0xC5F9, 0x9739,
	0xC5FA, 0x6279,
	0xC5FB, 0x62AB,
	0xC5FC, 0x5288,
	0xC5FD, 0x7435,
	0xC5FE, 0x6BD7,
	0xC644, 0x8271,
	0xC64D, 0x8283,
	0xC651, 0x8289,
	0xC652, 0x828C,
	0xC653, 0x8290,
	0xC65A, 0x829E,
	0xC65B, 0x82A0,
	0xC65E, 0x82A7,
	0xC65F, 0x82B2,
	0xC66B, 0x82C9,
	0xC66C, 0x82D0,
	0xC66D, 0x82D6,
	0xC670, 0x82DD,
	0xC671, 0x82E2,
	0xC679, 0x82F0,
	0xC67E, 0x82F8,
	0xC680, 0x82FA,
	0xC688, 0x830D,
	0xC689, 0x8310,
	0xC68C, 0x8316,
	0xC69B, 0x832E,
	0xC69C, 0x8330,
	0xC69D, 0x8332,
	0xC69E, 0x8337,
	0xC69F, 0x833B,
	0xC6A0, 0x833D,
	0xC6A1, 0x5564,
	0xC6A2, 0x813E,
	0xC6A3, 0x75B2,
	0xC6A4, 0x76AE,
	0xC6A5, 0x5339,
	0xC6A6, 0x75DE,
	0xC6A7, 0x50FB,
	0xC6A8, 0x5C41,
	0xC6A9, 0x8B6C,
	0xC6AA, 0x7BC7,
	0xC6AB, 0x504F,
	0xC6AC, 0x7247,
	0xC6AD, 0x9A97,
	0xC6AE, 0x98D8,
	0xC6AF, 0x6F02,
	0xC6B0, 0x74E2,
	0xC6B1, 0x7968,
	0xC6B2, 0x6487,
	0xC6B3, 0x77A5,
	0xC6B4, 0x62FC,
	0xC6B5, 0x9891,
	0xC6B6, 0x8D2B,
	0xC6B7, 0x54C1,
	0xC6B8, 0x8058,
	0xC6B9, 0x4E52,
	0xC6BA, 0x576A,
	0xC6BB, 0x82F9,
	0xC6BC, 0x840D,
	0xC6BD, 0x5E73,
	0xC6BE, 0x51ED,
	0xC6BF, 0x74F6,
	0xC6C0, 0x8BC4,
	0xC6C1, 0x5C4F,
	0xC6C2, 0x5761,
	0xC6C3, 0x6CFC,
	0xC6C4, 0x9887,
	0xC6C5, 0x5A46,
	0xC6C6, 0x7834,
	0xC6C7, 0x9B44,
	0xC6C8, 0x8FEB,
	0xC6C9, 0x7C95,
	0xC6CA, 0x5256,
	0xC6CB, 0x6251,
	0xC6CC, 0x94FA,
	0xC6CD, 0x4EC6,
	0xC6CE, 0x8386,
	0xC6CF, 0x8461,
	0xC6D0, 0x83E9,
	0xC6D1, 0x84B2,
	0xC6D2, 0x57D4,
	0xC6D3, 0x6734,
	0xC6D4, 0x5703,
	0xC6D5, 0x666E,
	0xC6D6, 0x6D66,
	0xC6D7, 0x8C31,
	0xC6D8, 0x66DD,
	0xC6D9, 0x7011,
	0xC6DA, 0x671F,
	0xC6DB, 0x6B3A,
	0xC6DC, 0x6816,
	0xC6DD, 0x621A,
	0xC6DE, 0x59BB,
	0xC6DF, 0x4E03,
	0xC6E0, 0x51C4,
	0xC6E1, 0x6F06,
	0xC6E2, 0x67D2,
	0xC6E3, 0x6C8F,
	0xC6E4, 0x5176,
	0xC6E5, 0x68CB,
	0xC6E6, 0x5947,
	0xC6E7, 0x6B67,
	0xC6E8, 0x7566,
	0xC6E9, 0x5D0E,
	0xC6EA, 0x8110,
	0xC6EB, 0x9F50,
	0xC6EC, 0x65D7,
	0xC6ED, 0x7948,
	0xC6EE, 0x7941,
	0xC6EF, 0x9A91,
	0xC6F0, 0x8D77,
	0xC6F1, 0x5C82,
	0xC6F2, 0x4E5E,
	0xC6F3, 0x4F01,
	0xC6F4, 0x542F,
	0xC6F5, 0x5951,
	0xC6F6, 0x780C,
	0xC6F7, 0x5668,
	0xC6F8, 0x6C14,
	0xC6F9, 0x8FC4,
	0xC6FA, 0x5F03,
	0xC6FB, 0x6C7D,
	0xC6FC, 0x6CE3,
	0xC6FD, 0x8BAB,
	0xC6FE, 0x6390,
	0xC746, 0x8348,
	0xC74C, 0x8353,
	0xC752, 0x835D,
	0xC753, 0x8362,
	0xC773, 0x839D,
	0xC774, 0x839F,
	0xC780, 0x83AF,
	0xC781, 0x83B5,
	0xC782, 0x83BB,
	0xC788, 0x83C6,
	0xC78B, 0x83CB,
	0xC792, 0x83D5,
	0xC793, 0x83D7,
	0xC797, 0x83DE,
	0xC7A1, 0x6070,
	0xC7A2, 0x6D3D,
	0xC7A3, 0x7275,
	0xC7A4, 0x6266,
	0xC7A5, 0x948E,
	0xC7A6, 0x94C5,
	0xC7A7, 0x5343,
	0xC7A8, 0x8FC1,
	0xC7A9, 0x7B7E,
	0xC7AA, 0x4EDF,
	0xC7AB, 0x8C26,
	0xC7AC, 0x4E7E,
	0xC7AD, 0x9ED4,
	0xC7AE, 0x94B1,
	0xC7AF, 0x94B3,
	0xC7B0, 0x524D,
	0xC7B1, 0x6F5C,
	0xC7B2, 0x9063,
	0xC7B3, 0x6D45,
	0xC7B4, 0x8C34,
	0xC7B5, 0x5811,
	0xC7B6, 0x5D4C,
	0xC7B7, 0x6B20,
	0xC7B8, 0x6B49,
	0xC7B9, 0x67AA,
	0xC7BA, 0x545B,
	0xC7BB, 0x8154,
	0xC7BC, 0x7F8C,
	0xC7BD, 0x5899,
	0xC7BE, 0x8537,
	0xC7BF, 0x5F3A,
	0xC7C0, 0x62A2,
	0xC7C1, 0x6A47,
	0xC7C2, 0x9539,
	0xC7C3, 0x6572,
	0xC7C4, 0x6084,
	0xC7C5, 0x6865,
	0xC7C6, 0x77A7,
	0xC7C7, 0x4E54,
	0xC7C8, 0x4FA8,
	0xC7C9, 0x5DE7,
	0xC7CA, 0x9798,
	0xC7CB, 0x64AC,
	0xC7CC, 0x7FD8,
	0xC7CD, 0x5CED,
	0xC7CE, 0x4FCF,
	0xC7CF, 0x7A8D,
	0xC7D0, 0x5207,
	0xC7D1, 0x8304,
	0xC7D2, 0x4E14,
	0xC7D3, 0x602F,
	0xC7D4, 0x7A83,
	0xC7D5, 0x94A6,
	0xC7D6, 0x4FB5,
	0xC7D7, 0x4EB2,
	0xC7D8, 0x79E6,
	0xC7D9, 0x7434,
	0xC7DA, 0x52E4,
	0xC7DB, 0x82B9,
	0xC7DC, 0x64D2,
	0xC7DD, 0x79BD,
	0xC7DE, 0x5BDD,
	0xC7DF, 0x6C81,
	0xC7E0, 0x9752,
	0xC7E1, 0x8F7B,
	0xC7E2, 0x6C22,
	0xC7E3, 0x503E,
	0xC7E4, 0x537F,
	0xC7E5, 0x6E05,
	0xC7E6, 0x64CE,
	0xC7E7, 0x6674,
	0xC7E8, 0x6C30,
	0xC7E9, 0x60C5,
	0xC7EA, 0x9877,
	0xC7EB, 0x8BF7,
	0xC7EC, 0x5E86,
	0xC7ED, 0x743C,
	0xC7EE, 0x7A77,
	0xC7EF, 0x79CB,
	0xC7F0, 0x4E18,
	0xC7F1, 0x90B1,
	0xC7F2, 0x7403,
	0xC7F3, 0x6C42,
	0xC7F4, 0x56DA,
	0xC7F5, 0x914B,
	0xC7F6, 0x6CC5,
	0xC7F7, 0x8D8B,
	0xC7F8, 0x533A,
	0xC7F9, 0x86C6,
	0xC7FA, 0x66F2,
	0xC7FB, 0x8EAF,
	0xC7FC, 0x5C48,
	0xC7FD, 0x9A71,
	0xC7FE, 0x6E20,
	0xC84D, 0x8402,
	0xC84E, 0x8405,
	0xC853, 0x8410,
	0xC88C, 0x8458,
	0xC891, 0x8462,
	0xC897, 0x846A,
	0xC89B, 0x8472,
	0xC89C, 0x8474,
	0xC89D, 0x8477,
	0xC89E, 0x8479,
	0xC8A1, 0x53D6,
	0xC8A2, 0x5A36,
	0xC8A3, 0x9F8B,
	0xC8A4, 0x8DA3,
	0xC8A5, 0x53BB,
	0xC8A6, 0x5708,
	0xC8A7, 0x98A7,
	0xC8A8, 0x6743,
	0xC8A9, 0x919B,
	0xC8AA, 0x6CC9,
	0xC8AB, 0x5168,
	0xC8AC, 0x75CA,
	0xC8AD, 0x62F3,
	0xC8AE, 0x72AC,
	0xC8AF, 0x5238,
	0xC8B0, 0x529D,
	0xC8B1, 0x7F3A,
	0xC8B2, 0x7094,
	0xC8B3, 0x7638,
	0xC8B4, 0x5374,
	0xC8B5, 0x9E4A,
	0xC8B6, 0x69B7,
	0xC8B7, 0x786E,
	0xC8B8, 0x96C0,
	0xC8B9, 0x88D9,
	0xC8BA, 0x7FA4,
	0xC8BB, 0x7136,
	0xC8BC, 0x71C3,
	0xC8BD, 0x5189,
	0xC8BE, 0x67D3,
	0xC8BF, 0x74E4,
	0xC8C0, 0x58E4,
	0xC8C1, 0x6518,
	0xC8C2, 0x56B7,
	0xC8C3, 0x8BA9,
	0xC8C4, 0x9976,
	0xC8C5, 0x6270,
	0xC8C6, 0x7ED5,
	0xC8C7, 0x60F9,
	0xC8C8, 0x70ED,
	0xC8C9, 0x58EC,
	0xC8CA, 0x4EC1,
	0xC8CB, 0x4EBA,
	0xC8CC, 0x5FCD,
	0xC8CD, 0x97E7,
	0xC8CE, 0x4EFB,
	0xC8CF, 0x8BA4,
	0xC8D0, 0x5203,
	0xC8D1, 0x598A,
	0xC8D2, 0x7EAB,
	0xC8D3, 0x6254,
	0xC8D4, 0x4ECD,
	0xC8D5, 0x65E5,
	0xC8D6, 0x620E,
	0xC8D7, 0x8338,
	0xC8D8, 0x84C9,
	0xC8D9, 0x8363,
	0xC8DA, 0x878D,
	0xC8DB, 0x7194,
	0xC8DC, 0x6EB6,
	0xC8DD, 0x5BB9,
	0xC8DE, 0x7ED2,
	0xC8DF, 0x5197,
	0xC8E0, 0x63C9,
	0xC8E1, 0x67D4,
	0xC8E2, 0x8089,
	0xC8E3, 0x8339,
	0xC8E4, 0x8815,
	0xC8E5, 0x5112,
	0xC8E6, 0x5B7A,
	0xC8E7, 0x5982,
	0xC8E8, 0x8FB1,
	0xC8E9, 0x4E73,
	0xC8EA, 0x6C5D,
	0xC8EB, 0x5165,
	0xC8EC, 0x8925,
	0xC8ED, 0x8F6F,
	0xC8EE, 0x962E,
	0xC8EF, 0x854A,
	0xC8F0, 0x745E,
	0xC8F1, 0x9510,
	0xC8F2, 0x95F0,
	0xC8F3, 0x6DA6,
	0xC8F4, 0x82E5,
	0xC8F5, 0x5F31,
	0xC8F6, 0x6492,
	0xC8F7, 0x6D12,
	0xC8F8, 0x8428,
	0xC8F9, 0x816E,
	0xC8FA, 0x9CC3,
	0xC8FB, 0x585E,
	0xC8FC, 0x8D5B,
	0xC8FD, 0x4E09,
	0xC8FE, 0x53C1,
	0xC949, 0x848A,
	0xC94A, 0x848D,
	0xC953, 0x8498,
	0xC969, 0x84B3,
	0xC96F, 0x84BE,
	0xC970, 0x84C0,
	0xC97B, 0x84D2,
	0xC97E, 0x84D7,
	0xC985, 0x84DE,
	0xC988, 0x84E4,
	0xC9A1, 0x4F1E,
	0xC9A2, 0x6563,
	0xC9A3, 0x6851,
	0xC9A4, 0x55D3,
	0xC9A5, 0x4E27,
	0xC9A6, 0x6414,
	0xC9A7, 0x9A9A,
	0xC9A8, 0x626B,
	0xC9A9, 0x5AC2,
	0xC9AA, 0x745F,
	0xC9AB, 0x8272,
	0xC9AC, 0x6DA9,
	0xC9AD, 0x68EE,
	0xC9AE, 0x50E7,
	0xC9AF, 0x838E,
	0xC9B0, 0x7802,
	0xC9B1, 0x6740,
	0xC9B2, 0x5239,
	0xC9B3, 0x6C99,
	0xC9B4, 0x7EB1,
	0xC9B5, 0x50BB,
	0xC9B6, 0x5565,
	0xC9B7, 0x715E,
	0xC9B8, 0x7B5B,
	0xC9B9, 0x6652,
	0xC9BA, 0x73CA,
	0xC9BB, 0x82EB,
	0xC9BC, 0x6749,
	0xC9BD, 0x5C71,
	0xC9BE, 0x5220,
	0xC9BF, 0x717D,
	0xC9C0, 0x886B,
	0xC9C1, 0x95EA,
	0xC9C2, 0x9655,
	0xC9C3, 0x64C5,
	0xC9C4, 0x8D61,
	0xC9C5, 0x81B3,
	0xC9C6, 0x5584,
	0xC9C7, 0x6C55,
	0xC9C8, 0x6247,
	0xC9C9, 0x7F2E,
	0xC9CA, 0x5892,
	0xC9CB, 0x4F24,
	0xC9CC, 0x5546,
	0xC9CD, 0x8D4F,
	0xC9CE, 0x664C,
	0xC9CF, 0x4E0A,
	0xC9D0, 0x5C1A,
	0xC9D1, 0x88F3,
	0xC9D2, 0x68A2,
	0xC9D3, 0x634E,
	0xC9D4, 0x7A0D,
	0xC9D5, 0x70E7,
	0xC9D6, 0x828D,
	0xC9D7, 0x52FA,
	0xC9D8, 0x97F6,
	0xC9D9, 0x5C11,
	0xC9DA, 0x54E8,
	0xC9DB, 0x90B5,
	0xC9DC, 0x7ECD,
	0xC9DD, 0x5962,
	0xC9DE, 0x8D4A,
	0xC9DF, 0x86C7,
	0xC9E2, 0x8D66,
	0xC9E3, 0x6444,
	0xC9E4, 0x5C04,
	0xC9E5, 0x6151,
	0xC9E6, 0x6D89,
	0xC9E7, 0x793E,
	0xC9E8, 0x8BBE,
	0xC9E9, 0x7837,
	0xC9EA, 0x7533,
	0xC9EB, 0x547B,
	0xC9EC, 0x4F38,
	0xC9ED, 0x8EAB,
	0xC9EE, 0x6DF1,
	0xC9EF, 0x5A20,
	0xC9F0, 0x7EC5,
	0xC9F1, 0x795E,
	0xC9F2, 0x6C88,
	0xC9F3, 0x5BA1,
	0xC9F4, 0x5A76,
	0xC9F5, 0x751A,
	0xC9F6, 0x80BE,
	0xC9F7, 0x614E,
	0xC9F8, 0x6E17,
	0xC9F9, 0x58F0,
	0xC9FA, 0x751F,
	0xC9FB, 0x7525,
	0xC9FC, 0x7272,
	0xC9FD, 0x5347,
	0xC9FE, 0x7EF3,
	0xCA4D, 0x8512,
	0xCA57, 0x8520,
	0xCA97, 0x8573,
	0xCAA1, 0x7701,
	0xCAA2, 0x76DB,
	0xCAA3, 0x5269,
	0xCAA4, 0x80DC,
	0xCAA5, 0x5723,
	0xCAA6, 0x5E08,
	0xCAA7, 0x5931,
	0xCAA8, 0x72EE,
	0xCAA9, 0x65BD,
	0xCAAA, 0x6E7F,
	0xCAAB, 0x8BD7,
	0xCAAC, 0x5C38,
	0xCAAD, 0x8671,
	0xCAAE, 0x5341,
	0xCAAF, 0x77F3,
	0xCAB0, 0x62FE,
	0xCAB1, 0x65F6,
	0xCAB2, 0x4EC0,
	0xCAB3, 0x98DF,
	0xCAB4, 0x8680,
	0xCAB5, 0x5B9E,
	0xCAB6, 0x8BC6,
	0xCAB7, 0x53F2,
	0xCAB8, 0x77E2,
	0xCAB9, 0x4F7F,
	0xCABA, 0x5C4E,
	0xCABB, 0x9A76,
	0xCABC, 0x59CB,
	0xCABD, 0x5F0F,
	0xCABE, 0x793A,
	0xCABF, 0x58EB,
	0xCAC0, 0x4E16,
	0xCAC1, 0x67FF,
	0xCAC2, 0x4E8B,
	0xCAC3, 0x62ED,
	0xCAC4, 0x8A93,
	0xCAC5, 0x901D,
	0xCAC6, 0x52BF,
	0xCAC7, 0x662F,
	0xCAC8, 0x55DC,
	0xCAC9, 0x566C,
	0xCACA, 0x9002,
	0xCACB, 0x4ED5,
	0xCACC, 0x4F8D,
	0xCACD, 0x91CA,
	0xCACE, 0x9970,
	0xCACF, 0x6C0F,
	0xCAD0, 0x5E02,
	0xCAD1, 0x6043,
	0xCAD2, 0x5BA4,
	0xCAD3, 0x89C6,
	0xCAD4, 0x8BD5,
	0xCAD5, 0x6536,
	0xCAD6, 0x624B,
	0xCAD7, 0x9996,
	0xCAD8, 0x5B88,
	0xCAD9, 0x5BFF,
	0xCADA, 0x6388,
	0xCADB, 0x552E,
	0xCADC, 0x53D7,
	0xCADD, 0x7626,
	0xCADE, 0x517D,
	0xCADF, 0x852C,
	0xCAE0, 0x67A2,
	0xCAE1, 0x68B3,
	0xCAE2, 0x6B8A,
	0xCAE3, 0x6292,
	0xCAE4, 0x8F93,
	0xCAE5, 0x53D4,
	0xCAE6, 0x8212,
	0xCAE7, 0x6DD1,
	0xCAE8, 0x758F,
	0xCAE9, 0x4E66,
	0xCAEA, 0x8D4E,
	0xCAEB, 0x5B70,
	0xCAEC, 0x719F,
	0xCAED, 0x85AF,
	0xCAEE, 0x6691,
	0xCAEF, 0x66D9,
	0xCAF0, 0x7F72,
	0xCAF1, 0x8700,
	0xCAF2, 0x9ECD,
	0xCAF3, 0x9F20,
	0xCAF4, 0x5C5E,
	0xCAF5, 0x672F,
	0xCAF6, 0x8FF0,
	0xCAF7, 0x6811,
	0xCAF8, 0x675F,
	0xCAF9, 0x620D,
	0xCAFA, 0x7AD6,
	0xCAFB, 0x5885,
	0xCAFC, 0x5EB6,
	0xCAFD, 0x6570,
	0xCAFE, 0x6F31,
	0xCB42, 0x8586,
	0xCB5F, 0x85A9,
	0xCB69, 0x85B8,
	0xCB80, 0x85D4,
	0xCBA1, 0x6055,
	0xCBA2, 0x5237,
	0xCBA3, 0x800D,
	0xCBA4, 0x6454,
	0xCBA5, 0x8870,
	0xCBA6, 0x7529,
	0xCBA7, 0x5E05,
	0xCBA8, 0x6813,
	0xCBA9, 0x62F4,
	0xCBAA, 0x971C,
	0xCBAB, 0x53CC,
	0xCBAC, 0x723D,
	0xCBAD, 0x8C01,
	0xCBAE, 0x6C34,
	0xCBAF, 0x7761,
	0xCBB0, 0x7A0E,
	0xCBB1, 0x542E,
	0xCBB2, 0x77AC,
	0xCBB3, 0x987A,
	0xCBB4, 0x821C,
	0xCBB5, 0x8BF4,
	0xCBB6, 0x7855,
	0xCBB7, 0x6714,
	0xCBB8, 0x70C1,
	0xCBB9, 0x65AF,
	0xCBBA, 0x6495,
	0xCBBB, 0x5636,
	0xCBBC, 0x601D,
	0xCBBD, 0x79C1,
	0xCBBE, 0x53F8,
	0xCBBF, 0x4E1D,
	0xCBC0, 0x6B7B,
	0xCBC1, 0x8086,
	0xCBC2, 0x5BFA,
	0xCBC3, 0x55E3,
	0xCBC4, 0x56DB,
	0xCBC5, 0x4F3A,
	0xCBC6, 0x4F3C,
	0xCBC7, 0x9972,
	0xCBC8, 0x5DF3,
	0xCBC9, 0x677E,
	0xCBCA, 0x8038,
	0xCBCB, 0x6002,
	0xCBCC, 0x9882,
	0xCBCD, 0x9001,
	0xCBCE, 0x5B8B,
	0xCBCF, 0x8BBC,
	0xCBD0, 0x8BF5,
	0xCBD1, 0x641C,
	0xCBD2, 0x8258,
	0xCBD3, 0x64DE,
	0xCBD4, 0x55FD,
	0xCBD5, 0x82CF,
	0xCBD6, 0x9165,
	0xCBD7, 0x4FD7,
	0xCBD8, 0x7D20,
	0xCBD9, 0x901F,
	0xCBDA, 0x7C9F,
	0xCBDB, 0x50F3,
	0xCBDC, 0x5851,
	0xCBDD, 0x6EAF,
	0xCBDE, 0x5BBF,
	0xCBDF, 0x8BC9,
	0xCBE0, 0x8083,
	0xCBE1, 0x9178,
	0xCBE2, 0x849C,
	0xCBE3, 0x7B97,
	0xCBE4, 0x867D,
	0xCBE5, 0x968B,
	0xCBE6, 0x968F,
	0xCBE7, 0x7EE5,
	0xCBE8, 0x9AD3,
	0xCBE9, 0x788E,
	0xCBEA, 0x5C81,
	0xCBEB, 0x7A57,
	0xCBEC, 0x9042,
	0xCBED, 0x96A7,
	0xCBEE, 0x795F,
	0xCBEF, 0x5B59,
	0xCBF0, 0x635F,
	0xCBF1, 0x7B0B,
	0xCBF2, 0x84D1,
	0xCBF3, 0x68AD,
	0xCBF4, 0x5506,
	0xCBF5, 0x7F29,
	0xCBF6, 0x7410,
	0xCBF7, 0x7D22,
	0xCBF8, 0x9501,
	0xCBF9, 0x6240,
	0xCBFA, 0x584C,
	0xCBFB, 0x4ED6,
	0xCBFC, 0x5B83,
	0xCBFD, 0x5979,
	0xCBFE, 0x5854,
	0xCC69, 0x8628,
	0xCCA1, 0x736D,
	0xCCA2, 0x631E,
	0xCCA3, 0x8E4B,
	0xCCA4, 0x8E0F,
	0xCCA5, 0x80CE,
	0xCCA6, 0x82D4,
	0xCCA7, 0x62AC,
	0xCCA8, 0x53F0,
	0xCCA9, 0x6CF0,
	0xCCAA, 0x915E,
	0xCCAB, 0x592A,
	0xCCAC, 0x6001,
	0xCCAD, 0x6C70,
	0xCCAE, 0x574D,
	0xCCAF, 0x644A,
	0xCCB0, 0x8D2A,
	0xCCB1, 0x762B,
	0xCCB2, 0x6EE9,
	0xCCB3, 0x575B,
	0xCCB4, 0x6A80,
	0xCCB5, 0x75F0,
	0xCCB6, 0x6F6D,
	0xCCB7, 0x8C2D,
	0xCCB8, 0x8C08,
	0xCCB9, 0x5766,
	0xCCBA, 0x6BEF,
	0xCCBB, 0x8892,
	0xCCBC, 0x78B3,
	0xCCBD, 0x63A2,
	0xCCBE, 0x53F9,
	0xCCBF, 0x70AD,
	0xCCC0, 0x6C64,
	0xCCC1, 0x5858,
	0xCCC2, 0x642A,
	0xCCC3, 0x5802,
	0xCCC4, 0x68E0,
	0xCCC5, 0x819B,
	0xCCC6, 0x5510,
	0xCCC7, 0x7CD6,
	0xCCC8, 0x5018,
	0xCCC9, 0x8EBA,
	0xCCCA, 0x6DCC,
	0xCCCB, 0x8D9F,
	0xCCCC, 0x70EB,
	0xCCCD, 0x638F,
	0xCCCE, 0x6D9B,
	0xCCCF, 0x6ED4,
	0xCCD0, 0x7EE6,
	0xCCD1, 0x8404,
	0xCCD2, 0x6843,
	0xCCD3, 0x9003,
	0xCCD4, 0x6DD8,
	0xCCD5, 0x9676,
	0xCCD6, 0x8BA8,
	0xCCD7, 0x5957,
	0xCCD8, 0x7279,
	0xCCD9, 0x85E4,
	0xCCDA, 0x817E,
	0xCCDB, 0x75BC,
	0xCCDC, 0x8A8A,
	0xCCDD, 0x68AF,
	0xCCDE, 0x5254,
	0xCCDF, 0x8E22,
	0xCCE0, 0x9511,
	0xCCE1, 0x63D0,
	0xCCE2, 0x9898,
	0xCCE3, 0x8E44,
	0xCCE4, 0x557C,
	0xCCE5, 0x4F53,
	0xCCE6, 0x66FF,
	0xCCE7, 0x568F,
	0xCCE8, 0x60D5,
	0xCCE9, 0x6D95,
	0xCCEA, 0x5243,
	0xCCEB, 0x5C49,
	0xCCEC, 0x5929,
	0xCCED, 0x6DFB,
	0xCCEE, 0x586B,
	0xCCEF, 0x7530,
	0xCCF0, 0x751C,
	0xCCF1, 0x606C,
	0xCCF2, 0x8214,
	0xCCF3, 0x8146,
	0xCCF4, 0x6311,
	0xCCF5, 0x6761,
	0xCCF6, 0x8FE2,
	0xCCF7, 0x773A,
	0xCCF8, 0x8DF3,
	0xCCF9, 0x8D34,
	0xCCFA, 0x94C1,
	0xCCFB, 0x5E16,
	0xCCFC, 0x5385,
	0xCCFD, 0x542C,
	0xCCFE, 0x70C3,
	0xCD40, 0x866D,
	0xCD56, 0x8694,
	0xCD64, 0x86AB,
	0xCD74, 0x86C5,
	0xCD75, 0x86C8,
	0xCD7D, 0x86DA,
	0xCD7E, 0x86DC,
	0xCD80, 0x86DD,
	0xCD8C, 0x86EF,
	0xCD94, 0x86FF,
	0xCD95, 0x8701,
	0xCD9F, 0x8714,
	0xCDA0, 0x8716,
	0xCDA1, 0x6C40,
	0xCDA2, 0x5EF7,
	0xCDA3, 0x505C,
	0xCDA4, 0x4EAD,
	0xCDA5, 0x5EAD,
	0xCDA6, 0x633A,
	0xCDA7, 0x8247,
	0xCDA8, 0x901A,
	0xCDA9, 0x6850,
	0xCDAA, 0x916E,
	0xCDAB, 0x77B3,
	0xCDAC, 0x540C,
	0xCDAD, 0x94DC,
	0xCDAE, 0x5F64,
	0xCDAF, 0x7AE5,
	0xCDB0, 0x6876,
	0xCDB1, 0x6345,
	0xCDB2, 0x7B52,
	0xCDB3, 0x7EDF,
	0xCDB4, 0x75DB,
	0xCDB5, 0x5077,
	0xCDB6, 0x6295,
	0xCDB7, 0x5934,
	0xCDB8, 0x900F,
	0xCDB9, 0x51F8,
	0xCDBA, 0x79C3,
	0xCDBB, 0x7A81,
	0xCDBC, 0x56FE,
	0xCDBD, 0x5F92,
	0xCDBE, 0x9014,
	0xCDBF, 0x6D82,
	0xCDC0, 0x5C60,
	0xCDC1, 0x571F,
	0xCDC2, 0x5410,
	0xCDC3, 0x5154,
	0xCDC4, 0x6E4D,
	0xCDC5, 0x56E2,
	0xCDC6, 0x63A8,
	0xCDC7, 0x9893,
	0xCDC8, 0x817F,
	0xCDC9, 0x8715,
	0xCDCA, 0x892A,
	0xCDCB, 0x9000,
	0xCDCC, 0x541E,
	0xCDCD, 0x5C6F,
	0xCDCE, 0x81C0,
	0xCDCF, 0x62D6,
	0xCDD0, 0x6258,
	0xCDD1, 0x8131,
	0xCDD2, 0x9E35,
	0xCDD3, 0x9640,
	0xCDD4, 0x9A6E,
	0xCDD5, 0x9A7C,
	0xCDD6, 0x692D,
	0xCDD7, 0x59A5,
	0xCDD8, 0x62D3,
	0xCDD9, 0x553E,
	0xCDDA, 0x6316,
	0xCDDB, 0x54C7,
	0xCDDC, 0x86D9,
	0xCDDD, 0x6D3C,
	0xCDDE, 0x5A03,
	0xCDDF, 0x74E6,
	0xCDE0, 0x889C,
	0xCDE1, 0x6B6A,
	0xCDE2, 0x5916,
	0xCDE3, 0x8C4C,
	0xCDE4, 0x5F2F,
	0xCDE5, 0x6E7E,
	0xCDE6, 0x73A9,
	0xCDE7, 0x987D,
	0xCDE8, 0x4E38,
	0xCDE9, 0x70F7,
	0xCDEA, 0x5B8C,
	0xCDEB, 0x7897,
	0xCDEC, 0x633D,
	0xCDED, 0x665A,
	0xCDEE, 0x7696,
	0xCDEF, 0x60CB,
	0xCDF0, 0x5B9B,
	0xCDF1, 0x5A49,
	0xCDF2, 0x4E07,
	0xCDF3, 0x8155,
	0xCDF4, 0x6C6A,
	0xCDF5, 0x738B,
	0xCDF6, 0x4EA1,
	0xCDF7, 0x6789,
	0xCDF8, 0x7F51,
	0xCDF9, 0x5F80,
	0xCDFA, 0x65FA,
	0xCDFB, 0x671B,
	0xCDFC, 0x5FD8,
	0xCDFD, 0x5984,
	0xCDFE, 0x5A01,
	0xCE40, 0x8719,
	0xCE41, 0x871B,
	0xCE42, 0x871D,
	0xCE45, 0x8724,
	0xCE61, 0x874D,
	0xCE69, 0x8758,
	0xCE7A, 0x876F,
	0xCE7E, 0x8775,
	0xCE87, 0x8784,
	0xCE8C, 0x878C,
	0xCEA1, 0x5DCD,
	0xCEA2, 0x5FAE,
	0xCEA3, 0x5371,
	0xCEA4, 0x97E6,
	0xCEA5, 0x8FDD,
	0xCEA6, 0x6845,
	0xCEA7, 0x56F4,
	0xCEA8, 0x552F,
	0xCEA9, 0x60DF,
	0xCEAA, 0x4E3A,
	0xCEAB, 0x6F4D,
	0xCEAC, 0x7EF4,
	0xCEAD, 0x82C7,
	0xCEAE, 0x840E,
	0xCEAF, 0x59D4,
	0xCEB0, 0x4F1F,
	0xCEB1, 0x4F2A,
	0xCEB2, 0x5C3E,
	0xCEB3, 0x7EAC,
	0xCEB4, 0x672A,
	0xCEB5, 0x851A,
	0xCEB6, 0x5473,
	0xCEB7, 0x754F,
	0xCEB8, 0x80C3,
	0xCEB9, 0x5582,
	0xCEBA, 0x9B4F,
	0xCEBB, 0x4F4D,
	0xCEBC, 0x6E2D,
	0xCEBD, 0x8C13,
	0xCEBE, 0x5C09,
	0xCEBF, 0x6170,
	0xCEC0, 0x536B,
	0xCEC1, 0x761F,
	0xCEC2, 0x6E29,
	0xCEC3, 0x868A,
	0xCEC4, 0x6587,
	0xCEC5, 0x95FB,
	0xCEC6, 0x7EB9,
	0xCEC7, 0x543B,
	0xCEC8, 0x7A33,
	0xCEC9, 0x7D0A,
	0xCECA, 0x95EE,
	0xCECB, 0x55E1,
	0xCECC, 0x7FC1,
	0xCECD, 0x74EE,
	0xCECE, 0x631D,
	0xCECF, 0x8717,
	0xCED0, 0x6DA1,
	0xCED1, 0x7A9D,
	0xCED2, 0x6211,
	0xCED3, 0x65A1,
	0xCED4, 0x5367,
	0xCED5, 0x63E1,
	0xCED6, 0x6C83,
	0xCED7, 0x5DEB,
	0xCED8, 0x545C,
	0xCED9, 0x94A8,
	0xCEDA, 0x4E4C,
	0xCEDB, 0x6C61,
	0xCEDC, 0x8BEC,
	0xCEDD, 0x5C4B,
	0xCEDE, 0x65E0,
	0xCEDF, 0x829C,
	0xCEE0, 0x68A7,
	0xCEE1, 0x543E,
	0xCEE2, 0x5434,
	0xCEE3, 0x6BCB,
	0xCEE4, 0x6B66,
	0xCEE5, 0x4E94,
	0xCEE6, 0x6342,
	0xCEE7, 0x5348,
	0xCEE8, 0x821E,
	0xCEE9, 0x4F0D,
	0xCEEA, 0x4FAE,
	0xCEEB, 0x575E,
	0xCEEC, 0x620A,
	0xCEED, 0x96FE,
	0xCEEE, 0x6664,
	0xCEEF, 0x7269,
	0xCEF0, 0x52FF,
	0xCEF1, 0x52A1,
	0xCEF2, 0x609F,
	0xCEF3, 0x8BEF,
	0xCEF4, 0x6614,
	0xCEF5, 0x7199,
	0xCEF6, 0x6790,
	0xCEF7, 0x897F,
	0xCEF8, 0x7852,
	0xCEF9, 0x77FD,
	0xCEFA, 0x6670,
	0xCEFB, 0x563B,
	0xCEFC, 0x5438,
	0xCEFD, 0x9521,
	0xCEFE, 0x727A,
	0xCF45, 0x87AE,
	0xCF49, 0x87B4,
	0xCF96, 0x8814,
	0xCFA0, 0x8823,
	0xCFA1, 0x7A00,
	0xCFA2, 0x606F,
	0xCFA3, 0x5E0C,
	0xCFA4, 0x6089,
	0xCFA5, 0x819D,
	0xCFA6, 0x5915,
	0xCFA7, 0x60DC,
	0xCFA8, 0x7184,
	0xCFA9, 0x70EF,
	0xCFAA, 0x6EAA,
	0xCFAB, 0x6C50,
	0xCFAC, 0x7280,
	0xCFAD, 0x6A84,
	0xCFAE, 0x88AD,
	0xCFAF, 0x5E2D,
	0xCFB0, 0x4E60,
	0xCFB1, 0x5AB3,
	0xCFB2, 0x559C,
	0xCFB3, 0x94E3,
	0xCFB4, 0x6D17,
	0xCFB5, 0x7CFB,
	0xCFB6, 0x9699,
	0xCFB7, 0x620F,
	0xCFB8, 0x7EC6,
	0xCFB9, 0x778E,
	0xCFBA, 0x867E,
	0xCFBB, 0x5323,
	0xCFBC, 0x971E,
	0xCFBD, 0x8F96,
	0xCFBE, 0x6687,
	0xCFBF, 0x5CE1,
	0xCFC0, 0x4FA0,
	0xCFC1, 0x72ED,
	0xCFC2, 0x4E0B,
	0xCFC3, 0x53A6,
	0xCFC4, 0x590F,
	0xCFC5, 0x5413,
	0xCFC6, 0x6380,
	0xCFC7, 0x9528,
	0xCFC8, 0x5148,
	0xCFC9, 0x4ED9,
	0xCFCA, 0x9C9C,
	0xCFCB, 0x7EA4,
	0xCFCC, 0x54B8,
	0xCFCD, 0x8D24,
	0xCFCE, 0x8854,
	0xCFCF, 0x8237,
	0xCFD0, 0x95F2,
	0xCFD1, 0x6D8E,
	0xCFD2, 0x5F26,
	0xCFD3, 0x5ACC,
	0xCFD4, 0x663E,
	0xCFD5, 0x9669,
	0xCFD6, 0x73B0,
	0xCFD7, 0x732E,
	0xCFD8, 0x53BF,
	0xCFD9, 0x817A,
	0xCFDA, 0x9985,
	0xCFDB, 0x7FA1,
	0xCFDC, 0x5BAA,
	0xCFDD, 0x9677,
	0xCFDE, 0x9650,
	0xCFDF, 0x7EBF,
	0xCFE0, 0x76F8,
	0xCFE1, 0x53A2,
	0xCFE2, 0x9576,
	0xCFE3, 0x9999,
	0xCFE4, 0x7BB1,
	0xCFE5, 0x8944,
	0xCFE6, 0x6E58,
	0xCFE7, 0x4E61,
	0xCFE8, 0x7FD4,
	0xCFE9, 0x7965,
	0xCFEA, 0x8BE6,
	0xCFEB, 0x60F3,
	0xCFEC, 0x54CD,
	0xCFED, 0x4EAB,
	0xCFEE, 0x9879,
	0xCFEF, 0x5DF7,
	0xCFF0, 0x6A61,
	0xCFF1, 0x50CF,
	0xCFF2, 0x5411,
	0xCFF3, 0x8C61,
	0xCFF4, 0x8427,
	0xCFF5, 0x785D,
	0xCFF6, 0x9704,
	0xCFF7, 0x524A,
	0xCFF8, 0x54EE,
	0xCFF9, 0x56A3,
	0xCFFA, 0x9500,
	0xCFFB, 0x6D88,
	0xCFFC, 0x5BB5,
	0xCFFD, 0x6DC6,
	0xCFFE, 0x6653,
	0xD06A, 0x8858,
	0xD074, 0x886A,
	0xD075, 0x886D,
	0xD076, 0x886F,
	0xD077, 0x8871,
	0xD082, 0x8880,
	0xD083, 0x8883,
	0xD088, 0x888C,
	0xD09A, 0x88A3,
	0xD0A1, 0x5C0F,
	0xD0A2, 0x5B5D,
	0xD0A3, 0x6821,
	0xD0A4, 0x8096,
	0xD0A5, 0x5578,
	0xD0A6, 0x7B11,
	0xD0A7, 0x6548,
	0xD0A8, 0x6954,
	0xD0A9, 0x4E9B,
	0xD0AA, 0x6B47,
	0xD0AB, 0x874E,
	0xD0AC, 0x978B,
	0xD0AD, 0x534F,
	0xD0AE, 0x631F,
	0xD0AF, 0x643A,
	0xD0B0, 0x90AA,
	0xD0B1, 0x659C,
	0xD0B2, 0x80C1,
	0xD0B3, 0x8C10,
	0xD0B4, 0x5199,
	0xD0B5, 0x68B0,
	0xD0B6, 0x5378,
	0xD0B7, 0x87F9,
	0xD0B8, 0x61C8,
	0xD0B9, 0x6CC4,
	0xD0BA, 0x6CFB,
	0xD0BB, 0x8C22,
	0xD0BC, 0x5C51,
	0xD0BD, 0x85AA,
	0xD0BE, 0x82AF,
	0xD0BF, 0x950C,
	0xD0C0, 0x6B23,
	0xD0C1, 0x8F9B,
	0xD0C2, 0x65B0,
	0xD0C3, 0x5FFB,
	0xD0C4, 0x5FC3,
	0xD0C5, 0x4FE1,
	0xD0C6, 0x8845,
	0xD0C7, 0x661F,
	0xD0C8, 0x8165,
	0xD0C9, 0x7329,
	0xD0CA, 0x60FA,
	0xD0CB, 0x5174,
	0xD0CC, 0x5211,
	0xD0CD, 0x578B,
	0xD0CE, 0x5F62,
	0xD0CF, 0x90A2,
	0xD0D0, 0x884C,
	0xD0D1, 0x9192,
	0xD0D2, 0x5E78,
	0xD0D3, 0x674F,
	0xD0D4, 0x6027,
	0xD0D5, 0x59D3,
	0xD0D6, 0x5144,
	0xD0D7, 0x51F6,
	0xD0D8, 0x80F8,
	0xD0D9, 0x5308,
	0xD0DA, 0x6C79,
	0xD0DB, 0x96C4,
	0xD0DC, 0x718A,
	0xD0DD, 0x4F11,
	0xD0DE, 0x4FEE,
	0xD0DF, 0x7F9E,
	0xD0E0, 0x673D,
	0xD0E1, 0x55C5,
	0xD0E2, 0x9508,
	0xD0E3, 0x79C0,
	0xD0E4, 0x8896,
	0xD0E5, 0x7EE3,
	0xD0E6, 0x589F,
	0xD0E7, 0x620C,
	0xD0E8, 0x9700,
	0xD0E9, 0x865A,
	0xD0EA, 0x5618,
	0xD0EB, 0x987B,
	0xD0EC, 0x5F90,
	0xD0ED, 0x8BB8,
	0xD0EE, 0x84C4,
	0xD0EF, 0x9157,
	0xD0F0, 0x53D9,
	0xD0F1, 0x65ED,
	0xD0F2, 0x5E8F,
	0xD0F3, 0x755C,
	0xD0F4, 0x6064,
	0xD0F5, 0x7D6E,
	0xD0F6, 0x5A7F,
	0xD0F7, 0x7EEA,
	0xD0F8, 0x7EED,
	0xD0F9, 0x8F69,
	0xD0FA, 0x55A7,
	0xD0FB, 0x5BA3,
	0xD0FC, 0x60AC,
	0xD0FD, 0x65CB,
	0xD0FE, 0x7384,
	0xD140, 0x88AC,
	0xD15C, 0x88D3,
	0xD16F, 0x88F2,
	0xD175, 0x88FD,
	0xD180, 0x8909,
	0xD186, 0x8911,
	0xD19F, 0x8935,
	0xD1A0, 0x8937,
	0xD1A1, 0x9009,
	0xD1A2, 0x7663,
	0xD1A3, 0x7729,
	0xD1A4, 0x7EDA,
	0xD1A5, 0x9774,
	0xD1A6, 0x859B,
	0xD1A7, 0x5B66,
	0xD1A8, 0x7A74,
	0xD1A9, 0x96EA,
	0xD1AA, 0x8840,
	0xD1AB, 0x52CB,
	0xD1AC, 0x718F,
	0xD1AD, 0x5FAA,
	0xD1AE, 0x65EC,
	0xD1AF, 0x8BE2,
	0xD1B0, 0x5BFB,
	0xD1B1, 0x9A6F,
	0xD1B2, 0x5DE1,
	0xD1B3, 0x6B89,
	0xD1B4, 0x6C5B,
	0xD1B5, 0x8BAD,
	0xD1B6, 0x8BAF,
	0xD1B7, 0x900A,
	0xD1B8, 0x8FC5,
	0xD1B9, 0x538B,
	0xD1BA, 0x62BC,
	0xD1BB, 0x9E26,
	0xD1BC, 0x9E2D,
	0xD1BD, 0x5440,
	0xD1BE, 0x4E2B,
	0xD1BF, 0x82BD,
	0xD1C0, 0x7259,
	0xD1C1, 0x869C,
	0xD1C2, 0x5D16,
	0xD1C3, 0x8859,
	0xD1C4, 0x6DAF,
	0xD1C5, 0x96C5,
	0xD1C6, 0x54D1,
	0xD1C7, 0x4E9A,
	0xD1C8, 0x8BB6,
	0xD1C9, 0x7109,
	0xD1CA, 0x54BD,
	0xD1CB, 0x9609,
	0xD1CC, 0x70DF,
	0xD1CD, 0x6DF9,
	0xD1CE, 0x76D0,
	0xD1CF, 0x4E25,
	0xD1D0, 0x7814,
	0xD1D1, 0x8712,
	0xD1D2, 0x5CA9,
	0xD1D3, 0x5EF6,
	0xD1D4, 0x8A00,
	0xD1D5, 0x989C,
	0xD1D6, 0x960E,
	0xD1D7, 0x708E,
	0xD1D8, 0x6CBF,
	0xD1D9, 0x5944,
	0xD1DA, 0x63A9,
	0xD1DB, 0x773C,
	0xD1DC, 0x884D,
	0xD1DD, 0x6F14,
	0xD1DE, 0x8273,
	0xD1DF, 0x5830,
	0xD1E0, 0x71D5,
	0xD1E1, 0x538C,
	0xD1E2, 0x781A,
	0xD1E3, 0x96C1,
	0xD1E4, 0x5501,
	0xD1E5, 0x5F66,
	0xD1E6, 0x7130,
	0xD1E7, 0x5BB4,
	0xD1E8, 0x8C1A,
	0xD1E9, 0x9A8C,
	0xD1EA, 0x6B83,
	0xD1EB, 0x592E,
	0xD1EC, 0x9E2F,
	0xD1ED, 0x79E7,
	0xD1EE, 0x6768,
	0xD1EF, 0x626C,
	0xD1F0, 0x4F6F,
	0xD1F1, 0x75A1,
	0xD1F2, 0x7F8A,
	0xD1F3, 0x6D0B,
	0xD1F4, 0x9633,
	0xD1F5, 0x6C27,
	0xD1F6, 0x4EF0,
	0xD1F7, 0x75D2,
	0xD1F8, 0x517B,
	0xD1F9, 0x6837,
	0xD1FA, 0x6F3E,
	0xD1FB, 0x9080,
	0xD1FC, 0x8170,
	0xD1FD, 0x5996,
	0xD1FE, 0x7476,
	0xD27E, 0x897C,
	0xD282, 0x8980,
	0xD283, 0x8982,
	0xD2A1, 0x6447,
	0xD2A2, 0x5C27,
	0xD2A3, 0x9065,
	0xD2A4, 0x7A91,
	0xD2A5, 0x8C23,
	0xD2A6, 0x59DA,
	0xD2A7, 0x54AC,
	0xD2A8, 0x8200,
	0xD2A9, 0x836F,
	0xD2AA, 0x8981,
	0xD2AB, 0x8000,
	0xD2AC, 0x6930,
	0xD2AD, 0x564E,
	0xD2AE, 0x8036,
	0xD2AF, 0x7237,
	0xD2B0, 0x91CE,
	0xD2B1, 0x51B6,
	0xD2B2, 0x4E5F,
	0xD2B3, 0x9875,
	0xD2B4, 0x6396,
	0xD2B5, 0x4E1A,
	0xD2B6, 0x53F6,
	0xD2B7, 0x66F3,
	0xD2B8, 0x814B,
	0xD2B9, 0x591C,
	0xD2BA, 0x6DB2,
	0xD2BB, 0x4E00,
	0xD2BC, 0x58F9,
	0xD2BD, 0x533B,
	0xD2BE, 0x63D6,
	0xD2BF, 0x94F1,
	0xD2C0, 0x4F9D,
	0xD2C1, 0x4F0A,
	0xD2C2, 0x8863,
	0xD2C3, 0x9890,
	0xD2C4, 0x5937,
	0xD2C5, 0x9057,
	0xD2C6, 0x79FB,
	0xD2C7, 0x4EEA,
	0xD2C8, 0x80F0,
	0xD2C9, 0x7591,
	0xD2CA, 0x6C82,
	0xD2CB, 0x5B9C,
	0xD2CC, 0x59E8,
	0xD2CD, 0x5F5D,
	0xD2CE, 0x6905,
	0xD2CF, 0x8681,
	0xD2D0, 0x501A,
	0xD2D1, 0x5DF2,
	0xD2D2, 0x4E59,
	0xD2D3, 0x77E3,
	0xD2D4, 0x4EE5,
	0xD2D5, 0x827A,
	0xD2D6, 0x6291,
	0xD2D7, 0x6613,
	0xD2D8, 0x9091,
	0xD2D9, 0x5C79,
	0xD2DA, 0x4EBF,
	0xD2DB, 0x5F79,
	0xD2DC, 0x81C6,
	0xD2DD, 0x9038,
	0xD2DE, 0x8084,
	0xD2DF, 0x75AB,
	0xD2E0, 0x4EA6,
	0xD2E1, 0x88D4,
	0xD2E2, 0x610F,
	0xD2E3, 0x6BC5,
	0xD2E4, 0x5FC6,
	0xD2E5, 0x4E49,
	0xD2E6, 0x76CA,
	0xD2E7, 0x6EA2,
	0xD2E8, 0x8BE3,
	0xD2E9, 0x8BAE,
	0xD2EA, 0x8C0A,
	0xD2EB, 0x8BD1,
	0xD2EC, 0x5F02,
	0xD2ED, 0x7FFC,
	0xD2EE, 0x7FCC,
	0xD2EF, 0x7ECE,
	0xD2F0, 0x8335,
	0xD2F1, 0x836B,
	0xD2F2, 0x56E0,
	0xD2F3, 0x6BB7,
	0xD2F4, 0x97F3,
	0xD2F5, 0x9634,
	0xD2F6, 0x59FB,
	0xD2F7, 0x541F,
	0xD2F8, 0x94F6,
	0xD2F9, 0x6DEB,
	0xD2FA, 0x5BC5,
	0xD2FB, 0x996E,
	0xD2FC, 0x5C39,
	0xD2FD, 0x5F15,
	0xD2FE, 0x9690,
	0xD35F, 0x89C3,
	0xD360, 0x89CD,
	0xD367, 0x89DB,
	0xD368, 0x89DD,
	0xD36D, 0x89E4,
	0xD3A1, 0x5370,
	0xD3A2, 0x82F1,
	0xD3A3, 0x6A31,
	0xD3A4, 0x5A74,
	0xD3A5, 0x9E70,
	0xD3A6, 0x5E94,
	0xD3A7, 0x7F28,
	0xD3A8, 0x83B9,
	0xD3AB, 0x8367,
	0xD3AC, 0x8747,
	0xD3AD, 0x8FCE,
	0xD3AE, 0x8D62,
	0xD3AF, 0x76C8,
	0xD3B0, 0x5F71,
	0xD3B1, 0x9896,
	0xD3B2, 0x786C,
	0xD3B3, 0x6620,
	0xD3B4, 0x54DF,
	0xD3B5, 0x62E5,
	0xD3B6, 0x4F63,
	0xD3B7, 0x81C3,
	0xD3B8, 0x75C8,
	0xD3B9, 0x5EB8,
	0xD3BA, 0x96CD,
	0xD3BB, 0x8E0A,
	0xD3BC, 0x86F9,
	0xD3BD, 0x548F,
	0xD3BE, 0x6CF3,
	0xD3BF, 0x6D8C,
	0xD3C0, 0x6C38,
	0xD3C1, 0x607F,
	0xD3C2, 0x52C7,
	0xD3C3, 0x7528,
	0xD3C4, 0x5E7D,
	0xD3C5, 0x4F18,
	0xD3C6, 0x60A0,
	0xD3C7, 0x5FE7,
	0xD3C8, 0x5C24,
	0xD3C9, 0x7531,
	0xD3CA, 0x90AE,
	0xD3CB, 0x94C0,
	0xD3CC, 0x72B9,
	0xD3CD, 0x6CB9,
	0xD3CE, 0x6E38,
	0xD3CF, 0x9149,
	0xD3D0, 0x6709,
	0xD3D1, 0x53CB,
	0xD3D2, 0x53F3,
	0xD3D3, 0x4F51,
	0xD3D4, 0x91C9,
	0xD3D5, 0x8BF1,
	0xD3D6, 0x53C8,
	0xD3D7, 0x5E7C,
	0xD3D8, 0x8FC2,
	0xD3D9, 0x6DE4,
	0xD3DA, 0x4E8E,
	0xD3DB, 0x76C2,
	0xD3DC, 0x6986,
	0xD3DD, 0x865E,
	0xD3DE, 0x611A,
	0xD3DF, 0x8206,
	0xD3E0, 0x4F59,
	0xD3E1, 0x4FDE,
	0xD3E2, 0x903E,
	0xD3E3, 0x9C7C,
	0xD3E4, 0x6109,
	0xD3E5, 0x6E1D,
	0xD3E6, 0x6E14,
	0xD3E7, 0x9685,
	0xD3E8, 0x4E88,
	0xD3E9, 0x5A31,
	0xD3EA, 0x96E8,
	0xD3EB, 0x4E0E,
	0xD3EC, 0x5C7F,
	0xD3ED, 0x79B9,
	0xD3EE, 0x5B87,
	0xD3EF, 0x8BED,
	0xD3F0, 0x7FBD,
	0xD3F1, 0x7389,
	0xD3F2, 0x57DF,
	0xD3F3, 0x828B,
	0xD3F4, 0x90C1,
	0xD3F5, 0x5401,
	0xD3F6, 0x9047,
	0xD3F7, 0x55BB,
	0xD3F8, 0x5CEA,
	0xD3F9, 0x5FA1,
	0xD3FA, 0x6108,
	0xD3FB, 0x6B32,
	0xD3FC, 0x72F1,
	0xD3FD, 0x80B2,
	0xD3FE, 0x8A89,
	0xD4A1, 0x6D74,
	0xD4A2, 0x5BD3,
	0xD4A3, 0x88D5,
	0xD4A4, 0x9884,
	0xD4A5, 0x8C6B,
	0xD4A6, 0x9A6D,
	0xD4A7, 0x9E33,
	0xD4A8, 0x6E0A,
	0xD4A9, 0x51A4,
	0xD4AA, 0x5143,
	0xD4AB, 0x57A3,
	0xD4AC, 0x8881,
	0xD4AD, 0x539F,
	0xD4AE, 0x63F4,
	0xD4AF, 0x8F95,
	0xD4B0, 0x56ED,
	0xD4B1, 0x5458,
	0xD4B2, 0x5706,
	0xD4B3, 0x733F,
	0xD4B4, 0x6E90,
	0xD4B5, 0x7F18,
	0xD4B6, 0x8FDC,
	0xD4B7, 0x82D1,
	0xD4B8, 0x613F,
	0xD4B9, 0x6028,
	0xD4BA, 0x9662,
	0xD4BB, 0x66F0,
	0xD4BC, 0x7EA6,
	0xD4BD, 0x8D8A,
	0xD4BE, 0x8DC3,
	0xD4BF, 0x94A5,
	0xD4C0, 0x5CB3,
	0xD4C1, 0x7CA4,
	0xD4C2, 0x6708,
	0xD4C3, 0x60A6,
	0xD4C4, 0x9605,
	0xD4C5, 0x8018,
	0xD4C6, 0x4E91,
	0xD4C7, 0x90E7,
	0xD4C8, 0x5300,
	0xD4C9, 0x9668,
	0xD4CA, 0x5141,
	0xD4CB, 0x8FD0,
	0xD4CC, 0x8574,
	0xD4CD, 0x915D,
	0xD4CE, 0x6655,
	0xD4CF, 0x97F5,
	0xD4D0, 0x5B55,
	0xD4D1, 0x531D,
	0xD4D2, 0x7838,
	0xD4D3, 0x6742,
	0xD4D4, 0x683D,
	0xD4D5, 0x54C9,
	0xD4D6, 0x707E,
	0xD4D7, 0x5BB0,
	0xD4D8, 0x8F7D,
	0xD4D9, 0x518D,
	0xD4DA, 0x5728,
	0xD4DB, 0x54B1,
	0xD4DC, 0x6512,
	0xD4DD, 0x6682,
	0xD4DE, 0x8D5E,
	0xD4DF, 0x8D43,
	0xD4E0, 0x810F,
	0xD4E1, 0x846C,
	0xD4E2, 0x906D,
	0xD4E3, 0x7CDF,
	0xD4E4, 0x51FF,
	0xD4E5, 0x85FB,
	0xD4E6, 0x67A3,
	0xD4E7, 0x65E9,
	0xD4E8, 0x6FA1,
	0xD4E9, 0x86A4,
	0xD4EA, 0x8E81,
	0xD4EB, 0x566A,
	0xD4EC, 0x9020,
	0xD4ED, 0x7682,
	0xD4EE, 0x7076,
	0xD4EF, 0x71E5,
	0xD4F0, 0x8D23,
	0xD4F1, 0x62E9,
	0xD4F2, 0x5219,
	0xD4F3, 0x6CFD,
	0xD4F4, 0x8D3C,
	0xD4F5, 0x600E,
	0xD4F6, 0x589E,
	0xD4F7, 0x618E,
	0xD4F8, 0x66FE,
	0xD4F9, 0x8D60,
	0xD4FA, 0x624E,
	0xD4FB, 0x55B3,
	0xD4FC, 0x6E23,
	0xD4FD, 0x672D,
	0xD4FE, 0x8F67,
	0xD5A1, 0x94E1,
	0xD5A2, 0x95F8,
	0xD5A3, 0x7728,
	0xD5A4, 0x6805,
	0xD5A5, 0x69A8,
	0xD5A6, 0x548B,
	0xD5A7, 0x4E4D,
	0xD5A8, 0x70B8,
	0xD5A9, 0x8BC8,
	0xD5AA, 0x6458,
	0xD5AB, 0x658B,
	0xD5AC, 0x5B85,
	0xD5AD, 0x7A84,
	0xD5AE, 0x503A,
	0xD5AF, 0x5BE8,
	0xD5B0, 0x77BB,
	0xD5B1, 0x6BE1,
	0xD5B2, 0x8A79,
	0xD5B3, 0x7C98,
	0xD5B4, 0x6CBE,
	0xD5B5, 0x76CF,
	0xD5B6, 0x65A9,
	0xD5B7, 0x8F97,
	0xD5B8, 0x5D2D,
	0xD5B9, 0x5C55,
	0xD5BA, 0x8638,
	0xD5BB, 0x6808,
	0xD5BC, 0x5360,
	0xD5BD, 0x6218,
	0xD5BE, 0x7AD9,
	0xD5BF, 0x6E5B,
	0xD5C0, 0x7EFD,
	0xD5C1, 0x6A1F,
	0xD5C2, 0x7AE0,
	0xD5C3, 0x5F70,
	0xD5C4, 0x6F33,
	0xD5C5, 0x5F20,
	0xD5C6, 0x638C,
	0xD5C7, 0x6DA8,
	0xD5C8, 0x6756,
	0xD5C9, 0x4E08,
	0xD5CA, 0x5E10,
	0xD5CB, 0x8D26,
	0xD5CC, 0x4ED7,
	0xD5CD, 0x80C0,
	0xD5CE, 0x7634,
	0xD5CF, 0x969C,
	0xD5D0, 0x62DB,
	0xD5D1, 0x662D,
	0xD5D2, 0x627E,
	0xD5D3, 0x6CBC,
	0xD5D4, 0x8D75,
	0xD5D5, 0x7167,
	0xD5D6, 0x7F69,
	0xD5D7, 0x5146,
	0xD5D8, 0x8087,
	0xD5D9, 0x53EC,
	0xD5DA, 0x906E,
	0xD5DB, 0x6298,
	0xD5DC, 0x54F2,
	0xD5DD, 0x86F0,
	0xD5DE, 0x8F99,
	0xD5DF, 0x8005,
	0xD5E0, 0x9517,
	0xD5E1, 0x8517,
	0xD5E2, 0x8FD9,
	0xD5E3, 0x6D59,
	0xD5E4, 0x73CD,
	0xD5E5, 0x659F,
	0xD5E6, 0x771F,
	0xD5E7, 0x7504,
	0xD5E8, 0x7827,
	0xD5E9, 0x81FB,
	0xD5EA, 0x8D1E,
	0xD5EB, 0x9488,
	0xD5EC, 0x4FA6,
	0xD5ED, 0x6795,
	0xD5EE, 0x75B9,
	0xD5EF, 0x8BCA,
	0xD5F0, 0x9707,
	0xD5F1, 0x632F,
	0xD5F2, 0x9547,
	0xD5F3, 0x9635,
	0xD5F4, 0x84B8,
	0xD5F5, 0x6323,
	0xD5F6, 0x7741,
	0xD5F7, 0x5F81,
	0xD5F8, 0x72F0,
	0xD5F9, 0x4E89,
	0xD5FA, 0x6014,
	0xD5FB, 0x6574,
	0xD5FC, 0x62EF,
	0xD5FD, 0x6B63,
	0xD5FE, 0x653F,
	0xD6A1, 0x5E27,
	0xD6A2, 0x75C7,
	0xD6A3, 0x90D1,
	0xD6A4, 0x8BC1,
	0xD6A5, 0x829D,
	0xD6A6, 0x679D,
	0xD6A7, 0x652F,
	0xD6A8, 0x5431,
	0xD6A9, 0x8718,
	0xD6AA, 0x77E5,
	0xD6AB, 0x80A2,
	0xD6AC, 0x8102,
	0xD6AD, 0x6C41,
	0xD6AE, 0x4E4B,
	0xD6AF, 0x7EC7,
	0xD6B0, 0x804C,
	0xD6B1, 0x76F4,
	0xD6B2, 0x690D,
	0xD6B3, 0x6B96,
	0xD6B4, 0x6267,
	0xD6B5, 0x503C,
	0xD6B6, 0x4F84,
	0xD6B7, 0x5740,
	0xD6B8, 0x6307,
	0xD6B9, 0x6B62,
	0xD6BA, 0x8DBE,
	0xD6BB, 0x53EA,
	0xD6BC, 0x65E8,
	0xD6BD, 0x7EB8,
	0xD6BE, 0x5FD7,
	0xD6BF, 0x631A,
	0xD6C0, 0x63B7,
	0xD6C3, 0x7F6E,
	0xD6C4, 0x5E1C,
	0xD6C5, 0x5CD9,
	0xD6C6, 0x5236,
	0xD6C7, 0x667A,
	0xD6C8, 0x79E9,
	0xD6C9, 0x7A1A,
	0xD6CA, 0x8D28,
	0xD6CB, 0x7099,
	0xD6CC, 0x75D4,
	0xD6CD, 0x6EDE,
	0xD6CE, 0x6CBB,
	0xD6CF, 0x7A92,
	0xD6D0, 0x4E2D,
	0xD6D1, 0x76C5,
	0xD6D2, 0x5FE0,
	0xD6D3, 0x949F,
	0xD6D4, 0x8877,
	0xD6D5, 0x7EC8,
	0xD6D6, 0x79CD,
	0xD6D7, 0x80BF,
	0xD6D8, 0x91CD,
	0xD6D9, 0x4EF2,
	0xD6DA, 0x4F17,
	0xD6DB, 0x821F,
	0xD6DC, 0x5468,
	0xD6DD, 0x5DDE,
	0xD6DE, 0x6D32,
	0xD6DF, 0x8BCC,
	0xD6E0, 0x7CA5,
	0xD6E1, 0x8F74,
	0xD6E2, 0x8098,
	0xD6E3, 0x5E1A,
	0xD6E4, 0x5492,
	0xD6E5, 0x76B1,
	0xD6E6, 0x5B99,
	0xD6E7, 0x663C,
	0xD6E8, 0x9AA4,
	0xD6E9, 0x73E0,
	0xD6EA, 0x682A,
	0xD6EB, 0x86DB,
	0xD6EC, 0x6731,
	0xD6ED, 0x732A,
	0xD6EE, 0x8BF8,
	0xD6EF, 0x8BDB,
	0xD6F0, 0x9010,
	0xD6F1, 0x7AF9,
	0xD6F2, 0x70DB,
	0xD6F3, 0x716E,
	0xD6F4, 0x62C4,
	0xD6F5, 0x77A9,
	0xD6F6, 0x5631,
	0xD6F7, 0x4E3B,
	0xD6F8, 0x8457,
	0xD6F9, 0x67F1,
	0xD6FA, 0x52A9,
	0xD6FB, 0x86C0,
	0xD6FC, 0x8D2E,
	0xD6FD, 0x94F8,
	0xD6FE, 0x7B51,
	0xD799, 0x8BAC,
	0xD79A, 0x8BB1,
	0xD79B, 0x8BBB,
	0xD79C, 0x8BC7,
	0xD79D, 0x8BD0,
	0xD79E, 0x8BEA,
	0xD79F, 0x8C09,
	0xD7A0, 0x8C1E,
	0xD7A1, 0x4F4F,
	0xD7A2, 0x6CE8,
	0xD7A3, 0x795D,
	0xD7A4, 0x9A7B,
	0xD7A5, 0x6293,
	0xD7A6, 0x722A,
	0xD7A7, 0x62FD,
	0xD7A8, 0x4E13,
	0xD7A9, 0x7816,
	0xD7AA, 0x8F6C,
	0xD7AB, 0x64B0,
	0xD7AC, 0x8D5A,
	0xD7AD, 0x7BC6,
	0xD7AE, 0x6869,
	0xD7AF, 0x5E84,
	0xD7B0, 0x88C5,
	0xD7B1, 0x5986,
	0xD7B2, 0x649E,
	0xD7B3, 0x58EE,
	0xD7B4, 0x72B6,
	0xD7B5, 0x690E,
	0xD7B6, 0x9525,
	0xD7B7, 0x8FFD,
	0xD7B8, 0x8D58,
	0xD7B9, 0x5760,
	0xD7BA, 0x7F00,
	0xD7BB, 0x8C06,
	0xD7BC, 0x51C6,
	0xD7BD, 0x6349,
	0xD7BE, 0x62D9,
	0xD7BF, 0x5353,
	0xD7C0, 0x684C,
	0xD7C1, 0x7422,
	0xD7C2, 0x8301,
	0xD7C3, 0x914C,
	0xD7C4, 0x5544,
	0xD7C5, 0x7740,
	0xD7C6, 0x707C,
	0xD7C7, 0x6D4A,
	0xD7C8, 0x5179,
	0xD7C9, 0x54A8,
	0xD7CA, 0x8D44,
	0xD7CB, 0x59FF,
	0xD7CC, 0x6ECB,
	0xD7CD, 0x6DC4,
	0xD7CE, 0x5B5C,
	0xD7CF, 0x7D2B,
	0xD7D0, 0x4ED4,
	0xD7D1, 0x7C7D,
	0xD7D2, 0x6ED3,
	0xD7D3, 0x5B50,
	0xD7D4, 0x81EA,
	0xD7D5, 0x6E0D,
	0xD7D6, 0x5B57,
	0xD7D7, 0x9B03,
	0xD7D8, 0x68D5,
	0xD7D9, 0x8E2A,
	0xD7DA, 0x5B97,
	0xD7DB, 0x7EFC,
	0xD7DC, 0x603B,
	0xD7DD, 0x7EB5,
	0xD7DE, 0x90B9,
	0xD7DF, 0x8D70,
	0xD7E0, 0x594F,
	0xD7E1, 0x63CD,
	0xD7E2, 0x79DF,
	0xD7E3, 0x8DB3,
	0xD7E4, 0x5352,
	0xD7E5, 0x65CF,
	0xD7E6, 0x7956,
	0xD7E7, 0x8BC5,
	0xD7E8, 0x963B,
	0xD7E9, 0x7EC4,
	0xD7EA, 0x94BB,
	0xD7EB, 0x7E82,
	0xD7EC, 0x5634,
	0xD7ED, 0x9189,
	0xD7EE, 0x6700,
	0xD7EF, 0x7F6A,
	0xD7F0, 0x5C0A,
	0xD7F1, 0x9075,
	0xD7F2, 0x6628,
	0xD7F3, 0x5DE6,
	0xD7F4, 0x4F50,
	0xD7F5, 0x67DE,
	0xD7F6, 0x505A,
	0xD7F7, 0x4F5C,
	0xD7F8, 0x5750,
	0xD7F9, 0x5EA7,
	0xD84D, 0x8C48,
	0xD880, 0x8C88,
	0xD881, 0x8C8B,
	0xD8A1, 0x4E8D,
	0xD8A2, 0x4E0C,
	0xD8A3, 0x5140,
	0xD8A4, 0x4E10,
	0xD8A5, 0x5EFF,
	0xD8A6, 0x5345,
	0xD8A7, 0x4E15,
	0xD8A8, 0x4E98,
	0xD8A9, 0x4E1E,
	0xD8AA, 0x9B32,
	0xD8AB, 0x5B6C,
	0xD8AC, 0x5669,
	0xD8AD, 0x4E28,
	0xD8AE, 0x79BA,
	0xD8AF, 0x4E3F,
	0xD8B0, 0x5315,
	0xD8B1, 0x4E47,
	0xD8B2, 0x592D,
	0xD8B3, 0x723B,
	0xD8B4, 0x536E,
	0xD8B5, 0x6C10,
	0xD8B6, 0x56DF,
	0xD8B7, 0x80E4,
	0xD8B8, 0x9997,
	0xD8B9, 0x6BD3,
	0xD8BA, 0x777E,
	0xD8BB, 0x9F17,
	0xD8BC, 0x4E36,
	0xD8BD, 0x4E9F,
	0xD8BE, 0x9F10,
	0xD8BF, 0x4E5C,
	0xD8C0, 0x4E69,
	0xD8C1, 0x4E93,
	0xD8C2, 0x8288,
	0xD8C3, 0x5B5B,
	0xD8C4, 0x556C,
	0xD8C5, 0x560F,
	0xD8C6, 0x4EC4,
	0xD8C7, 0x538D,
	0xD8C8, 0x539D,
	0xD8C9, 0x53A3,
	0xD8CA, 0x53A5,
	0xD8CB, 0x53AE,
	0xD8CC, 0x9765,
	0xD8CD, 0x8D5D,
	0xD8CE, 0x531A,
	0xD8CF, 0x53F5,
	0xD8D0, 0x5326,
	0xD8D1, 0x532E,
	0xD8D2, 0x533E,
	0xD8D3, 0x8D5C,
	0xD8D4, 0x5366,
	0xD8D5, 0x5363,
	0xD8D6, 0x5202,
	0xD8D7, 0x5208,
	0xD8D8, 0x520E,
	0xD8D9, 0x522D,
	0xD8DA, 0x5233,
	0xD8DD, 0x524C,
	0xD8DE, 0x525E,
	0xD8DF, 0x5261,
	0xD8E0, 0x525C,
	0xD8E1, 0x84AF,
	0xD8E2, 0x527D,
	0xD8E3, 0x5282,
	0xD8E4, 0x5281,
	0xD8E5, 0x5290,
	0xD8E6, 0x5293,
	0xD8E7, 0x5182,
	0xD8E8, 0x7F54,
	0xD8E9, 0x4EBB,
	0xD8EA, 0x4EC3,
	0xD8EB, 0x4EC9,
	0xD8EC, 0x4EC2,
	0xD8ED, 0x4EE8,
	0xD8EE, 0x4EE1,
	0xD8EF, 0x4EEB,
	0xD8F0, 0x4EDE,
	0xD8F1, 0x4F1B,
	0xD8F2, 0x4EF3,
	0xD8F3, 0x4F22,
	0xD8F4, 0x4F64,
	0xD8F5, 0x4EF5,
	0xD8F6, 0x4F25,
	0xD8F7, 0x4F27,
	0xD8F8, 0x4F09,
	0xD8F9, 0x4F2B,
	0xD8FA, 0x4F5E,
	0xD8FB, 0x4F67,
	0xD8FC, 0x6538,
	0xD8FD, 0x4F5A,
	0xD8FE, 0x4F5D,
	0xD9A1, 0x4F5F,
	0xD9A2, 0x4F57,
	0xD9A3, 0x4F32,
	0xD9A4, 0x4F3D,
	0xD9A5, 0x4F76,
	0xD9A6, 0x4F74,
	0xD9A7, 0x4F91,
	0xD9A8, 0x4F89,
	0xD9A9, 0x4F83,
	0xD9AA, 0x4F8F,
	0xD9AB, 0x4F7E,
	0xD9AC, 0x4F7B,
	0xD9AD, 0x4FAA,
	0xD9AE, 0x4F7C,
	0xD9AF, 0x4FAC,
	0xD9B0, 0x4F94,
	0xD9B1, 0x4FE6,
	0xD9B2, 0x4FE8,
	0xD9B3, 0x4FEA,
	0xD9B4, 0x4FC5,
	0xD9B5, 0x4FDA,
	0xD9B6, 0x4FE3,
	0xD9B7, 0x4FDC,
	0xD9B8, 0x4FD1,
	0xD9B9, 0x4FDF,
	0xD9BA, 0x4FF8,
	0xD9BB, 0x5029,
	0xD9BC, 0x504C,
	0xD9BD, 0x4FF3,
	0xD9BE, 0x502C,
	0xD9BF, 0x500F,
	0xD9C0, 0x502E,
	0xD9C1, 0x502D,
	0xD9C2, 0x4FFE,
	0xD9C3, 0x501C,
	0xD9C4, 0x500C,
	0xD9C5, 0x5025,
	0xD9C6, 0x5028,
	0xD9C7, 0x507E,
	0xD9C8, 0x5043,
	0xD9C9, 0x5055,
	0xD9CA, 0x5048,
	0xD9CB, 0x504E,
	0xD9CC, 0x506C,
	0xD9CD, 0x507B,
	0xD9CE, 0x50A5,
	0xD9CF, 0x50A7,
	0xD9D0, 0x50A9,
	0xD9D1, 0x50BA,
	0xD9D2, 0x50D6,
	0xD9D3, 0x5106,
	0xD9D4, 0x50ED,
	0xD9D5, 0x50EC,
	0xD9D6, 0x50E6,
	0xD9D7, 0x50EE,
	0xD9D8, 0x5107,
	0xD9D9, 0x510B,
	0xD9DA, 0x4EDD,
	0xD9DB, 0x6C3D,
	0xD9DC, 0x4F58,
	0xD9DD, 0x4F65,
	0xD9DE, 0x4FCE,
	0xD9DF, 0x9FA0,
	0xD9E0, 0x6C46,
	0xD9E1, 0x7C74,
	0xD9E2, 0x516E,
	0xD9E3, 0x5DFD,
	0xD9E4, 0x9EC9,
	0xD9E5, 0x9998,
	0xD9E6, 0x5181,
	0xD9E7, 0x5914,
	0xD9E8, 0x52F9,
	0xD9E9, 0x530D,
	0xD9EA, 0x8A07,
	0xD9EB, 0x5310,
	0xD9EC, 0x51EB,
	0xD9ED, 0x5919,
	0xD9EE, 0x5155,
	0xD9EF, 0x4EA0,
	0xD9F0, 0x5156,
	0xD9F1, 0x4EB3,
	0xD9F2, 0x886E,
	0xD9F3, 0x88A4,
	0xD9F4, 0x4EB5,
	0xD9F5, 0x8114,
	0xD9F6, 0x88D2,
	0xD9F7, 0x7980,
	0xD9F8, 0x5B34,
	0xD9F9, 0x8803,
	0xD9FA, 0x7FB8,
	0xD9FB, 0x51AB,
	0xD9FC, 0x51B1,
	0xD9FD, 0x51BD,
	0xD9FE, 0x51BC,
	0xDA4F, 0x8D20,
	0xDA52, 0x8D57,
	0xDA53, 0x8D5F,
	0xDA54, 0x8D65,
	0xDA58, 0x8D6C,
	0xDA80, 0x8DA2,
	0xDA8E, 0x8DB2,
	0xDA91, 0x8DB9,
	0xDA92, 0x8DBB,
	0xDA93, 0x8DBD,
	0xDA97, 0x8DC5,
	0xDA9C, 0x8DCD,
	0xDA9D, 0x8DD0,
	0xDAA1, 0x51C7,
	0xDAA2, 0x5196,
	0xDAA3, 0x51A2,
	0xDAA4, 0x51A5,
	0xDAA5, 0x8BA0,
	0xDAA8, 0x8BAA,
	0xDAAB, 0x8BB7,
	0xDAAE, 0x8BCB,
	0xDAAF, 0x8BCF,
	0xDAB0, 0x8BCE,
	0xDAB4, 0x8BD6,
	0xDAB7, 0x8BDC,
	0xDABA, 0x8BE4,
	0xDABD, 0x8BEE,
	0xDABE, 0x8BF0,
	0xDABF, 0x8BF3,
	0xDAC0, 0x8BF6,
	0xDAC1, 0x8BF9,
	0xDAC2, 0x8BFC,
	0xDAC5, 0x8C02,
	0xDAC6, 0x8C04,
	0xDAC7, 0x8C07,
	0xDAC8, 0x8C0C,
	0xDAC9, 0x8C0F,
	0xDACF, 0x8C19,
	0xDAD0, 0x8C1B,
	0xDAD1, 0x8C18,
	0xDAD2, 0x8C1D,
	0xDAD6, 0x8C25,
	0xDAD7, 0x8C27,
	0xDAE0, 0x5369,
	0xDAE1, 0x537A,
	0xDAE2, 0x961D,
	0xDAE3, 0x9622,
	0xDAE4, 0x9621,
	0xDAE5, 0x9631,
	0xDAE6, 0x962A,
	0xDAE7, 0x963D,
	0xDAE8, 0x963C,
	0xDAE9, 0x9642,
	0xDAEA, 0x9649,
	0xDAEB, 0x9654,
	0xDAEC, 0x965F,
	0xDAED, 0x9667,
	0xDAEE, 0x966C,
	0xDAEF, 0x9672,
	0xDAF0, 0x9674,
	0xDAF1, 0x9688,
	0xDAF2, 0x968D,
	0xDAF3, 0x9697,
	0xDAF4, 0x96B0,
	0xDAF5, 0x9097,
	0xDAF6, 0x909B,
	0xDAF7, 0x909D,
	0xDAF8, 0x9099,
	0xDAF9, 0x90AC,
	0xDAFA, 0x90A1,
	0xDAFB, 0x90B4,
	0xDAFC, 0x90B3,
	0xDAFD, 0x90B6,
	0xDAFE, 0x90BA,
	0xDB40, 0x8DD5,
	0xDB43, 0x8DDC,
	0xDB4A, 0x8DE9,
	0xDB50, 0x8DF4,
	0xDB51, 0x8DF6,
	0xDB52, 0x8DFC,
	0xDB5D, 0x8E0B,
	0xDB73, 0x8E2B,
	0xDB74, 0x8E2D,
	0xDB75, 0x8E30,
	0xDB7E, 0x8E3E,
	0xDB80, 0x8E3F,
	0xDB81, 0x8E43,
	0xDB9F, 0x8E6E,
	0xDBA0, 0x8E71,
	0xDBA1, 0x90B8,
	0xDBA2, 0x90B0,
	0xDBA3, 0x90CF,
	0xDBA4, 0x90C5,
	0xDBA5, 0x90BE,
	0xDBA6, 0x90D0,
	0xDBA7, 0x90C4,
	0xDBA8, 0x90C7,
	0xDBA9, 0x90D3,
	0xDBAA, 0x90E6,
	0xDBAB, 0x90E2,
	0xDBAC, 0x90DC,
	0xDBAD, 0x90D7,
	0xDBAE, 0x90DB,
	0xDBAF, 0x90EB,
	0xDBB0, 0x90EF,
	0xDBB1, 0x90FE,
	0xDBB2, 0x9104,
	0xDBB3, 0x9122,
	0xDBB4, 0x911E,
	0xDBB5, 0x9123,
	0xDBB6, 0x9131,
	0xDBB7, 0x912F,
	0xDBB8, 0x9139,
	0xDBB9, 0x9143,
	0xDBBA, 0x9146,
	0xDBBB, 0x520D,
	0xDBBC, 0x5942,
	0xDBBD, 0x52A2,
	0xDBC0, 0x52BE,
	0xDBC1, 0x54FF,
	0xDBC2, 0x52D0,
	0xDBC3, 0x52D6,
	0xDBC4, 0x52F0,
	0xDBC5, 0x53DF,
	0xDBC6, 0x71EE,
	0xDBC7, 0x77CD,
	0xDBC8, 0x5EF4,
	0xDBC9, 0x51F5,
	0xDBCA, 0x51FC,
	0xDBCB, 0x9B2F,
	0xDBCC, 0x53B6,
	0xDBCD, 0x5F01,
	0xDBCE, 0x755A,
	0xDBCF, 0x5DEF,
	0xDBD0, 0x574C,
	0xDBD1, 0x57A9,
	0xDBD2, 0x57A1,
	0xDBD3, 0x587E,
	0xDBD4, 0x58BC,
	0xDBD5, 0x58C5,
	0xDBD6, 0x58D1,
	0xDBD7, 0x5729,
	0xDBD8, 0x572C,
	0xDBD9, 0x572A,
	0xDBDA, 0x5733,
	0xDBDB, 0x5739,
	0xDBDE, 0x575C,
	0xDBDF, 0x573B,
	0xDBE0, 0x5742,
	0xDBE1, 0x5769,
	0xDBE2, 0x5785,
	0xDBE3, 0x576B,
	0xDBE4, 0x5786,
	0xDBE5, 0x577C,
	0xDBE6, 0x577B,
	0xDBE7, 0x5768,
	0xDBE8, 0x576D,
	0xDBE9, 0x5776,
	0xDBEA, 0x5773,
	0xDBEB, 0x57AD,
	0xDBEC, 0x57A4,
	0xDBED, 0x578C,
	0xDBEE, 0x57B2,
	0xDBEF, 0x57CF,
	0xDBF0, 0x57A7,
	0xDBF1, 0x57B4,
	0xDBF2, 0x5793,
	0xDBF3, 0x57A0,
	0xDBF4, 0x57D5,
	0xDBF5, 0x57D8,
	0xDBF6, 0x57DA,
	0xDBF7, 0x57D9,
	0xDBF8, 0x57D2,
	0xDBF9, 0x57B8,
	0xDBFA, 0x57F4,
	0xDBFB, 0x57EF,
	0xDBFC, 0x57F8,
	0xDBFD, 0x57E4,
	0xDBFE, 0x57DD,
	0xDC40, 0x8E73,
	0xDC41, 0x8E75,
	0xDC49, 0x8E80,
	0xDC4D, 0x8E86,
	0xDC5F, 0x8E9D,
	0xDCA1, 0x580B,
	0xDCA2, 0x580D,
	0xDCA3, 0x57FD,
	0xDCA4, 0x57ED,
	0xDCA5, 0x5800,
	0xDCA6, 0x581E,
	0xDCA7, 0x5819,
	0xDCA8, 0x5844,
	0xDCA9, 0x5820,
	0xDCAA, 0x5865,
	0xDCAB, 0x586C,
	0xDCAC, 0x5881,
	0xDCAD, 0x5889,
	0xDCAE, 0x589A,
	0xDCAF, 0x5880,
	0xDCB0, 0x99A8,
	0xDCB1, 0x9F19,
	0xDCB2, 0x61FF,
	0xDCB3, 0x8279,
	0xDCB4, 0x827D,
	0xDCB5, 0x827F,
	0xDCB6, 0x828F,
	0xDCB7, 0x828A,
	0xDCB8, 0x82A8,
	0xDCB9, 0x8284,
	0xDCBA, 0x828E,
	0xDCBB, 0x8291,
	0xDCBC, 0x8297,
	0xDCBD, 0x8299,
	0xDCBE, 0x82AB,
	0xDCBF, 0x82B8,
	0xDCC0, 0x82BE,
	0xDCC1, 0x82B0,
	0xDCC2, 0x82C8,
	0xDCC3, 0x82CA,
	0xDCC4, 0x82E3,
	0xDCC5, 0x8298,
	0xDCC6, 0x82B7,
	0xDCC7, 0x82AE,
	0xDCCA, 0x82C1,
	0xDCCB, 0x82A9,
	0xDCCC, 0x82B4,
	0xDCCD, 0x82A1,
	0xDCCE, 0x82AA,
	0xDCCF, 0x829F,
	0xDCD0, 0x82C4,
	0xDCD1, 0x82CE,
	0xDCD2, 0x82A4,
	0xDCD3, 0x82E1,
	0xDCD4, 0x8309,
	0xDCD5, 0x82F7,
	0xDCD6, 0x82E4,
	0xDCD7, 0x830F,
	0xDCD8, 0x8307,
	0xDCD9, 0x82DC,
	0xDCDA, 0x82F4,
	0xDCDB, 0x82D2,
	0xDCDC, 0x82D8,
	0xDCDD, 0x830C,
	0xDCDE, 0x82FB,
	0xDCDF, 0x82D3,
	0xDCE0, 0x8311,
	0xDCE1, 0x831A,
	0xDCE2, 0x8306,
	0xDCE5, 0x82E0,
	0xDCE6, 0x82D5,
	0xDCE7, 0x831C,
	0xDCE8, 0x8351,
	0xDCEB, 0x8308,
	0xDCEC, 0x8392,
	0xDCED, 0x833C,
	0xDCEE, 0x8334,
	0xDCEF, 0x8331,
	0xDCF0, 0x839B,
	0xDCF1, 0x835E,
	0xDCF2, 0x832F,
	0xDCF3, 0x834F,
	0xDCF4, 0x8347,
	0xDCF5, 0x8343,
	0xDCF6, 0x835F,
	0xDCF7, 0x8340,
	0xDCF8, 0x8317,
	0xDCF9, 0x8360,
	0xDCFA, 0x832D,
	0xDCFB, 0x833A,
	0xDCFC, 0x8333,
	0xDCFD, 0x8366,
	0xDCFE, 0x8365,
	0xDDA1, 0x8368,
	0xDDA2, 0x831B,
	0xDDA3, 0x8369,
	0xDDA4, 0x836C,
	0xDDA5, 0x836A,
	0xDDA8, 0x83B0,
	0xDDA9, 0x8378,
	0xDDAC, 0x83A0,
	0xDDAD, 0x83AA,
	0xDDAE, 0x8393,
	0xDDAF, 0x839C,
	0xDDB0, 0x8385,
	0xDDB1, 0x837C,
	0xDDB2, 0x83B6,
	0xDDB3, 0x83A9,
	0xDDB4, 0x837D,
	0xDDB5, 0x83B8,
	0xDDB6, 0x837B,
	0xDDB7, 0x8398,
	0xDDB8, 0x839E,
	0xDDB9, 0x83A8,
	0xDDBA, 0x83BA,
	0xDDBB, 0x83BC,
	0xDDBC, 0x83C1,
	0xDDBD, 0x8401,
	0xDDBE, 0x83E5,
	0xDDBF, 0x83D8,
	0xDDC0, 0x5807,
	0xDDC1, 0x8418,
	0xDDC2, 0x840B,
	0xDDC3, 0x83DD,
	0xDDC4, 0x83FD,
	0xDDC5, 0x83D6,
	0xDDC6, 0x841C,
	0xDDC7, 0x8438,
	0xDDC8, 0x8411,
	0xDDC9, 0x8406,
	0xDDCA, 0x83D4,
	0xDDCB, 0x83DF,
	0xDDCC, 0x840F,
	0xDDCD, 0x8403,
	0xDDD0, 0x83EA,
	0xDDD1, 0x83C5,
	0xDDD2, 0x83C0,
	0xDDD3, 0x8426,
	0xDDD4, 0x83F0,
	0xDDD5, 0x83E1,
	0xDDD6, 0x845C,
	0xDDD7, 0x8451,
	0xDDD8, 0x845A,
	0xDDD9, 0x8459,
	0xDDDA, 0x8473,
	0xDDDD, 0x847A,
	0xDDDE, 0x8489,
	0xDDDF, 0x8478,
	0xDDE0, 0x843C,
	0xDDE1, 0x8446,
	0xDDE2, 0x8469,
	0xDDE3, 0x8476,
	0xDDE4, 0x848C,
	0xDDE5, 0x848E,
	0xDDE6, 0x8431,
	0xDDE7, 0x846D,
	0xDDE8, 0x84C1,
	0xDDE9, 0x84CD,
	0xDDEA, 0x84D0,
	0xDDEB, 0x84E6,
	0xDDEC, 0x84BD,
	0xDDED, 0x84D3,
	0xDDEE, 0x84CA,
	0xDDEF, 0x84BF,
	0xDDF0, 0x84BA,
	0xDDF1, 0x84E0,
	0xDDF2, 0x84A1,
	0xDDF3, 0x84B9,
	0xDDF4, 0x84B4,
	0xDDF5, 0x8497,
	0xDDF6, 0x84E5,
	0xDDF7, 0x84E3,
	0xDDF8, 0x850C,
	0xDDF9, 0x750D,
	0xDDFA, 0x8538,
	0xDDFB, 0x84F0,
	0xDDFC, 0x8539,
	0xDDFD, 0x851F,
	0xDDFE, 0x853A,
	0xDE61, 0x8F6A,
	0xDE62, 0x8F80,
	0xDE63, 0x8F8C,
	0xDE64, 0x8F92,
	0xDE65, 0x8F9D,
	0xDE6D, 0x8FAA,
	0xDE7D, 0x8FC3,
	0xDE7E, 0x8FC6,
	0xDE85, 0x8FCF,
	0xDE86, 0x8FD2,
	0xDE89, 0x8FDA,
	0xDE8C, 0x8FE3,
	0xDE8D, 0x8FE7,
	0xDE8E, 0x8FEC,
	0xDE8F, 0x8FEF,
	0xDE9C, 0x900C,
	0xDE9D, 0x900E,
	0xDE9E, 0x9013,
	0xDE9F, 0x9015,
	0xDEA0, 0x9018,
	0xDEA1, 0x8556,
	0xDEA2, 0x853B,
	0xDEA3, 0x84FF,
	0xDEA4, 0x84FC,
	0xDEA5, 0x8559,
	0xDEA6, 0x8548,
	0xDEA7, 0x8568,
	0xDEA8, 0x8564,
	0xDEA9, 0x855E,
	0xDEAA, 0x857A,
	0xDEAB, 0x77A2,
	0xDEAC, 0x8543,
	0xDEAD, 0x8572,
	0xDEAE, 0x857B,
	0xDEAF, 0x85A4,
	0xDEB0, 0x85A8,
	0xDEB1, 0x8587,
	0xDEB2, 0x858F,
	0xDEB3, 0x8579,
	0xDEB4, 0x85AE,
	0xDEB5, 0x859C,
	0xDEB6, 0x8585,
	0xDEB7, 0x85B9,
	0xDEB8, 0x85B7,
	0xDEB9, 0x85B0,
	0xDEBA, 0x85D3,
	0xDEBB, 0x85C1,
	0xDEBC, 0x85DC,
	0xDEBD, 0x85FF,
	0xDEBE, 0x8627,
	0xDEBF, 0x8605,
	0xDEC0, 0x8629,
	0xDEC1, 0x8616,
	0xDEC2, 0x863C,
	0xDEC3, 0x5EFE,
	0xDEC4, 0x5F08,
	0xDEC5, 0x593C,
	0xDEC6, 0x5941,
	0xDEC7, 0x8037,
	0xDEC8, 0x5955,
	0xDEC9, 0x595A,
	0xDECA, 0x5958,
	0xDECB, 0x530F,
	0xDECC, 0x5C22,
	0xDECD, 0x5C25,
	0xDECE, 0x5C2C,
	0xDECF, 0x5C34,
	0xDED0, 0x624C,
	0xDED1, 0x626A,
	0xDED2, 0x629F,
	0xDED3, 0x62BB,
	0xDED4, 0x62CA,
	0xDED5, 0x62DA,
	0xDED6, 0x62D7,
	0xDED7, 0x62EE,
	0xDED8, 0x6322,
	0xDED9, 0x62F6,
	0xDEDA, 0x6339,
	0xDEDB, 0x634B,
	0xDEDC, 0x6343,
	0xDEDD, 0x63AD,
	0xDEDE, 0x63F6,
	0xDEDF, 0x6371,
	0xDEE0, 0x637A,
	0xDEE1, 0x638E,
	0xDEE2, 0x63B4,
	0xDEE3, 0x636D,
	0xDEE4, 0x63AC,
	0xDEE5, 0x638A,
	0xDEE6, 0x6369,
	0xDEE7, 0x63AE,
	0xDEE8, 0x63BC,
	0xDEE9, 0x63F2,
	0xDEEA, 0x63F8,
	0xDEEB, 0x63E0,
	0xDEEC, 0x63FF,
	0xDEED, 0x63C4,
	0xDEEE, 0x63DE,
	0xDEEF, 0x63CE,
	0xDEF0, 0x6452,
	0xDEF1, 0x63C6,
	0xDEF2, 0x63BE,
	0xDEF3, 0x6445,
	0xDEF4, 0x6441,
	0xDEF5, 0x640B,
	0xDEF6, 0x641B,
	0xDEF7, 0x6420,
	0xDEF8, 0x640C,
	0xDEF9, 0x6426,
	0xDEFA, 0x6421,
	0xDEFB, 0x645E,
	0xDEFC, 0x6484,
	0xDEFD, 0x646D,
	0xDEFE, 0x6496,
	0xDF40, 0x9019,
	0xDF41, 0x901C,
	0xDF50, 0x9037,
	0xDF53, 0x903D,
	0xDF56, 0x9043,
	0xDF5E, 0x904E,
	0xDF6A, 0x9064,
	0xDF7D, 0x907E,
	0xDF7E, 0x9081,
	0xDF8B, 0x9092,
	0xDF8C, 0x9094,
	0xDF8D, 0x9096,
	0xDF8E, 0x9098,
	0xDF8F, 0x909A,
	0xDF90, 0x909C,
	0xDF99, 0x90AB,
	0xDF9A, 0x90AD,
	0xDF9B, 0x90B2,
	0xDF9C, 0x90B7,
	0xDFA1, 0x647A,
	0xDFA4, 0x6499,
	0xDFA5, 0x64BA,
	0xDFA6, 0x64C0,
	0xDFA7, 0x64D0,
	0xDFA8, 0x64D7,
	0xDFA9, 0x64E4,
	0xDFAA, 0x64E2,
	0xDFAB, 0x6509,
	0xDFAC, 0x6525,
	0xDFAD, 0x652E,
	0xDFAE, 0x5F0B,
	0xDFAF, 0x5FD2,
	0xDFB0, 0x7519,
	0xDFB1, 0x5F11,
	0xDFB2, 0x535F,
	0xDFB3, 0x53F1,
	0xDFB4, 0x53FD,
	0xDFB5, 0x53E9,
	0xDFB6, 0x53E8,
	0xDFB7, 0x53FB,
	0xDFB8, 0x5412,
	0xDFB9, 0x5416,
	0xDFBA, 0x5406,
	0xDFBB, 0x544B,
	0xDFBF, 0x5456,
	0xDFC0, 0x5443,
	0xDFC1, 0x5421,
	0xDFC2, 0x5457,
	0xDFC3, 0x5459,
	0xDFC4, 0x5423,
	0xDFC5, 0x5432,
	0xDFC6, 0x5482,
	0xDFC7, 0x5494,
	0xDFC8, 0x5477,
	0xDFC9, 0x5471,
	0xDFCA, 0x5464,
	0xDFCD, 0x5484,
	0xDFCE, 0x5476,
	0xDFCF, 0x5466,
	0xDFD0, 0x549D,
	0xDFD1, 0x54D0,
	0xDFD2, 0x54AD,
	0xDFD3, 0x54C2,
	0xDFD4, 0x54B4,
	0xDFD5, 0x54D2,
	0xDFD6, 0x54A7,
	0xDFD7, 0x54A6,
	0xDFDA, 0x5472,
	0xDFDB, 0x54A3,
	0xDFDC, 0x54D5,
	0xDFDD, 0x54BB,
	0xDFDE, 0x54BF,
	0xDFDF, 0x54CC,
	0xDFE2, 0x54DC,
	0xDFE5, 0x54A4,
	0xDFE6, 0x54DD,
	0xDFE7, 0x54CF,
	0xDFE8, 0x54DE,
	0xDFE9, 0x551B,
	0xDFEA, 0x54E7,
	0xDFEB, 0x5520,
	0xDFEC, 0x54FD,
	0xDFED, 0x5514,
	0xDFEE, 0x54F3,
	0xDFF1, 0x550F,
	0xDFF2, 0x5511,
	0xDFF3, 0x5527,
	0xDFF4, 0x552A,
	0xDFF5, 0x5567,
	0xDFF6, 0x558F,
	0xDFF7, 0x55B5,
	0xDFF8, 0x5549,
	0xDFF9, 0x556D,
	0xDFFA, 0x5541,
	0xDFFB, 0x5555,
	0xDFFC, 0x553F,
	0xDFFD, 0x5550,
	0xDFFE, 0x553C,
	0xE042, 0x90C6,
	0xE048, 0x90D2,
	0xE057, 0x90EC,
	0xE058, 0x90EE,
	0xE067, 0x9103,
	0xE080, 0x911D,
	0xE08F, 0x9130,
	0xE0A0, 0x9144,
	0xE0A1, 0x5537,
	0xE0A2, 0x5556,
	0xE0A6, 0x5533,
	0xE0A7, 0x5530,
	0xE0A8, 0x555C,
	0xE0A9, 0x558B,
	0xE0AA, 0x55D2,
	0xE0AB, 0x5583,
	0xE0AC, 0x55B1,
	0xE0AD, 0x55B9,
	0xE0AE, 0x5588,
	0xE0AF, 0x5581,
	0xE0B0, 0x559F,
	0xE0B1, 0x557E,
	0xE0B2, 0x55D6,
	0xE0B3, 0x5591,
	0xE0B4, 0x557B,
	0xE0B5, 0x55DF,
	0xE0B8, 0x5594,
	0xE0B9, 0x5599,
	0xE0BA, 0x55EA,
	0xE0BB, 0x55F7,
	0xE0BC, 0x55C9,
	0xE0BD, 0x561F,
	0xE0BE, 0x55D1,
	0xE0C1, 0x55D4,
	0xE0C2, 0x55E6,
	0xE0C3, 0x55DD,
	0xE0C4, 0x55C4,
	0xE0C5, 0x55EF,
	0xE0C6, 0x55E5,
	0xE0CB, 0x55E8,
	0xE0CC, 0x55F5,
	0xE0CD, 0x55E4,
	0xE0CE, 0x8F94,
	0xE0CF, 0x561E,
	0xE0D0, 0x5608,
	0xE0D1, 0x560C,
	0xE0D2, 0x5601,
	0xE0D3, 0x5624,
	0xE0D4, 0x5623,
	0xE0D5, 0x55FE,
	0xE0D6, 0x5600,
	0xE0D7, 0x5627,
	0xE0D8, 0x562D,
	0xE0D9, 0x5658,
	0xE0DA, 0x5639,
	0xE0DB, 0x5657,
	0xE0DC, 0x562C,
	0xE0DD, 0x564D,
	0xE0DE, 0x5662,
	0xE0DF, 0x5659,
	0xE0E0, 0x565C,
	0xE0E1, 0x564C,
	0xE0E2, 0x5654,
	0xE0E3, 0x5686,
	0xE0E4, 0x5664,
	0xE0E5, 0x5671,
	0xE0E6, 0x566B,
	0xE0E9, 0x5685,
	0xE0EA, 0x5693,
	0xE0EB, 0x56AF,
	0xE0EC, 0x56D4,
	0xE0ED, 0x56D7,
	0xE0EE, 0x56DD,
	0xE0EF, 0x56E1,
	0xE0F0, 0x56F5,
	0xE0F1, 0x56EB,
	0xE0F2, 0x56F9,
	0xE0F3, 0x56FF,
	0xE0F4, 0x5704,
	0xE0F5, 0x570A,
	0xE0F6, 0x5709,
	0xE0F7, 0x571C,
	0xE0F8, 0x5E0F,
	0xE0F9, 0x5E19,
	0xE0FA, 0x5E14,
	0xE0FB, 0x5E11,
	0xE0FC, 0x5E31,
	0xE140, 0x9145,
	0xE143, 0x9151,
	0xE151, 0x916B,
	0xE152, 0x916D,
	0xE153, 0x9173,
	0xE15C, 0x9186,
	0xE15D, 0x9188,
	0xE15E, 0x918A,
	0xE17E, 0x91BB,
	0xE18B, 0x91C8,
	0xE18C, 0x91CB,
	0xE18D, 0x91D0,
	0xE1A1, 0x5E37,
	0xE1A2, 0x5E44,
	0xE1A3, 0x5E54,
	0xE1A4, 0x5E5B,
	0xE1A5, 0x5E5E,
	0xE1A6, 0x5E61,
	0xE1A7, 0x5C8C,
	0xE1A8, 0x5C7A,
	0xE1A9, 0x5C8D,
	0xE1AA, 0x5C90,
	0xE1AB, 0x5C96,
	0xE1AC, 0x5C88,
	0xE1AF, 0x5C91,
	0xE1B0, 0x5C9A,
	0xE1B1, 0x5C9C,
	0xE1B2, 0x5CB5,
	0xE1B3, 0x5CA2,
	0xE1B4, 0x5CBD,
	0xE1B5, 0x5CAC,
	0xE1B6, 0x5CAB,
	0xE1B7, 0x5CB1,
	0xE1B8, 0x5CA3,
	0xE1B9, 0x5CC1,
	0xE1BA, 0x5CB7,
	0xE1BB, 0x5CC4,
	0xE1BC, 0x5CD2,
	0xE1BD, 0x5CE4,
	0xE1BE, 0x5CCB,
	0xE1BF, 0x5CE5,
	0xE1C2, 0x5D27,
	0xE1C3, 0x5D26,
	0xE1C4, 0x5D2E,
	0xE1C5, 0x5D24,
	0xE1C6, 0x5D1E,
	0xE1C7, 0x5D06,
	0xE1C8, 0x5D1B,
	0xE1C9, 0x5D58,
	0xE1CA, 0x5D3E,
	0xE1CB, 0x5D34,
	0xE1CC, 0x5D3D,
	0xE1CD, 0x5D6C,
	0xE1CE, 0x5D5B,
	0xE1CF, 0x5D6F,
	0xE1D0, 0x5D5D,
	0xE1D1, 0x5D6B,
	0xE1D2, 0x5D4B,
	0xE1D3, 0x5D4A,
	0xE1D4, 0x5D69,
	0xE1D5, 0x5D74,
	0xE1D6, 0x5D82,
	0xE1D7, 0x5D99,
	0xE1D8, 0x5D9D,
	0xE1D9, 0x8C73,
	0xE1DA, 0x5DB7,
	0xE1DB, 0x5DC5,
	0xE1DC, 0x5F73,
	0xE1DD, 0x5F77,
	0xE1DE, 0x5F82,
	0xE1DF, 0x5F87,
	0xE1E0, 0x5F89,
	0xE1E1, 0x5F8C,
	0xE1E2, 0x5F95,
	0xE1E3, 0x5F99,
	0xE1E4, 0x5F9C,
	0xE1E5, 0x5FA8,
	0xE1E6, 0x5FAD,
	0xE1E7, 0x5FB5,
	0xE1E8, 0x5FBC,
	0xE1E9, 0x8862,
	0xE1EA, 0x5F61,
	0xE1EB, 0x72AD,
	0xE1EC, 0x72B0,
	0xE1ED, 0x72B4,
	0xE1F0, 0x72C3,
	0xE1F1, 0x72C1,
	0xE1F2, 0x72CE,
	0xE1F3, 0x72CD,
	0xE1F4, 0x72D2,
	0xE1F5, 0x72E8,
	0xE1F6, 0x72EF,
	0xE1F7, 0x72E9,
	0xE1F8, 0x72F2,
	0xE1F9, 0x72F4,
	0xE1FA, 0x72F7,
	0xE1FB, 0x7301,
	0xE1FC, 0x72F3,
	0xE1FD, 0x7303,
	0xE1FE, 0x72FA,
	0xE2A1, 0x72FB,
	0xE2A2, 0x7317,
	0xE2A3, 0x7313,
	0xE2A4, 0x7321,
	0xE2A5, 0x730A,
	0xE2A6, 0x731E,
	0xE2A7, 0x731D,
	0xE2A8, 0x7315,
	0xE2A9, 0x7322,
	0xE2AA, 0x7339,
	0xE2AB, 0x7325,
	0xE2AC, 0x732C,
	0xE2AD, 0x7338,
	0xE2AE, 0x7331,
	0xE2AF, 0x7350,
	0xE2B0, 0x734D,
	0xE2B1, 0x7357,
	0xE2B2, 0x7360,
	0xE2B3, 0x736C,
	0xE2B4, 0x736F,
	0xE2B5, 0x737E,
	0xE2B6, 0x821B,
	0xE2B7, 0x5925,
	0xE2B8, 0x98E7,
	0xE2B9, 0x5924,
	0xE2BA, 0x5902,
	0xE2BB, 0x9963,
	0xE2C2, 0x9974,
	0xE2C3, 0x9977,
	0xE2C4, 0x997D,
	0xE2C5, 0x9980,
	0xE2C6, 0x9984,
	0xE2C7, 0x9987,
	0xE2C8, 0x998A,
	0xE2C9, 0x998D,
	0xE2CF, 0x5E80,
	0xE2D0, 0x5E91,
	0xE2D1, 0x5E8B,
	0xE2D2, 0x5E96,
	0xE2D3, 0x5EA5,
	0xE2D4, 0x5EA0,
	0xE2D5, 0x5EB9,
	0xE2D6, 0x5EB5,
	0xE2D7, 0x5EBE,
	0xE2D8, 0x5EB3,
	0xE2D9, 0x8D53,
	0xE2DA, 0x5ED2,
	0xE2DB, 0x5ED1,
	0xE2DC, 0x5EDB,
	0xE2DD, 0x5EE8,
	0xE2DE, 0x5EEA,
	0xE2DF, 0x81BA,
	0xE2E0, 0x5FC4,
	0xE2E1, 0x5FC9,
	0xE2E2, 0x5FD6,
	0xE2E3, 0x5FCF,
	0xE2E4, 0x6003,
	0xE2E5, 0x5FEE,
	0xE2E6, 0x6004,
	0xE2E7, 0x5FE1,
	0xE2E8, 0x5FE4,
	0xE2E9, 0x5FFE,
	0xE2EC, 0x5FEA,
	0xE2ED, 0x5FED,
	0xE2EE, 0x5FF8,
	0xE2EF, 0x6019,
	0xE2F0, 0x6035,
	0xE2F1, 0x6026,
	0xE2F2, 0x601B,
	0xE2F3, 0x600F,
	0xE2F4, 0x600D,
	0xE2F5, 0x6029,
	0xE2F6, 0x602B,
	0xE2F7, 0x600A,
	0xE2F8, 0x603F,
	0xE2F9, 0x6021,
	0xE2FC, 0x607B,
	0xE2FD, 0x607A,
	0xE2FE, 0x6042,
	0xE3A1, 0x606A,
	0xE3A2, 0x607D,
	0xE3A3, 0x6096,
	0xE3A4, 0x609A,
	0xE3A5, 0x60AD,
	0xE3A6, 0x609D,
	0xE3A7, 0x6083,
	0xE3A8, 0x6092,
	0xE3A9, 0x608C,
	0xE3AA, 0x609B,
	0xE3AB, 0x60EC,
	0xE3AC, 0x60BB,
	0xE3AD, 0x60B1,
	0xE3AE, 0x60DD,
	0xE3AF, 0x60D8,
	0xE3B0, 0x60C6,
	0xE3B1, 0x60DA,
	0xE3B2, 0x60B4,
	0xE3B3, 0x6120,
	0xE3B4, 0x6126,
	0xE3B5, 0x6115,
	0xE3B6, 0x6123,
	0xE3B7, 0x60F4,
	0xE3B8, 0x6100,
	0xE3B9, 0x610E,
	0xE3BA, 0x612B,
	0xE3BB, 0x614A,
	0xE3BC, 0x6175,
	0xE3BD, 0x61AC,
	0xE3BE, 0x6194,
	0xE3BF, 0x61A7,
	0xE3C0, 0x61B7,
	0xE3C1, 0x61D4,
	0xE3C2, 0x61F5,
	0xE3C3, 0x5FDD,
	0xE3C4, 0x96B3,
	0xE3C5, 0x95E9,
	0xE3C6, 0x95EB,
	0xE3C7, 0x95F1,
	0xE3C8, 0x95F3,
	0xE3CB, 0x95FC,
	0xE3CC, 0x95FE,
	0xE3CF, 0x9606,
	0xE3D0, 0x9608,
	0xE3D5, 0x960F,
	0xE3D6, 0x9612,
	0xE3DC, 0x4E2C,
	0xE3DD, 0x723F,
	0xE3DE, 0x6215,
	0xE3DF, 0x6C35,
	0xE3E0, 0x6C54,
	0xE3E1, 0x6C5C,
	0xE3E2, 0x6C4A,
	0xE3E3, 0x6CA3,
	0xE3E4, 0x6C85,
	0xE3E5, 0x6C90,
	0xE3E6, 0x6C94,
	0xE3E7, 0x6C8C,
	0xE3EA, 0x6C74,
	0xE3EB, 0x6C76,
	0xE3EC, 0x6C86,
	0xE3ED, 0x6CA9,
	0xE3EE, 0x6CD0,
	0xE3EF, 0x6CD4,
	0xE3F0, 0x6CAD,
	0xE3F3, 0x6CF1,
	0xE3F4, 0x6CD7,
	0xE3F5, 0x6CB2,
	0xE3F6, 0x6CE0,
	0xE3F7, 0x6CD6,
	0xE3F8, 0x6CFA,
	0xE3F9, 0x6CEB,
	0xE3FA, 0x6CEE,
	0xE3FB, 0x6CB1,
	0xE3FC, 0x6CD3,
	0xE3FD, 0x6CEF,
	0xE3FE, 0x6CFE,
	0xE4A1, 0x6D39,
	0xE4A2, 0x6D27,
	0xE4A3, 0x6D0C,
	0xE4A4, 0x6D43,
	0xE4A5, 0x6D48,
	0xE4A6, 0x6D07,
	0xE4A7, 0x6D04,
	0xE4A8, 0x6D19,
	0xE4A9, 0x6D0E,
	0xE4AA, 0x6D2B,
	0xE4AB, 0x6D4D,
	0xE4AC, 0x6D2E,
	0xE4AD, 0x6D35,
	0xE4AE, 0x6D1A,
	0xE4AF, 0x6D4F,
	0xE4B0, 0x6D52,
	0xE4B1, 0x6D54,
	0xE4B2, 0x6D33,
	0xE4B3, 0x6D91,
	0xE4B4, 0x6D6F,
	0xE4B5, 0x6D9E,
	0xE4B6, 0x6DA0,
	0xE4B7, 0x6D5E,
	0xE4BA, 0x6D5C,
	0xE4BB, 0x6D60,
	0xE4BC, 0x6D7C,
	0xE4BD, 0x6D63,
	0xE4BE, 0x6E1A,
	0xE4BF, 0x6DC7,
	0xE4C0, 0x6DC5,
	0xE4C1, 0x6DDE,
	0xE4C2, 0x6E0E,
	0xE4C3, 0x6DBF,
	0xE4C4, 0x6DE0,
	0xE4C5, 0x6E11,
	0xE4C6, 0x6DE6,
	0xE4C7, 0x6DDD,
	0xE4C8, 0x6DD9,
	0xE4C9, 0x6E16,
	0xE4CA, 0x6DAB,
	0xE4CB, 0x6E0C,
	0xE4CC, 0x6DAE,
	0xE4CD, 0x6E2B,
	0xE4CE, 0x6E6E,
	0xE4CF, 0x6E4E,
	0xE4D0, 0x6E6B,
	0xE4D1, 0x6EB2,
	0xE4D2, 0x6E5F,
	0xE4D3, 0x6E86,
	0xE4D6, 0x6E32,
	0xE4D7, 0x6E25,
	0xE4D8, 0x6E44,
	0xE4D9, 0x6EDF,
	0xE4DA, 0x6EB1,
	0xE4DB, 0x6E98,
	0xE4DC, 0x6EE0,
	0xE4DD, 0x6F2D,
	0xE4DE, 0x6EE2,
	0xE4DF, 0x6EA5,
	0xE4E0, 0x6EA7,
	0xE4E1, 0x6EBD,
	0xE4E2, 0x6EBB,
	0xE4E3, 0x6EB7,
	0xE4E4, 0x6ED7,
	0xE4E5, 0x6EB4,
	0xE4E6, 0x6ECF,
	0xE4E7, 0x6E8F,
	0xE4E8, 0x6EC2,
	0xE4E9, 0x6E9F,
	0xE4EA, 0x6F62,
	0xE4ED, 0x6F24,
	0xE4EE, 0x6F15,
	0xE4EF, 0x6EF9,
	0xE4F0, 0x6F2F,
	0xE4F1, 0x6F36,
	0xE4F2, 0x6F4B,
	0xE4F3, 0x6F74,
	0xE4F4, 0x6F2A,
	0xE4F5, 0x6F09,
	0xE4F6, 0x6F29,
	0xE4F7, 0x6F89,
	0xE4F8, 0x6F8D,
	0xE4F9, 0x6F8C,
	0xE4FA, 0x6F78,
	0xE4FB, 0x6F72,
	0xE4FC, 0x6F7C,
	0xE4FD, 0x6F7A,
	0xE4FE, 0x6FD1,
	0xE5A0, 0x936B,
	0xE5A1, 0x6FC9,
	0xE5A2, 0x6FA7,
	0xE5A3, 0x6FB9,
	0xE5A4, 0x6FB6,
	0xE5A5, 0x6FC2,
	0xE5A6, 0x6FE1,
	0xE5A7, 0x6FEE,
	0xE5A8, 0x6FDE,
	0xE5A9, 0x6FE0,
	0xE5AA, 0x6FEF,
	0xE5AB, 0x701A,
	0xE5AC, 0x7023,
	0xE5AD, 0x701B,
	0xE5AE, 0x7039,
	0xE5AF, 0x7035,
	0xE5B0, 0x704F,
	0xE5B1, 0x705E,
	0xE5B2, 0x5B80,
	0xE5B3, 0x5B84,
	0xE5B4, 0x5B95,
	0xE5B5, 0x5B93,
	0xE5B6, 0x5BA5,
	0xE5B7, 0x5BB8,
	0xE5B8, 0x752F,
	0xE5B9, 0x9A9E,
	0xE5BA, 0x6434,
	0xE5BB, 0x5BE4,
	0xE5BC, 0x5BEE,
	0xE5BD, 0x8930,
	0xE5BE, 0x5BF0,
	0xE5BF, 0x8E47,
	0xE5C0, 0x8B07,
	0xE5C1, 0x8FB6,
	0xE5C2, 0x8FD3,
	0xE5C3, 0x8FD5,
	0xE5C4, 0x8FE5,
	0xE5C5, 0x8FEE,
	0xE5C6, 0x8FE4,
	0xE5C7, 0x8FE9,
	0xE5C8, 0x8FE6,
	0xE5C9, 0x8FF3,
	0xE5CA, 0x8FE8,
	0xE5CB, 0x9005,
	0xE5CC, 0x9004,
	0xE5CD, 0x900B,
	0xE5CE, 0x9026,
	0xE5CF, 0x9011,
	0xE5D0, 0x900D,
	0xE5D1, 0x9016,
	0xE5D2, 0x9021,
	0xE5D5, 0x902D,
	0xE5D6, 0x902F,
	0xE5D7, 0x9044,
	0xE5DA, 0x9050,
	0xE5DB, 0x9068,
	0xE5DC, 0x9058,
	0xE5DD, 0x9062,
	0xE5DE, 0x905B,
	0xE5DF, 0x66B9,
	0xE5E0, 0x9074,
	0xE5E1, 0x907D,
	0xE5E2, 0x9082,
	0xE5E3, 0x9088,
	0xE5E4, 0x9083,
	0xE5E5, 0x908B,
	0xE5E6, 0x5F50,
	0xE5E7, 0x5F57,
	0xE5E8, 0x5F56,
	0xE5E9, 0x5F58,
	0xE5EA, 0x5C3B,
	0xE5EB, 0x54AB,
	0xE5EC, 0x5C50,
	0xE5ED, 0x5C59,
	0xE5EE, 0x5B71,
	0xE5EF, 0x5C63,
	0xE5F0, 0x5C66,
	0xE5F1, 0x7FBC,
	0xE5F2, 0x5F2A,
	0xE5F3, 0x5F29,
	0xE5F4, 0x5F2D,
	0xE5F5, 0x8274,
	0xE5F6, 0x5F3C,
	0xE5F7, 0x9B3B,
	0xE5F8, 0x5C6E,
	0xE5F9, 0x5981,
	0xE5FA, 0x5983,
	0xE5FB, 0x598D,
	0xE5FE, 0x59A3,
	0xE6A1, 0x5997,
	0xE6A2, 0x59CA,
	0xE6A3, 0x59AB,
	0xE6A4, 0x599E,
	0xE6A5, 0x59A4,
	0xE6A6, 0x59D2,
	0xE6A7, 0x59B2,
	0xE6A8, 0x59AF,
	0xE6A9, 0x59D7,
	0xE6AA, 0x59BE,
	0xE6AD, 0x59DD,
	0xE6AE, 0x5A08,
	0xE6AF, 0x59E3,
	0xE6B0, 0x59D8,
	0xE6B1, 0x59F9,
	0xE6B2, 0x5A0C,
	0xE6B3, 0x5A09,
	0xE6B4, 0x5A32,
	0xE6B5, 0x5A34,
	0xE6B6, 0x5A11,
	0xE6B7, 0x5A23,
	0xE6B8, 0x5A13,
	0xE6B9, 0x5A40,
	0xE6BA, 0x5A67,
	0xE6BB, 0x5A4A,
	0xE6BC, 0x5A55,
	0xE6BD, 0x5A3C,
	0xE6BE, 0x5A62,
	0xE6BF, 0x5A75,
	0xE6C0, 0x80EC,
	0xE6C1, 0x5AAA,
	0xE6C2, 0x5A9B,
	0xE6C3, 0x5A77,
	0xE6C4, 0x5A7A,
	0xE6C5, 0x5ABE,
	0xE6C6, 0x5AEB,
	0xE6C7, 0x5AB2,
	0xE6C8, 0x5AD2,
	0xE6C9, 0x5AD4,
	0xE6CA, 0x5AB8,
	0xE6CB, 0x5AE0,
	0xE6CC, 0x5AE3,
	0xE6CD, 0x5AF1,
	0xE6CE, 0x5AD6,
	0xE6CF, 0x5AE6,
	0xE6D0, 0x5AD8,
	0xE6D1, 0x5ADC,
	0xE6D2, 0x5B09,
	0xE6D3, 0x5B17,
	0xE6D4, 0x5B16,
	0xE6D5, 0x5B32,
	0xE6D6, 0x5B37,
	0xE6D7, 0x5B40,
	0xE6D8, 0x5C15,
	0xE6D9, 0x5C1C,
	0xE6DA, 0x5B5A,
	0xE6DB, 0x5B65,
	0xE6DC, 0x5B73,
	0xE6DD, 0x5B51,
	0xE6DE, 0x5B53,
	0xE6DF, 0x5B62,
	0xE6E0, 0x9A75,
	0xE6E3, 0x9A7A,
	0xE6E4, 0x9A7F,
	0xE6E5, 0x9A7D,
	0xE6E8, 0x9A85,
	0xE6E9, 0x9A88,
	0xE6EA, 0x9A8A,
	0xE6EB, 0x9A90,
	0xE6EE, 0x9A96,
	0xE6EF, 0x9A98,
	0xE6F7, 0x9AA5,
	0xE6F8, 0x9AA7,
	0xE6F9, 0x7E9F,
	0xE6FA, 0x7EA1,
	0xE6FB, 0x7EA3,
	0xE6FC, 0x7EA5,
	0xE7A1, 0x7EAD,
	0xE7A2, 0x7EB0,
	0xE7A3, 0x7EBE,
	0xE7A7, 0x7EC9,
	0xE7AA, 0x7ED0,
	0xE7AB, 0x7ED4,
	0xE7AC, 0x7ED7,
	0xE7AD, 0x7EDB,
	0xE7B0, 0x7EE8,
	0xE7B1, 0x7EEB,
	0xE7B6, 0x7F0D,
	0xE7B7, 0x7EF6,
	0xE7BA, 0x7EFE,
	0xE7C2, 0x7F0F,
	0xE7C5, 0x7F17,
	0xE7C6, 0x7F19,
	0xE7C7, 0x7F1C,
	0xE7C8, 0x7F1B,
	0xE7C9, 0x7F1F,
	0xE7DA, 0x7F35,
	0xE7DB, 0x5E7A,
	0xE7DC, 0x757F,
	0xE7DD, 0x5DDB,
	0xE7DE, 0x753E,
	0xE7DF, 0x9095,
	0xE7E0, 0x738E,
	0xE7E1, 0x7391,
	0xE7E2, 0x73AE,
	0xE7E3, 0x73A2,
	0xE7E4, 0x739F,
	0xE7E5, 0x73CF,
	0xE7E6, 0x73C2,
	0xE7E7, 0x73D1,
	0xE7E8, 0x73B7,
	0xE7E9, 0x73B3,
	0xE7EA, 0x73C0,
	0xE7EB, 0x73C9,
	0xE7EC, 0x73C8,
	0xE7ED, 0x73E5,
	0xE7EE, 0x73D9,
	0xE7EF, 0x987C,
	0xE7F0, 0x740A,
	0xE7F1, 0x73E9,
	0xE7F2, 0x73E7,
	0xE7F3, 0x73DE,
	0xE7F4, 0x73BA,
	0xE7F5, 0x73F2,
	0xE7F6, 0x740F,
	0xE7F7, 0x742A,
	0xE7F8, 0x745B,
	0xE7F9, 0x7426,
	0xE7FA, 0x7425,
	0xE7FB, 0x7428,
	0xE7FC, 0x7430,
	0xE7FD, 0x742E,
	0xE7FE, 0x742C,
	0xE895, 0x9491,
	0xE896, 0x9496,
	0xE897, 0x9498,
	0xE898, 0x94C7,
	0xE899, 0x94CF,
	0xE89C, 0x94DA,
	0xE89D, 0x94E6,
	0xE89E, 0x94FB,
	0xE89F, 0x951C,
	0xE8A0, 0x9520,
	0xE8A1, 0x741B,
	0xE8A2, 0x741A,
	0xE8A3, 0x7441,
	0xE8A4, 0x745C,
	0xE8A5, 0x7457,
	0xE8A6, 0x7455,
	0xE8A7, 0x7459,
	0xE8A8, 0x7477,
	0xE8A9, 0x746D,
	0xE8AA, 0x747E,
	0xE8AB, 0x749C,
	0xE8AC, 0x748E,
	0xE8AF, 0x7487,
	0xE8B0, 0x748B,
	0xE8B1, 0x749E,
	0xE8B4, 0x7490,
	0xE8B5, 0x74A7,
	0xE8B6, 0x74D2,
	0xE8B7, 0x74BA,
	0xE8BB, 0x674C,
	0xE8BC, 0x6753,
	0xE8BD, 0x675E,
	0xE8BE, 0x6748,
	0xE8BF, 0x6769,
	0xE8C0, 0x67A5,
	0xE8C1, 0x6787,
	0xE8C2, 0x676A,
	0xE8C3, 0x6773,
	0xE8C4, 0x6798,
	0xE8C5, 0x67A7,
	0xE8C6, 0x6775,
	0xE8C7, 0x67A8,
	0xE8C8, 0x679E,
	0xE8C9, 0x67AD,
	0xE8CA, 0x678B,
	0xE8CB, 0x6777,
	0xE8CC, 0x677C,
	0xE8CD, 0x67F0,
	0xE8CE, 0x6809,
	0xE8CF, 0x67D8,
	0xE8D0, 0x680A,
	0xE8D1, 0x67E9,
	0xE8D2, 0x67B0,
	0xE8D3, 0x680C,
	0xE8D4, 0x67D9,
	0xE8D5, 0x67B5,
	0xE8D6, 0x67DA,
	0xE8D7, 0x67B3,
	0xE8D8, 0x67DD,
	0xE8D9, 0x6800,
	0xE8DA, 0x67C3,
	0xE8DB, 0x67B8,
	0xE8DC, 0x67E2,
	0xE8DD, 0x680E,
	0xE8DE, 0x67C1,
	0xE8DF, 0x67FD,
	0xE8E4, 0x684E,
	0xE8E5, 0x6862,
	0xE8E6, 0x6844,
	0xE8E7, 0x6864,
	0xE8E8, 0x6883,
	0xE8E9, 0x681D,
	0xE8EA, 0x6855,
	0xE8EB, 0x6866,
	0xE8EC, 0x6841,
	0xE8ED, 0x6867,
	0xE8EE, 0x6840,
	0xE8EF, 0x683E,
	0xE8F0, 0x684A,
	0xE8F1, 0x6849,
	0xE8F2, 0x6829,
	0xE8F3, 0x68B5,
	0xE8F4, 0x688F,
	0xE8F5, 0x6874,
	0xE8F6, 0x6877,
	0xE8F7, 0x6893,
	0xE8F8, 0x686B,
	0xE8F9, 0x68C2,
	0xE8FA, 0x696E,
	0xE8FB, 0x68FC,
	0xE8FE, 0x68F9,
	0xE940, 0x9527,
	0xE941, 0x9533,
	0xE942, 0x953D,
	0xE943, 0x9543,
	0xE944, 0x9548,
	0xE945, 0x954B,
	0xE946, 0x9555,
	0xE947, 0x955A,
	0xE948, 0x9560,
	0xE949, 0x956E,
	0xE9A1, 0x6924,
	0xE9A2, 0x68F0,
	0xE9A3, 0x690B,
	0xE9A4, 0x6901,
	0xE9A5, 0x6957,
	0xE9A6, 0x68E3,
	0xE9A7, 0x6910,
	0xE9A8, 0x6971,
	0xE9A9, 0x6939,
	0xE9AA, 0x6960,
	0xE9AB, 0x6942,
	0xE9AC, 0x695D,
	0xE9AD, 0x6984,
	0xE9AE, 0x696B,
	0xE9AF, 0x6980,
	0xE9B0, 0x6998,
	0xE9B1, 0x6978,
	0xE9B2, 0x6934,
	0xE9B3, 0x69CC,
	0xE9B6, 0x69CE,
	0xE9B7, 0x6989,
	0xE9B8, 0x6966,
	0xE9B9, 0x6963,
	0xE9BA, 0x6979,
	0xE9BB, 0x699B,
	0xE9BC, 0x69A7,
	0xE9BD, 0x69BB,
	0xE9BE, 0x69AB,
	0xE9BF, 0x69AD,
	0xE9C0, 0x69D4,
	0xE9C1, 0x69B1,
	0xE9C2, 0x69C1,
	0xE9C3, 0x69CA,
	0xE9C4, 0x69DF,
	0xE9C5, 0x6995,
	0xE9C6, 0x69E0,
	0xE9C7, 0x698D,
	0xE9C8, 0x69FF,
	0xE9C9, 0x6A2F,
	0xE9CA, 0x69ED,
	0xE9CD, 0x6A65,
	0xE9CE, 0x69F2,
	0xE9CF, 0x6A44,
	0xE9D0, 0x6A3E,
	0xE9D1, 0x6AA0,
	0xE9D2, 0x6A50,
	0xE9D3, 0x6A5B,
	0xE9D4, 0x6A35,
	0xE9D5, 0x6A8E,
	0xE9D6, 0x6A79,
	0xE9D7, 0x6A3D,
	0xE9D8, 0x6A28,
	0xE9D9, 0x6A58,
	0xE9DA, 0x6A7C,
	0xE9DB, 0x6A91,
	0xE9DC, 0x6A90,
	0xE9DD, 0x6AA9,
	0xE9DE, 0x6A97,
	0xE9DF, 0x6AAB,
	0xE9E0, 0x7337,
	0xE9E1, 0x7352,
	0xE9E4, 0x6B87,
	0xE9E5, 0x6B84,
	0xE9E8, 0x6B8D,
	0xE9EB, 0x6BA1,
	0xE9EC, 0x6BAA,
	0xE9ED, 0x8F6B,
	0xE9EE, 0x8F6D,
	0xE9F4, 0x8F78,
	0xE9F5, 0x8F77,
	0xE9F8, 0x8F7C,
	0xE9F9, 0x8F7E,
	0xE9FC, 0x8F84,
	0xE9FD, 0x8F87,
	0xE9FE, 0x8F8B,
	0xEA5C, 0x95EC,
	0xEA5D, 0x95FF,
	0xEA5E, 0x9607,
	0xEA5F, 0x9613,
	0xEA60, 0x9618,
	0xEA61, 0x961B,
	0xEA62, 0x961E,
	0xEA63, 0x9620,
	0xEA74, 0x963E,
	0xEA75, 0x9641,
	0xEA76, 0x9643,
	0xEA77, 0x964A,
	0xEA86, 0x9660,
	0xEA87, 0x9663,
	0xEA8A, 0x966B,
	0xEA90, 0x9673,
	0xEA9E, 0x9687,
	0xEAA4, 0x8F98,
	0xEAA5, 0x8F9A,
	0xEAA6, 0x8ECE,
	0xEAA7, 0x620B,
	0xEAA8, 0x6217,
	0xEAA9, 0x621B,
	0xEAAA, 0x621F,
	0xEAAB, 0x6222,
	0xEAAC, 0x6221,
	0xEAAD, 0x6225,
	0xEAAE, 0x6224,
	0xEAAF, 0x622C,
	0xEAB0, 0x81E7,
	0xEAB1, 0x74EF,
	0xEAB2, 0x74F4,
	0xEAB3, 0x74FF,
	0xEAB4, 0x750F,
	0xEAB5, 0x7511,
	0xEAB6, 0x7513,
	0xEAB7, 0x6534,
	0xEABB, 0x660A,
	0xEABC, 0x6619,
	0xEABD, 0x6772,
	0xEABE, 0x6603,
	0xEABF, 0x6615,
	0xEAC0, 0x6600,
	0xEAC1, 0x7085,
	0xEAC2, 0x66F7,
	0xEAC3, 0x661D,
	0xEAC4, 0x6634,
	0xEAC5, 0x6631,
	0xEAC6, 0x6636,
	0xEAC7, 0x6635,
	0xEAC8, 0x8006,
	0xEAC9, 0x665F,
	0xEACA, 0x6654,
	0xEACB, 0x6641,
	0xEACC, 0x664F,
	0xEACD, 0x6656,
	0xEACE, 0x6661,
	0xEACF, 0x6657,
	0xEAD0, 0x6677,
	0xEAD1, 0x6684,
	0xEAD2, 0x668C,
	0xEAD3, 0x66A7,
	0xEAD4, 0x669D,
	0xEAD5, 0x66BE,
	0xEAD8, 0x66E6,
	0xEAD9, 0x66E9,
	0xEADC, 0x8D36,
	0xEADD, 0x8D3B,
	0xEADE, 0x8D3D,
	0xEADF, 0x8D40,
	0xEAE4, 0x8D47,
	0xEAE5, 0x8D4D,
	0xEAE6, 0x8D55,
	0xEAE7, 0x8D59,
	0xEAE8, 0x89C7,
	0xEAF0, 0x726E,
	0xEAF1, 0x729F,
	0xEAF2, 0x725D,
	0xEAF3, 0x7266,
	0xEAF4, 0x726F,
	0xEAF7, 0x7284,
	0xEAF8, 0x728B,
	0xEAF9, 0x728D,
	0xEAFA, 0x728F,
	0xEAFB, 0x7292,
	0xEAFC, 0x6308,
	0xEAFD, 0x6332,
	0xEAFE, 0x63B0,
	0xEB40, 0x968C,
	0xEB41, 0x968E,
	0xEB63, 0x96BF,
	0xEB66, 0x96C8,
	0xEB7E, 0x96EB,
	0xEB88, 0x96F8,
	0xEB8D, 0x96FF,
	0xEB90, 0x9705,
	0xEB9E, 0x971D,
	0xEBA1, 0x643F,
	0xEBA2, 0x64D8,
	0xEBA3, 0x8004,
	0xEBA4, 0x6BEA,
	0xEBA5, 0x6BF3,
	0xEBA6, 0x6BFD,
	0xEBA7, 0x6BF5,
	0xEBA8, 0x6BF9,
	0xEBA9, 0x6C05,
	0xEBAA, 0x6C07,
	0xEBAB, 0x6C06,
	0xEBAC, 0x6C0D,
	0xEBAD, 0x6C15,
	0xEBB1, 0x6C21,
	0xEBB2, 0x6C29,
	0xEBB3, 0x6C24,
	0xEBB4, 0x6C2A,
	0xEBB5, 0x6C32,
	0xEBB6, 0x6535,
	0xEBB7, 0x6555,
	0xEBB8, 0x656B,
	0xEBB9, 0x724D,
	0xEBBA, 0x7252,
	0xEBBB, 0x7256,
	0xEBBC, 0x7230,
	0xEBBD, 0x8662,
	0xEBBE, 0x5216,
	0xEBBF, 0x809F,
	0xEBC0, 0x809C,
	0xEBC1, 0x8093,
	0xEBC2, 0x80BC,
	0xEBC3, 0x670A,
	0xEBC4, 0x80BD,
	0xEBC5, 0x80B1,
	0xEBC6, 0x80AB,
	0xEBC7, 0x80AD,
	0xEBC8, 0x80B4,
	0xEBC9, 0x80B7,
	0xEBCE, 0x80DB,
	0xEBCF, 0x80C2,
	0xEBD0, 0x80C4,
	0xEBD1, 0x80D9,
	0xEBD2, 0x80CD,
	0xEBD3, 0x80D7,
	0xEBD4, 0x6710,
	0xEBD5, 0x80DD,
	0xEBD6, 0x80EB,
	0xEBD7, 0x80F1,
	0xEBD8, 0x80F4,
	0xEBD9, 0x80ED,
	0xEBDC, 0x80F2,
	0xEBDD, 0x80FC,
	0xEBDE, 0x6715,
	0xEBDF, 0x8112,
	0xEBE0, 0x8C5A,
	0xEBE1, 0x8136,
	0xEBE2, 0x811E,
	0xEBE3, 0x812C,
	0xEBE4, 0x8118,
	0xEBE5, 0x8132,
	0xEBE6, 0x8148,
	0xEBE7, 0x814C,
	0xEBE8, 0x8153,
	0xEBE9, 0x8174,
	0xEBEC, 0x8171,
	0xEBED, 0x8160,
	0xEBEE, 0x8169,
	0xEBF1, 0x816D,
	0xEBF2, 0x8167,
	0xEBF3, 0x584D,
	0xEBF4, 0x5AB5,
	0xEBF5, 0x8188,
	0xEBF6, 0x8182,
	0xEBF7, 0x8191,
	0xEBF8, 0x6ED5,
	0xEBF9, 0x81A3,
	0xEBFA, 0x81AA,
	0xEBFB, 0x81CC,
	0xEBFC, 0x6726,
	0xEBFD, 0x81CA,
	0xEBFE, 0x81BB,
	0xEC4D, 0x9731,
	0xEC6E, 0x975A,
	0xEC71, 0x975F,
	0xEC80, 0x9772,
	0xEC81, 0x9775,
	0xEC94, 0x978C,
	0xEC98, 0x9793,
	0xECA1, 0x81C1,
	0xECA2, 0x81A6,
	0xECA3, 0x6B24,
	0xECA4, 0x6B37,
	0xECA5, 0x6B39,
	0xECA6, 0x6B43,
	0xECA7, 0x6B46,
	0xECA8, 0x6B59,
	0xECAC, 0x98D5,
	0xECAF, 0x6BB3,
	0xECB0, 0x5F40,
	0xECB1, 0x6BC2,
	0xECB2, 0x89F3,
	0xECB3, 0x6590,
	0xECB4, 0x9F51,
	0xECB5, 0x6593,
	0xECB6, 0x65BC,
	0xECB7, 0x65C6,
	0xECB8, 0x65C4,
	0xECB9, 0x65C3,
	0xECBA, 0x65CC,
	0xECBB, 0x65CE,
	0xECBC, 0x65D2,
	0xECBD, 0x65D6,
	0xECBE, 0x7080,
	0xECBF, 0x709C,
	0xECC0, 0x7096,
	0xECC1, 0x709D,
	0xECC2, 0x70BB,
	0xECC3, 0x70C0,
	0xECC4, 0x70B7,
	0xECC5, 0x70AB,
	0xECC6, 0x70B1,
	0xECC7, 0x70E8,
	0xECC8, 0x70CA,
	0xECC9, 0x7110,
	0xECCA, 0x7113,
	0xECCB, 0x7116,
	0xECCC, 0x712F,
	0xECCD, 0x7131,
	0xECCE, 0x7173,
	0xECCF, 0x715C,
	0xECD0, 0x7168,
	0xECD1, 0x7145,
	0xECD2, 0x7172,
	0xECD3, 0x714A,
	0xECD4, 0x7178,
	0xECD5, 0x717A,
	0xECD6, 0x7198,
	0xECD7, 0x71B3,
	0xECD8, 0x71B5,
	0xECD9, 0x71A8,
	0xECDA, 0x71A0,
	0xECDB, 0x71E0,
	0xECDC, 0x71D4,
	0xECDD, 0x71E7,
	0xECDE, 0x71F9,
	0xECDF, 0x721D,
	0xECE0, 0x7228,
	0xECE1, 0x706C,
	0xECE2, 0x7118,
	0xECE3, 0x7166,
	0xECE4, 0x71B9,
	0xECE5, 0x623E,
	0xECE6, 0x623D,
	0xECE7, 0x6243,
	0xECEA, 0x793B,
	0xECEB, 0x7940,
	0xECEC, 0x7946,
	0xECED, 0x7949,
	0xECF0, 0x7953,
	0xECF1, 0x795A,
	0xECF2, 0x7962,
	0xECF3, 0x7957,
	0xECF4, 0x7960,
	0xECF5, 0x796F,
	0xECF6, 0x7967,
	0xECF7, 0x797A,
	0xECF8, 0x7985,
	0xECF9, 0x798A,
	0xECFA, 0x799A,
	0xECFB, 0x79A7,
	0xECFC, 0x79B3,
	0xECFD, 0x5FD1,
	0xECFE, 0x5FD0,
	0xED4B, 0x97AC,
	0xED4C, 0x97AE,
	0xED4F, 0x97B3,
	0xED82, 0x97E8,
	0xED88, 0x97F4,
	0xEDA1, 0x603C,
	0xEDA2, 0x605D,
	0xEDA3, 0x605A,
	0xEDA4, 0x6067,
	0xEDA5, 0x6041,
	0xEDA6, 0x6059,
	0xEDA7, 0x6063,
	0xEDA8, 0x60AB,
	0xEDA9, 0x6106,
	0xEDAA, 0x610D,
	0xEDAB, 0x615D,
	0xEDAC, 0x61A9,
	0xEDAD, 0x619D,
	0xEDAE, 0x61CB,
	0xEDAF, 0x61D1,
	0xEDB0, 0x6206,
	0xEDB1, 0x8080,
	0xEDB2, 0x807F,
	0xEDB3, 0x6C93,
	0xEDB4, 0x6CF6,
	0xEDB5, 0x6DFC,
	0xEDB6, 0x77F6,
	0xEDB7, 0x77F8,
	0xEDB8, 0x7800,
	0xEDB9, 0x7809,
	0xEDBC, 0x7811,
	0xEDBD, 0x65AB,
	0xEDBE, 0x782D,
	0xEDC4, 0x781F,
	0xEDC5, 0x783C,
	0xEDC6, 0x7825,
	0xEDC7, 0x782C,
	0xEDC8, 0x7823,
	0xEDC9, 0x7829,
	0xEDCA, 0x784E,
	0xEDCB, 0x786D,
	0xEDCE, 0x7826,
	0xEDCF, 0x7850,
	0xEDD0, 0x7847,
	0xEDD1, 0x784C,
	0xEDD2, 0x786A,
	0xEDD3, 0x789B,
	0xEDD4, 0x7893,
	0xEDD5, 0x789A,
	0xEDD6, 0x7887,
	0xEDD7, 0x789C,
	0xEDD8, 0x78A1,
	0xEDD9, 0x78A3,
	0xEDDA, 0x78B2,
	0xEDDB, 0x78B9,
	0xEDDC, 0x78A5,
	0xEDDD, 0x78D4,
	0xEDDE, 0x78D9,
	0xEDDF, 0x78C9,
	0xEDE0, 0x78EC,
	0xEDE1, 0x78F2,
	0xEDE2, 0x7905,
	0xEDE3, 0x78F4,
	0xEDE4, 0x7913,
	0xEDE5, 0x7924,
	0xEDE6, 0x791E,
	0xEDE7, 0x7934,
	0xEDE8, 0x9F9B,
	0xEDE9, 0x9EF9,
	0xEDEC, 0x76F1,
	0xEDED, 0x7704,
	0xEDEE, 0x770D,
	0xEDEF, 0x76F9,
	0xEDF2, 0x771A,
	0xEDF3, 0x7722,
	0xEDF4, 0x7719,
	0xEDF5, 0x772D,
	0xEDF6, 0x7726,
	0xEDF7, 0x7735,
	0xEDF8, 0x7738,
	0xEDFB, 0x7747,
	0xEDFC, 0x7743,
	0xEDFD, 0x775A,
	0xEDFE, 0x7768,
	0xEEA1, 0x7762,
	0xEEA2, 0x7765,
	0xEEA3, 0x777F,
	0xEEA4, 0x778D,
	0xEEA5, 0x777D,
	0xEEA6, 0x7780,
	0xEEA7, 0x778C,
	0xEEA8, 0x7791,
	0xEEAB, 0x77B0,
	0xEEAC, 0x77B5,
	0xEEAD, 0x77BD,
	0xEEAE, 0x753A,
	0xEEAF, 0x7540,
	0xEEB0, 0x754E,
	0xEEB1, 0x754B,
	0xEEB2, 0x7548,
	0xEEB3, 0x755B,
	0xEEB4, 0x7572,
	0xEEB5, 0x7579,
	0xEEB6, 0x7583,
	0xEEB7, 0x7F58,
	0xEEB8, 0x7F61,
	0xEEB9, 0x7F5F,
	0xEEBA, 0x8A48,
	0xEEBB, 0x7F68,
	0xEEBC, 0x7F74,
	0xEEBD, 0x7F71,
	0xEEBE, 0x7F79,
	0xEEBF, 0x7F81,
	0xEEC0, 0x7F7E,
	0xEEC1, 0x76CD,
	0xEEC2, 0x76E5,
	0xEEC3, 0x8832,
	0xEEC7, 0x948B,
	0xEEC8, 0x948A,
	0xEECD, 0x9494,
	0xEECE, 0x9497,
	0xEECF, 0x9495,
	0xEED5, 0x94AB,
	0xEED6, 0x94AA,
	0xEED7, 0x94AD,
	0xEED8, 0x94AC,
	0xEEDB, 0x94B2,
	0xEEDC, 0x94B4,
	0xEEE4, 0x94BF,
	0xEEE5, 0x94C4,
	0xEEF3, 0x94D9,
	0xEEF4, 0x94D8,
	0xEEF5, 0x94DB,
	0xEEF9, 0x94E2,
	0xEEFE, 0x94EA,
	0xEF46, 0x988B,
	0xEF47, 0x988E,
	0xEF48, 0x9892,
	0xEF49, 0x9895,
	0xEF4A, 0x9899,
	0xEF4B, 0x98A3,
	0xEF74, 0x98D4,
	0xEFA1, 0x94E9,
	0xEFA2, 0x94EB,
	0xEFA8, 0x94F7,
	0xEFA9, 0x94F9,
	0xEFAC, 0x94FF,
	0xEFAD, 0x9503,
	0xEFAE, 0x9502,
	0xEFBB, 0x9518,
	0xEFBC, 0x951B,
	0xEFC0, 0x9522,
	0xEFC3, 0x9529,
	0xEFC4, 0x952C,
	0xEFC7, 0x9534,
	0xEFCB, 0x953C,
	0xEFCE, 0x9542,
	0xEFCF, 0x9535,
	0xEFD3, 0x9549,
	0xEFD4, 0x954C,
	0xEFDE, 0x955B,
	0xEFE1, 0x955D,
	0xEFED, 0x956F,
	0xEFF1, 0x953A,
	0xEFF2, 0x77E7,
	0xEFF3, 0x77EC,
	0xEFF4, 0x96C9,
	0xEFF5, 0x79D5,
	0xEFF6, 0x79ED,
	0xEFF7, 0x79E3,
	0xEFF8, 0x79EB,
	0xEFF9, 0x7A06,
	0xEFFA, 0x5D47,
	0xEFFB, 0x7A03,
	0xEFFC, 0x7A02,
	0xEFFD, 0x7A1E,
	0xEFFE, 0x7A14,
	0xF097, 0x9964,
	0xF098, 0x9966,
	0xF099, 0x9973,
	0xF09C, 0x997B,
	0xF09D, 0x997E,
	0xF0A0, 0x9989,
	0xF0A1, 0x7A39,
	0xF0A2, 0x7A37,
	0xF0A3, 0x7A51,
	0xF0A4, 0x9ECF,
	0xF0A5, 0x99A5,
	0xF0A6, 0x7A70,
	0xF0A7, 0x7688,
	0xF0A8, 0x768E,
	0xF0A9, 0x7693,
	0xF0AA, 0x7699,
	0xF0AB, 0x76A4,
	0xF0AC, 0x74DE,
	0xF0AD, 0x74E0,
	0xF0AE, 0x752C,
	0xF0AF, 0x9E20,
	0xF0B0, 0x9E22,
	0xF0B6, 0x9E32,
	0xF0B7, 0x9E31,
	0xF0B8, 0x9E36,
	0xF0B9, 0x9E38,
	0xF0BA, 0x9E37,
	0xF0BD, 0x9E3E,
	0xF0C0, 0x9E44,
	0xF0C7, 0x9E4E,
	0xF0C8, 0x9E51,
	0xF0C9, 0x9E55,
	0xF0CA, 0x9E57,
	0xF0CE, 0x9E5E,
	0xF0CF, 0x9E63,
	0xF0D7, 0x9E71,
	0xF0D8, 0x9E6D,
	0xF0D9, 0x9E73,
	0xF0DA, 0x7592,
	0xF0DB, 0x7594,
	0xF0DC, 0x7596,
	0xF0DD, 0x75A0,
	0xF0DE, 0x759D,
	0xF0DF, 0x75AC,
	0xF0E0, 0x75A3,
	0xF0E3, 0x75B8,
	0xF0E4, 0x75C4,
	0xF0E5, 0x75B1,
	0xF0E6, 0x75B0,
	0xF0E7, 0x75C3,
	0xF0E8, 0x75C2,
	0xF0E9, 0x75D6,
	0xF0EA, 0x75CD,
	0xF0EB, 0x75E3,
	0xF0EC, 0x75E8,
	0xF0ED, 0x75E6,
	0xF0EE, 0x75E4,
	0xF0EF, 0x75EB,
	0xF0F0, 0x75E7,
	0xF0F1, 0x7603,
	0xF0F2, 0x75F1,
	0xF0F3, 0x75FC,
	0xF0F4, 0x75FF,
	0xF0F5, 0x7610,
	0xF0F6, 0x7600,
	0xF0F7, 0x7605,
	0xF0F8, 0x760C,
	0xF0F9, 0x7617,
	0xF0FA, 0x760A,
	0xF0FB, 0x7625,
	0xF0FC, 0x7618,
	0xF0FD, 0x7615,
	0xF0FE, 0x7619,
	0xF140, 0x998C,
	0xF141, 0x998E,
	0xF1A1, 0x761B,
	0xF1A2, 0x763C,
	0xF1A3, 0x7622,
	0xF1A4, 0x7620,
	0xF1A5, 0x7640,
	0xF1A6, 0x762D,
	0xF1A7, 0x7630,
	0xF1A8, 0x763F,
	0xF1A9, 0x7635,
	0xF1AA, 0x7643,
	0xF1AB, 0x763E,
	0xF1AC, 0x7633,
	0xF1AD, 0x764D,
	0xF1AE, 0x765E,
	0xF1AF, 0x7654,
	0xF1B0, 0x765C,
	0xF1B1, 0x7656,
	0xF1B2, 0x766B,
	0xF1B3, 0x766F,
	0xF1B4, 0x7FCA,
	0xF1B5, 0x7AE6,
	0xF1B8, 0x7A80,
	0xF1B9, 0x7A86,
	0xF1BA, 0x7A88,
	0xF1BB, 0x7A95,
	0xF1BC, 0x7AA6,
	0xF1BD, 0x7AA0,
	0xF1BE, 0x7AAC,
	0xF1BF, 0x7AA8,
	0xF1C0, 0x7AAD,
	0xF1C1, 0x7AB3,
	0xF1C2, 0x8864,
	0xF1C3, 0x8869,
	0xF1C4, 0x8872,
	0xF1C5, 0x887D,
	0xF1C6, 0x887F,
	0xF1C7, 0x8882,
	0xF1C8, 0x88A2,
	0xF1C9, 0x88C6,
	0xF1CA, 0x88B7,
	0xF1CB, 0x88BC,
	0xF1CC, 0x88C9,
	0xF1CD, 0x88E2,
	0xF1CE, 0x88CE,
	0xF1CF, 0x88E3,
	0xF1D0, 0x88E5,
	0xF1D1, 0x88F1,
	0xF1D2, 0x891A,
	0xF1D3, 0x88FC,
	0xF1D4, 0x88E8,
	0xF1D5, 0x88FE,
	0xF1D6, 0x88F0,
	0xF1D7, 0x8921,
	0xF1D8, 0x8919,
	0xF1D9, 0x8913,
	0xF1DA, 0x891B,
	0xF1DB, 0x890A,
	0xF1DC, 0x8934,
	0xF1DD, 0x892B,
	0xF1DE, 0x8936,
	0xF1DF, 0x8941,
	0xF1E0, 0x8966,
	0xF1E1, 0x897B,
	0xF1E2, 0x758B,
	0xF1E3, 0x80E5,
	0xF1E4, 0x76B2,
	0xF1E5, 0x76B4,
	0xF1E6, 0x77DC,
	0xF1E7, 0x8012,
	0xF1E8, 0x8014,
	0xF1E9, 0x8016,
	0xF1EA, 0x801C,
	0xF1EB, 0x8020,
	0xF1EC, 0x8022,
	0xF1F0, 0x8029,
	0xF1F1, 0x8028,
	0xF1F2, 0x8031,
	0xF1F3, 0x800B,
	0xF1F4, 0x8035,
	0xF1F5, 0x8043,
	0xF1F6, 0x8046,
	0xF1F7, 0x804D,
	0xF1F8, 0x8052,
	0xF1F9, 0x8069,
	0xF1FA, 0x8071,
	0xF1FB, 0x8983,
	0xF1FC, 0x9878,
	0xF1FD, 0x9880,
	0xF1FE, 0x9883,
	0xF2A1, 0x9889,
	0xF2A4, 0x988F,
	0xF2A5, 0x9894,
	0xF2AE, 0x864D,
	0xF2AF, 0x8654,
	0xF2B0, 0x866C,
	0xF2B1, 0x866E,
	0xF2B2, 0x867F,
	0xF2B3, 0x867A,
	0xF2B4, 0x867C,
	0xF2B5, 0x867B,
	0xF2B6, 0x86A8,
	0xF2B7, 0x868D,
	0xF2B8, 0x868B,
	0xF2B9, 0x86AC,
	0xF2BA, 0x869D,
	0xF2BB, 0x86A7,
	0xF2BC, 0x86A3,
	0xF2BD, 0x86AA,
	0xF2BE, 0x8693,
	0xF2BF, 0x86A9,
	0xF2C0, 0x86B6,
	0xF2C1, 0x86C4,
	0xF2C2, 0x86B5,
	0xF2C3, 0x86CE,
	0xF2C4, 0x86B0,
	0xF2C5, 0x86BA,
	0xF2C6, 0x86B1,
	0xF2C7, 0x86AF,
	0xF2C8, 0x86C9,
	0xF2C9, 0x86CF,
	0xF2CA, 0x86B4,
	0xF2CB, 0x86E9,
	0xF2CE, 0x86ED,
	0xF2CF, 0x86F3,
	0xF2D0, 0x86D0,
	0xF2D1, 0x8713,
	0xF2D2, 0x86DE,
	0xF2D3, 0x86F4,
	0xF2D4, 0x86DF,
	0xF2D5, 0x86D8,
	0xF2D6, 0x86D1,
	0xF2D7, 0x8703,
	0xF2D8, 0x8707,
	0xF2D9, 0x86F8,
	0xF2DA, 0x8708,
	0xF2DB, 0x870A,
	0xF2DC, 0x870D,
	0xF2DD, 0x8709,
	0xF2DE, 0x8723,
	0xF2DF, 0x873B,
	0xF2E0, 0x871E,
	0xF2E1, 0x8725,
	0xF2E2, 0x872E,
	0xF2E3, 0x871A,
	0xF2E4, 0x873E,
	0xF2E5, 0x8748,
	0xF2E6, 0x8734,
	0xF2E7, 0x8731,
	0xF2E8, 0x8729,
	0xF2E9, 0x8737,
	0xF2EA, 0x873F,
	0xF2EB, 0x8782,
	0xF2EC, 0x8722,
	0xF2EF, 0x877B,
	0xF2F0, 0x8760,
	0xF2F1, 0x8770,
	0xF2F2, 0x874C,
	0xF2F3, 0x876E,
	0xF2F4, 0x878B,
	0xF2F5, 0x8753,
	0xF2F6, 0x8763,
	0xF2F7, 0x877C,
	0xF2F8, 0x8764,
	0xF2F9, 0x8759,
	0xF2FA, 0x8765,
	0xF2FB, 0x8793,
	0xF2FC, 0x87AF,
	0xF2FD, 0x87A8,
	0xF2FE, 0x87D2,
	0xF352, 0x9A72,
	0xF353, 0x9A83,
	0xF354, 0x9A89,
	0xF359, 0x9A99,
	0xF35A, 0x9AA6,
	0xF366, 0x9AB9,
	0xF367, 0x9ABB,
	0xF376, 0x9AD2,
	0xF382, 0x9AE0,
	0xF38B, 0x9AEC,
	0xF38C, 0x9AEE,
	0xF396, 0x9AFA,
	0xF3A1, 0x87C6,
	0xF3A2, 0x8788,
	0xF3A3, 0x8785,
	0xF3A4, 0x87AD,
	0xF3A5, 0x8797,
	0xF3A6, 0x8783,
	0xF3A7, 0x87AB,
	0xF3A8, 0x87E5,
	0xF3A9, 0x87AC,
	0xF3AA, 0x87B5,
	0xF3AB, 0x87B3,
	0xF3AC, 0x87CB,
	0xF3AD, 0x87D3,
	0xF3AE, 0x87BD,
	0xF3AF, 0x87D1,
	0xF3B0, 0x87C0,
	0xF3B1, 0x87CA,
	0xF3B2, 0x87DB,
	0xF3B3, 0x87EA,
	0xF3B4, 0x87E0,
	0xF3B5, 0x87EE,
	0xF3B6, 0x8816,
	0xF3B7, 0x8813,
	0xF3B8, 0x87FE,
	0xF3B9, 0x880A,
	0xF3BA, 0x881B,
	0xF3BB, 0x8821,
	0xF3BC, 0x8839,
	0xF3BD, 0x883C,
	0xF3BE, 0x7F36,
	0xF3BF, 0x7F42,
	0xF3C2, 0x8210,
	0xF3C3, 0x7AFA,
	0xF3C4, 0x7AFD,
	0xF3C5, 0x7B08,
	0xF3C8, 0x7B15,
	0xF3C9, 0x7B0A,
	0xF3CA, 0x7B2B,
	0xF3CB, 0x7B0F,
	0xF3CC, 0x7B47,
	0xF3CD, 0x7B38,
	0xF3CE, 0x7B2A,
	0xF3CF, 0x7B19,
	0xF3D0, 0x7B2E,
	0xF3D1, 0x7B31,
	0xF3D2, 0x7B20,
	0xF3D3, 0x7B25,
	0xF3D4, 0x7B24,
	0xF3D5, 0x7B33,
	0xF3D6, 0x7B3E,
	0xF3D7, 0x7B1E,
	0xF3D8, 0x7B58,
	0xF3D9, 0x7B5A,
	0xF3DA, 0x7B45,
	0xF3DB, 0x7B75,
	0xF3DC, 0x7B4C,
	0xF3DD, 0x7B5D,
	0xF3DE, 0x7B60,
	0xF3DF, 0x7B6E,
	0xF3E0, 0x7B7B,
	0xF3E1, 0x7B62,
	0xF3E2, 0x7B72,
	0xF3E3, 0x7B71,
	0xF3E4, 0x7B90,
	0xF3E7, 0x7BB8,
	0xF3E8, 0x7BAC,
	0xF3E9, 0x7B9D,
	0xF3EA, 0x7BA8,
	0xF3EB, 0x7B85,
	0xF3EC, 0x7BAA,
	0xF3ED, 0x7B9C,
	0xF3EE, 0x7BA2,
	0xF3EF, 0x7BAB,
	0xF3F0, 0x7BB4,
	0xF3F1, 0x7BD1,
	0xF3F2, 0x7BC1,
	0xF3F3, 0x7BCC,
	0xF3F4, 0x7BDD,
	0xF3F5, 0x7BDA,
	0xF3F8, 0x7BEA,
	0xF3F9, 0x7C0C,
	0xF3FA, 0x7BFE,
	0xF3FB, 0x7BFC,
	0xF3FC, 0x7C0F,
	0xF3FD, 0x7C16,
	0xF3FE, 0x7C0B,
	0xF440, 0x9B07,
	0xF471, 0x9B46,
	0xF475, 0x9B4E,
	0xF476, 0x9B50,
	0xF4A1, 0x7C1F,
	0xF4A2, 0x7C2A,
	0xF4A3, 0x7C26,
	0xF4A4, 0x7C38,
	0xF4A5, 0x7C41,
	0xF4A6, 0x7C40,
	0xF4A7, 0x81FE,
	0xF4AA, 0x8204,
	0xF4AB, 0x81EC,
	0xF4AC, 0x8844,
	0xF4B0, 0x822D,
	0xF4B1, 0x822F,
	0xF4B2, 0x8228,
	0xF4B3, 0x822B,
	0xF4B4, 0x8238,
	0xF4B5, 0x823B,
	0xF4B8, 0x823E,
	0xF4B9, 0x8244,
	0xF4BA, 0x8249,
	0xF4BB, 0x824B,
	0xF4BC, 0x824F,
	0xF4BD, 0x825A,
	0xF4BE, 0x825F,
	0xF4BF, 0x8268,
	0xF4C0, 0x887E,
	0xF4C1, 0x8885,
	0xF4C2, 0x8888,
	0xF4C3, 0x88D8,
	0xF4C4, 0x88DF,
	0xF4C5, 0x895E,
	0xF4C6, 0x7F9D,
	0xF4C7, 0x7F9F,
	0xF4C8, 0x7FA7,
	0xF4CB, 0x7FB2,
	0xF4CC, 0x7C7C,
	0xF4CD, 0x6549,
	0xF4CE, 0x7C91,
	0xF4CF, 0x7C9D,
	0xF4D0, 0x7C9C,
	0xF4D1, 0x7C9E,
	0xF4D2, 0x7CA2,
	0xF4D3, 0x7CB2,
	0xF4D6, 0x7CC1,
	0xF4D7, 0x7CC7,
	0xF4DA, 0x7CC8,
	0xF4DB, 0x7CC5,
	0xF4DC, 0x7CD7,
	0xF4DD, 0x7CE8,
	0xF4DE, 0x826E,
	0xF4DF, 0x66A8,
	0xF4E0, 0x7FBF,
	0xF4E1, 0x7FCE,
	0xF4E2, 0x7FD5,
	0xF4E3, 0x7FE5,
	0xF4E4, 0x7FE1,
	0xF4E5, 0x7FE6,
	0xF4E6, 0x7FE9,
	0xF4E7, 0x7FEE,
	0xF4E8, 0x7FF3,
	0xF4E9, 0x7CF8,
	0xF4EA, 0x7D77,
	0xF4EB, 0x7DA6,
	0xF4EC, 0x7DAE,
	0xF4ED, 0x7E47,
	0xF4EE, 0x7E9B,
	0xF4EF, 0x9EB8,
	0xF4F0, 0x9EB4,
	0xF4F1, 0x8D73,
	0xF4F2, 0x8D84,
	0xF4F3, 0x8D94,
	0xF4F4, 0x8D91,
	0xF4F5, 0x8DB1,
	0xF4F6, 0x8D67,
	0xF4F7, 0x8D6D,
	0xF4F8, 0x8C47,
	0xF4F9, 0x8C49,
	0xF4FA, 0x914A,
	0xF4FB, 0x9150,
	0xF4FE, 0x9164,
	0xF5A1, 0x9162,
	0xF5A2, 0x9161,
	0xF5A3, 0x9170,
	0xF5A4, 0x9169,
	0xF5A5, 0x916F,
	0xF5A8, 0x9172,
	0xF5A9, 0x9174,
	0xF5AA, 0x9179,
	0xF5AB, 0x918C,
	0xF5AC, 0x9185,
	0xF5AD, 0x9190,
	0xF5AE, 0x918D,
	0xF5AF, 0x9191,
	0xF5B2, 0x91AA,
	0xF5B6, 0x91B5,
	0xF5B7, 0x91B4,
	0xF5B8, 0x91BA,
	0xF5B9, 0x8C55,
	0xF5BA, 0x9E7E,
	0xF5BB, 0x8DB8,
	0xF5BC, 0x8DEB,
	0xF5BD, 0x8E05,
	0xF5BE, 0x8E59,
	0xF5BF, 0x8E69,
	0xF5C0, 0x8DB5,
	0xF5C1, 0x8DBF,
	0xF5C2, 0x8DBC,
	0xF5C3, 0x8DBA,
	0xF5C4, 0x8DC4,
	0xF5C7, 0x8DDA,
	0xF5C8, 0x8DDE,
	0xF5CB, 0x8DDB,
	0xF5CC, 0x8DC6,
	0xF5CD, 0x8DEC,
	0xF5D0, 0x8DE3,
	0xF5D1, 0x8DF9,
	0xF5D2, 0x8DFB,
	0xF5D3, 0x8DE4,
	0xF5D4, 0x8E09,
	0xF5D5, 0x8DFD,
	0xF5D6, 0x8E14,
	0xF5D7, 0x8E1D,
	0xF5D8, 0x8E1F,
	0xF5D9, 0x8E2C,
	0xF5DA, 0x8E2E,
	0xF5DB, 0x8E23,
	0xF5DC, 0x8E2F,
	0xF5DD, 0x8E3A,
	0xF5DE, 0x8E40,
	0xF5DF, 0x8E39,
	0xF5E0, 0x8E35,
	0xF5E1, 0x8E3D,
	0xF5E2, 0x8E31,
	0xF5E3, 0x8E49,
	0xF5E8, 0x8E4A,
	0xF5E9, 0x8E70,
	0xF5EA, 0x8E76,
	0xF5EB, 0x8E7C,
	0xF5EC, 0x8E6F,
	0xF5ED, 0x8E74,
	0xF5EE, 0x8E85,
	0xF5EF, 0x8E8F,
	0xF5F0, 0x8E94,
	0xF5F1, 0x8E90,
	0xF5F2, 0x8E9C,
	0xF5F3, 0x8E9E,
	0xF5F4, 0x8C78,
	0xF5F5, 0x8C82,
	0xF5F6, 0x8C8A,
	0xF5F7, 0x8C85,
	0xF5F8, 0x8C98,
	0xF5F9, 0x8C94,
	0xF5FA, 0x659B,
	0xF5FB, 0x89D6,
	0xF5FC, 0x89DE,
	0xF5FD, 0x89DA,
	0xF5FE, 0x89DC,
	0xF6A1, 0x89E5,
	0xF6A2, 0x89EB,
	0xF6A3, 0x89EF,
	0xF6A4, 0x8A3E,
	0xF6A5, 0x8B26,
	0xF6A6, 0x9753,
	0xF6A7, 0x96E9,
	0xF6A8, 0x96F3,
	0xF6A9, 0x96EF,
	0xF6AA, 0x9706,
	0xF6AB, 0x9701,
	0xF6AC, 0x9708,
	0xF6AD, 0x970F,
	0xF6AE, 0x970E,
	0xF6AF, 0x972A,
	0xF6B0, 0x972D,
	0xF6B1, 0x9730,
	0xF6B2, 0x973E,
	0xF6B3, 0x9F80,
	0xF6B4, 0x9F83,
	0xF6BB, 0x9F8C,
	0xF6BC, 0x9EFE,
	0xF6BD, 0x9F0B,
	0xF6BE, 0x9F0D,
	0xF6BF, 0x96B9,
	0xF6C2, 0x96CE,
	0xF6C3, 0x96D2,
	0xF6C4, 0x77BF,
	0xF6C5, 0x96E0,
	0xF6C6, 0x928E,
	0xF6C7, 0x92AE,
	0xF6C8, 0x92C8,
	0xF6C9, 0x933E,
	0xF6CA, 0x936A,
	0xF6CB, 0x93CA,
	0xF6CC, 0x938F,
	0xF6CD, 0x943E,
	0xF6CE, 0x946B,
	0xF6CF, 0x9C7F,
	0xF6D0, 0x9C82,
	0xF6D5, 0x7A23,
	0xF6D6, 0x9C8B,
	0xF6D7, 0x9C8E,
	0xF6EA, 0x9CAB,
	0xF780, 0x9C7B,
	0xF783, 0x9C80,
	0xF788, 0x9C8C,
	0xF789, 0x9C8F,
	0xF78A, 0x9C93,
	0xF78F, 0x9C9D,
	0xF790, 0x9CAA,
	0xF791, 0x9CAC,
	0xF792, 0x9CAF,
	0xF793, 0x9CB9,
	0xF7AE, 0x9CDF,
	0xF7AF, 0x9CE2,
	0xF7B0, 0x977C,
	0xF7B1, 0x9785,
	0xF7B4, 0x9794,
	0xF7B5, 0x97AF,
	0xF7B6, 0x97AB,
	0xF7B7, 0x97A3,
	0xF7B8, 0x97B2,
	0xF7B9, 0x97B4,
	0xF7BA, 0x9AB1,
	0xF7BB, 0x9AB0,
	0xF7BC, 0x9AB7,
	0xF7BD, 0x9E58,
	0xF7BE, 0x9AB6,
	0xF7BF, 0x9ABA,
	0xF7C0, 0x9ABC,
	0xF7C1, 0x9AC1,
	0xF7C2, 0x9AC0,
	0xF7C3, 0x9AC5,
	0xF7C4, 0x9AC2,
	0xF7C7, 0x9AD1,
	0xF7C8, 0x9B45,
	0xF7C9, 0x9B43,
	0xF7CA, 0x9B47,
	0xF7CB, 0x9B49,
	0xF7CC, 0x9B48,
	0xF7CD, 0x9B4D,
	0xF7CE, 0x9B51,
	0xF7CF, 0x98E8,
	0xF7D0, 0x990D,
	0xF7D1, 0x992E,
	0xF7D2, 0x9955,
	0xF7D3, 0x9954,
	0xF7D4, 0x9ADF,
	0xF7D5, 0x9AE1,
	0xF7D6, 0x9AE6,
	0xF7D7, 0x9AEF,
	0xF7D8, 0x9AEB,
	0xF7D9, 0x9AFB,
	0xF7DA, 0x9AED,
	0xF7DB, 0x9AF9,
	0xF7DC, 0x9B08,
	0xF7DD, 0x9B0F,
	0xF7DE, 0x9B13,
	0xF7DF, 0x9B1F,
	0xF7E0, 0x9B23,
	0xF7E3, 0x7E3B,
	0xF7E4, 0x9E82,
	0xF7E7, 0x9E8B,
	0xF7E8, 0x9E92,
	0xF7E9, 0x93D6,
	0xF7EA, 0x9E9D,
	0xF7EB, 0x9E9F,
	0xF7EF, 0x9EE0,
	0xF7F0, 0x9EDF,
	0xF7F1, 0x9EE2,
	0xF7F2, 0x9EE9,
	0xF7F3, 0x9EE7,
	0xF7F4, 0x9EE5,
	0xF7F5, 0x9EEA,
	0xF7F6, 0x9EEF,
	0xF7F7, 0x9F22,
	0xF7F8, 0x9F2C,
	0xF7F9, 0x9F2F,
	0xF7FA, 0x9F39,
	0xF7FB, 0x9F37,
	0xF7FE, 0x9F44,
	0xFB5C, 0x9E24,
	0xFB5D, 0x9E27,
	0xFB5E, 0x9E2E,
	0xFB5F, 0x9E30,
	0xFB60, 0x9E34,
	0xFB63, 0x9E40,
	0xFB64, 0x9E4D,
	0xFB65, 0x9E50,
	0xFB69, 0x9E56,
	0xFB6A, 0x9E59,
	0xFB6B, 0x9E5D,
	0xFB70, 0x9E65,
	0xFB73, 0x9E72,
	0xFB7E, 0x9E80,
	0xFB80, 0x9E81,
	0xFB96, 0x9E9E,
	0xFC4E, 0x9EBC,
	0xFC5B, 0x9ED0,
	0xFC63, 0x9EDE,
	0xFC64, 0x9EE1,
	0xFC67, 0x9EE6,
	0xFC68, 0x9EE8,
	0xFC76, 0x9EFA,
	0xFC77, 0x9EFD,
	0xFC85, 0x9F0C,
	0xFC86, 0x9F0F,
	0xFC8C, 0x9F18,
	0xFC93, 0x9F21,
	0xFD45, 0x9F38,
	0xFD46, 0x9F3A,
	0xFD47, 0x9F3C,
	0xFD9C, 0xF92C,
	0xFD9D, 0xF979,
	0xFD9E, 0xF995,
	0xFD9F, 0xF9E7,
	0xFDA0, 0xF9F1,
	0xFE44, 0xFA11,
	0xFE47, 0xFA18
};

constexpr unsigned short gbk2_utf16_3_host[] =
{
	0x8141, 0x8143, 0x4E04,
	0x8147, 0x8149, 0x4E1F,
	0x814D, 0x814E, 0x4E2E,
	0x8154, 0x8156, 0x4E40,
	0x815D, 0x815E, 0x4E5A,
	0x815F, 0x8162, 0x4E62,
	0x8163, 0x8164, 0x4E67,
	0x8165, 0x816A, 0x4E6A,
	0x816C, 0x8175, 0x4E74,
	0x8176, 0x817C, 0x4E7F,
	0x8181, 0x8182, 0x4E96,
	0x8184, 0x8186, 0x4E9C,
	0x8189, 0x818B, 0x4EAF,
	0x818D, 0x8190, 0x4EB6,
	0x8191, 0x8193, 0x4EBC,
	0x8196, 0x8197, 0x4ECF,
	0x8199, 0x819B, 0x4EDA,
	0x819E, 0x819F, 0x4EE6,
	0x81A1, 0x81A3, 0x4EED,
	0x81A6, 0x81A8, 0x4EF8,
	0x81AC, 0x81B2, 0x4F02,
	0x81B3, 0x81B4, 0x4F0B,
	0x81B5, 0x81B9, 0x4F12,
	0x81BA, 0x81BB, 0x4F1C,
	0x81BE, 0x81BF, 0x4F28,
	0x81C0, 0x81C2, 0x4F2C,
	0x81C9, 0x81CD, 0x4F3E,
	0x81CE, 0x81CF, 0x4F44,
	0x81D0, 0x81D5, 0x4F47,
	0x81D9, 0x81DA, 0x4F61,
	0x81DD, 0x81DE, 0x4F6A,
	0x81DF, 0x81E0, 0x4F6D,
	0x81E1, 0x81E2, 0x4F71,
	0x81E4, 0x81E7, 0x4F77,
	0x81E9, 0x81EB, 0x4F80,
	0x81EC, 0x81EE, 0x4F85,
	0x81F3, 0x81F4, 0x4F92,
	0x81F5, 0x81F6, 0x4F95,
	0x81F7, 0x81F9, 0x4F98,
	0x81FB, 0x81FC, 0x4F9E,
	0x81FD, 0x81FE, 0x4FA1,
	0x8243, 0x8247, 0x4FB0,
	0x8248, 0x8250, 0x4FB6,
	0x8251, 0x8253, 0x4FC0,
	0x8254, 0x8257, 0x4FC6,
	0x8258, 0x825A, 0x4FCB,
	0x825B, 0x825F, 0x4FD2,
	0x8264, 0x8265, 0x4FE4,
	0x8267, 0x8268, 0x4FEB,
	0x826B, 0x826E, 0x4FF4,
	0x8270, 0x8272, 0x4FFB,
	0x8273, 0x827E, 0x4FFF,
	0x8282, 0x8283, 0x5010,
	0x8285, 0x8287, 0x5015,
	0x8289, 0x828A, 0x501D,
	0x828C, 0x828E, 0x5022,
	0x8291, 0x829B, 0x502F,
	0x829E, 0x82A1, 0x503F,
	0x82A2, 0x82A4, 0x5044,
	0x82A5, 0x82A7, 0x5049,
	0x82A9, 0x82AD, 0x5050,
	0x82AE, 0x82B1, 0x5056,
	0x82B3, 0x82BA, 0x505D,
	0x82BB, 0x82C0, 0x5066,
	0x82C1, 0x82C9, 0x506D,
	0x82CA, 0x82CC, 0x5078,
	0x82CD, 0x82CE, 0x507C,
	0x82CF, 0x82D2, 0x5081,
	0x82D3, 0x82D4, 0x5086,
	0x82D5, 0x82D8, 0x5089,
	0x82D9, 0x82ED, 0x508E,
	0x82F0, 0x82F1, 0x50AA,
	0x82F2, 0x82F6, 0x50AD,
	0x82F7, 0x82FD, 0x50B3,
	0x8340, 0x8351, 0x50BD,
	0x8352, 0x8357, 0x50D0,
	0x8358, 0x835A, 0x50D7,
	0x835B, 0x8365, 0x50DB,
	0x8366, 0x8369, 0x50E8,
	0x836A, 0x836D, 0x50EF,
	0x836F, 0x8373, 0x50F6,
	0x8374, 0x837D, 0x50FC,
	0x8380, 0x8381, 0x5109,
	0x8382, 0x8387, 0x510C,
	0x8388, 0x8395, 0x5113,
	0x8396, 0x83B2, 0x5122,
	0x83B7, 0x83B9, 0x514E,
	0x83BA, 0x83BB, 0x5152,
	0x83BC, 0x83BE, 0x5157,
	0x83C0, 0x83C4, 0x515D,
	0x83C5, 0x83C6, 0x5163,
	0x83C7, 0x83C8, 0x5166,
	0x83C9, 0x83CA, 0x5169,
	0x83CE, 0x83CF, 0x517E,
	0x83D0, 0x83D1, 0x5183,
	0x83D2, 0x83D3, 0x5186,
	0x83D4, 0x83D5, 0x518A,
	0x83D6, 0x83D9, 0x518E,
	0x83DA, 0x83DB, 0x5193,
	0x83DE, 0x83E0, 0x519D,
	0x83E3, 0x83E7, 0x51A6,
	0x83E8, 0x83E9, 0x51AD,
	0x83EB, 0x83ED, 0x51B8,
	0x83EE, 0x83EF, 0x51BE,
	0x83F0, 0x83F2, 0x51C1,
	0x83F6, 0x83F7, 0x51CD,
	0x83F9, 0x83FE, 0x51D2,
	0x8440, 0x8442, 0x51D8,
	0x8444, 0x8445, 0x51DE,
	0x8446, 0x8447, 0x51E2,
	0x8448, 0x844D, 0x51E5,
	0x8450, 0x8451, 0x51F1,
	0x8455, 0x8456, 0x5204,
	0x8458, 0x8459, 0x520B,
	0x845A, 0x845B, 0x520F,
	0x845C, 0x845E, 0x5213,
	0x8460, 0x8461, 0x521E,
	0x8462, 0x8464, 0x5221,
	0x8465, 0x8467, 0x5225,
	0x846B, 0x846C, 0x5231,
	0x846D, 0x846E, 0x5234,
	0x8471, 0x8476, 0x5244,
	0x8478, 0x8479, 0x524E,
	0x847A, 0x847B, 0x5252,
	0x847D, 0x847E, 0x5257,
	0x8480, 0x8482, 0x5259,
	0x8484, 0x8485, 0x525F,
	0x8486, 0x8488, 0x5262,
	0x848B, 0x848E, 0x526B,
	0x848F, 0x8490, 0x5270,
	0x8491, 0x849A, 0x5273,
	0x849D, 0x84A1, 0x5283,
	0x84A2, 0x84A8, 0x5289,
	0x84A9, 0x84AA, 0x5291,
	0x84AB, 0x84B1, 0x5294,
	0x84B3, 0x84B6, 0x52A4,
	0x84B7, 0x84B9, 0x52AE,
	0x84BA, 0x84C3, 0x52B4,
	0x84C4, 0x84C6, 0x52C0,
	0x84C7, 0x84C9, 0x52C4,
	0x84CC, 0x84CF, 0x52CC,
	0x84D1, 0x84D3, 0x52D3,
	0x84D5, 0x84DA, 0x52D9,
	0x84DB, 0x84DE, 0x52E0,
	0x84DF, 0x84E9, 0x52E5,
	0x84EA, 0x84F1, 0x52F1,
	0x84F2, 0x84F4, 0x52FB,
	0x84F5, 0x84F8, 0x5301,
	0x84FA, 0x84FD, 0x5309,
	0x8540, 0x8543, 0x5311,
	0x8545, 0x8546, 0x531B,
	0x8547, 0x8548, 0x531E,
	0x854A, 0x854B, 0x5324,
	0x854C, 0x854E, 0x5327,
	0x854F, 0x8551, 0x532B,
	0x8552, 0x855B, 0x532F,
	0x855C, 0x855D, 0x533C,
	0x8562, 0x8564, 0x534B,
	0x8567, 0x8568, 0x5358,
	0x856E, 0x856F, 0x536C,
	0x8573, 0x8576, 0x537B,
	0x8577, 0x8578, 0x5380,
	0x857A, 0x857B, 0x5387,
	0x857D, 0x857E, 0x538E,
	0x8580, 0x8584, 0x5390,
	0x8585, 0x8586, 0x5396,
	0x8588, 0x8589, 0x539B,
	0x858B, 0x858C, 0x53A0,
	0x858F, 0x8592, 0x53AA,
	0x8593, 0x8599, 0x53AF,
	0x859A, 0x859D, 0x53B7,
	0x859E, 0x85A0, 0x53BC,
	0x85A2, 0x85A6, 0x53C3,
	0x85A7, 0x85A9, 0x53CE,
	0x85AA, 0x85AB, 0x53D2,
	0x85AE, 0x85B0, 0x53DC,
	0x85B1, 0x85B2, 0x53E1,
	0x85B6, 0x85B8, 0x53FE,
	0x85BE, 0x85C0, 0x5418,
	0x85C3, 0x85C4, 0x5424,
	0x85C8, 0x85C9, 0x5436,
	0x85CD, 0x85CE, 0x5441,
	0x85CF, 0x85D0, 0x5444,
	0x85D3, 0x85D6, 0x544C,
	0x85D9, 0x85DD, 0x545D,
	0x85E1, 0x85E8, 0x5469,
	0x85EA, 0x85EB, 0x5479,
	0x85EC, 0x85ED, 0x547E,
	0x85F1, 0x85F4, 0x5487,
	0x85F8, 0x85F9, 0x5497,
	0x85FB, 0x85FE, 0x549E,
	0x8645, 0x8647, 0x54B5,
	0x8648, 0x8649, 0x54B9,
	0x864E, 0x864F, 0x54CA,
	0x8653, 0x8657, 0x54E0,
	0x8658, 0x8659, 0x54EB,
	0x865A, 0x865C, 0x54EF,
	0x865D, 0x8662, 0x54F4,
	0x8666, 0x8669, 0x5502,
	0x866B, 0x866F, 0x550A,
	0x8670, 0x8671, 0x5512,
	0x8672, 0x8677, 0x5515,
	0x8678, 0x867B, 0x551C,
	0x867D, 0x867E, 0x5525,
	0x8680, 0x8681, 0x5528,
	0x8685, 0x8687, 0x5534,
	0x8688, 0x868B, 0x5538,
	0x8690, 0x8691, 0x5547,
	0x8692, 0x8696, 0x554B,
	0x8697, 0x869A, 0x5551,
	0x869B, 0x869F, 0x5557,
	0x86A0, 0x86A3, 0x555D,
	0x86A4, 0x86A5, 0x5562,
	0x86A6, 0x86A7, 0x5568,
	0x86A9, 0x86AE, 0x556F,
	0x86AF, 0x86B0, 0x5579,
	0x86B3, 0x86B4, 0x5585,
	0x86B5, 0x86B7, 0x558C,
	0x86B9, 0x86BA, 0x5592,
	0x86BB, 0x86BD, 0x5595,
	0x86BE, 0x86BF, 0x559A,
	0x86C1, 0x86C7, 0x55A0,
	0x86C8, 0x86D0, 0x55A8,
	0x86D7, 0x86DB, 0x55BF,
	0x86DC, 0x86DE, 0x55C6,
	0x86DF, 0x86E0, 0x55CA,
	0x86E1, 0x86E3, 0x55CE,
	0x86E5, 0x86E9, 0x55D7,
	0x86EF, 0x86F0, 0x55ED,
	0x86F1, 0x86F2, 0x55F0,
	0x86F5, 0x86F9, 0x55F8,
	0x86FB, 0x86FE, 0x5602,
	0x8740, 0x8741, 0x5606,
	0x8742, 0x8743, 0x560A,
	0x8745, 0x874C, 0x5610,
	0x874D, 0x874E, 0x5619,
	0x874F, 0x8750, 0x561C,
	0x8751, 0x8753, 0x5620,
	0x8754, 0x8755, 0x5625,
	0x8756, 0x8759, 0x5628,
	0x875A, 0x875C, 0x562E,
	0x875F, 0x8760, 0x5637,
	0x8762, 0x8764, 0x563C,
	0x8765, 0x8770, 0x5640,
	0x8771, 0x8775, 0x564F,
	0x8776, 0x8777, 0x5655,
	0x8778, 0x8779, 0x565A,
	0x877A, 0x877E, 0x565D,
	0x8781, 0x8783, 0x5665,
	0x8784, 0x8787, 0x566D,
	0x8788, 0x878B, 0x5672,
	0x878C, 0x878F, 0x5677,
	0x8790, 0x8797, 0x567D,
	0x8798, 0x879E, 0x5687,
	0x879F, 0x87A1, 0x5690,
	0x87A2, 0x87B0, 0x5694,
	0x87B1, 0x87BB, 0x56A4,
	0x87BC, 0x87C2, 0x56B0,
	0x87C3, 0x87C6, 0x56B8,
	0x87C7, 0x87D3, 0x56BD,
	0x87D4, 0x87DC, 0x56CB,
	0x87DD, 0x87DE, 0x56D5,
	0x87DF, 0x87E0, 0x56D8,
	0x87E3, 0x87E8, 0x56E5,
	0x87EA, 0x87EB, 0x56EE,
	0x87EC, 0x87ED, 0x56F2,
	0x87EE, 0x87F0, 0x56F6,
	0x87F1, 0x87F2, 0x56FB,
	0x87F3, 0x87F5, 0x5700,
	0x87F8, 0x87FE, 0x570B,
	0x8840, 0x8849, 0x5712,
	0x884A, 0x884B, 0x571D,
	0x884C, 0x884E, 0x5720,
	0x884F, 0x8852, 0x5724,
	0x8854, 0x8855, 0x5731,
	0x8856, 0x885A, 0x5734,
	0x885B, 0x885C, 0x573C,
	0x885F, 0x8862, 0x5743,
	0x8863, 0x8864, 0x5748,
	0x8866, 0x886A, 0x5752,
	0x886B, 0x886C, 0x5758,
	0x886D, 0x886E, 0x5762,
	0x8873, 0x8875, 0x5770,
	0x8876, 0x8877, 0x5774,
	0x8878, 0x887A, 0x5778,
	0x887B, 0x887E, 0x577D,
	0x8881, 0x8884, 0x5787,
	0x8885, 0x8889, 0x578D,
	0x888A, 0x8890, 0x5794,
	0x8891, 0x8894, 0x579C,
	0x8899, 0x889B, 0x57AF,
	0x889D, 0x889F, 0x57B5,
	0x88A0, 0x88A8, 0x57B9,
	0x88A9, 0x88AF, 0x57C4,
	0x88B0, 0x88B1, 0x57CC,
	0x88B2, 0x88B3, 0x57D0,
	0x88B5, 0x88B6, 0x57D6,
	0x88B7, 0x88B8, 0x57DB,
	0x88BA, 0x88BC, 0x57E1,
	0x88BD, 0x88C4, 0x57E5,
	0x88C6, 0x88C9, 0x57F0,
	0x88CA, 0x88CC, 0x57F5,
	0x88CD, 0x88CE, 0x57FB,
	0x88CF, 0x88D0, 0x57FE,
	0x88D2, 0x88D4, 0x5803,
	0x88D5, 0x88D7, 0x5808,
	0x88D9, 0x88DB, 0x580E,
	0x88DC, 0x88DE, 0x5812,
	0x88DF, 0x88E1, 0x5816,
	0x88E2, 0x88E5, 0x581A,
	0x88E7, 0x88E8, 0x5822,
	0x88E9, 0x88ED, 0x5825,
	0x88EE, 0x88F2, 0x582B,
	0x88F3, 0x88F6, 0x5831,
	0x88F7, 0x88FE, 0x5836,
	0x8940, 0x8945, 0x583E,
	0x8946, 0x894C, 0x5845,
	0x894D, 0x894F, 0x584E,
	0x8950, 0x8951, 0x5852,
	0x8952, 0x8954, 0x5855,
	0x8955, 0x8959, 0x5859,
	0x895A, 0x895F, 0x585F,
	0x8960, 0x8964, 0x5866,
	0x8965, 0x8975, 0x586D,
	0x8979, 0x897B, 0x5886,
	0x897C, 0x897E, 0x588A,
	0x8980, 0x8984, 0x588D,
	0x8985, 0x8989, 0x5894,
	0x898A, 0x898C, 0x589B,
	0x898D, 0x8994, 0x58A0,
	0x8995, 0x89A6, 0x58AA,
	0x89A7, 0x89AA, 0x58BD,
	0x89AB, 0x89AD, 0x58C2,
	0x89AE, 0x89B8, 0x58C6,
	0x89B9, 0x89BB, 0x58D2,
	0x89BC, 0x89C9, 0x58D6,
	0x89CA, 0x89CF, 0x58E5,
	0x89D2, 0x89D3, 0x58F1,
	0x89D4, 0x89D5, 0x58F4,
	0x89D6, 0x89D7, 0x58F7,
	0x89D8, 0x89DF, 0x58FA,
	0x89E1, 0x89E2, 0x5905,
	0x89E3, 0x89E7, 0x5908,
	0x89E9, 0x89EC, 0x5910,
	0x89ED, 0x89EE, 0x5917,
	0x89F0, 0x89F1, 0x591D,
	0x89F2, 0x89F5, 0x5920,
	0x89FA, 0x89FB, 0x5932,
	0x89FC, 0x89FD, 0x5935,
	0x8A40, 0x8A43, 0x593D,
	0x8A45, 0x8A46, 0x5945,
	0x8A48, 0x8A49, 0x594C,
	0x8A4B, 0x8A4C, 0x5952,
	0x8A4E, 0x8A52, 0x595B,
	0x8A54, 0x8A55, 0x5963,
	0x8A56, 0x8A62, 0x5966,
	0x8A65, 0x8A67, 0x597A,
	0x8A68, 0x8A6A, 0x597E,
	0x8A6D, 0x8A6E, 0x598B,
	0x8A6F, 0x8A72, 0x598E,
	0x8A73, 0x8A74, 0x5994,
	0x8A76, 0x8A79, 0x599A,
	0x8A7A, 0x8A7D, 0x599F,
	0x8A81, 0x8A82, 0x59AC,
	0x8A83, 0x8A84, 0x59B0,
	0x8A85, 0x8A8A, 0x59B3,
	0x8A8C, 0x8A8D, 0x59BC,
	0x8A8E, 0x8A94, 0x59BF,
	0x8A95, 0x8A97, 0x59C7,
	0x8A98, 0x8A9B, 0x59CC,
	0x8A9C, 0x8A9D, 0x59D5,
	0x8AA0, 0x8AA4, 0x59DE,
	0x8AA6, 0x8AA7, 0x59E6,
	0x8AA8, 0x8AAA, 0x59E9,
	0x8AAB, 0x8AB6, 0x59ED,
	0x8AB8, 0x8ABA, 0x59FC,
	0x8ABD, 0x8ABE, 0x5A0A,
	0x8ABF, 0x8AC2, 0x5A0D,
	0x8AC4, 0x8AC7, 0x5A14,
	0x8AC8, 0x8ACA, 0x5A19,
	0x8ACB, 0x8ACC, 0x5A1D,
	0x8ACD, 0x8ACE, 0x5A21,
	0x8AD0, 0x8AD2, 0x5A26,
	0x8AD3, 0x8AD9, 0x5A2A,
	0x8ADC, 0x8AE0, 0x5A37,
	0x8AE1, 0x8AE3, 0x5A3D,
	0x8AE4, 0x8AE8, 0x5A41,
	0x8AE9, 0x8AEA, 0x5A47,
	0x8AEB, 0x8AF4, 0x5A4B,
	0x8AF5, 0x8AF8, 0x5A56,
	0x8AF9, 0x8AFE, 0x5A5B,
	0x8B41, 0x8B44, 0x5A63,
	0x8B45, 0x8B46, 0x5A68,
	0x8B47, 0x8B4F, 0x5A6B,
	0x8B50, 0x8B51, 0x5A78,
	0x8B52, 0x8B55, 0x5A7B,
	0x8B56, 0x8B67, 0x5A80,
	0x8B68, 0x8B6E, 0x5A93,
	0x8B6F, 0x8B7C, 0x5A9C,
	0x8B7D, 0x8B7E, 0x5AAB,
	0x8B80, 0x8B84, 0x5AAD,
	0x8B86, 0x8B87, 0x5AB6,
	0x8B88, 0x8B8C, 0x5AB9,
	0x8B8D, 0x8B8E, 0x5ABF,
	0x8B8F, 0x8B94, 0x5AC3,
	0x8B95, 0x8B96, 0x5ACA,
	0x8B97, 0x8B9B, 0x5ACD,
	0x8B9F, 0x8BA1, 0x5AD9,
	0x8BA2, 0x8BA4, 0x5ADD,
	0x8BA6, 0x8BA7, 0x5AE4,
	0x8BA8, 0x8BA9, 0x5AE7,
	0x8BAB, 0x8BAF, 0x5AEC,
	0x8BB0, 0x8BC6, 0x5AF2,
	0x8BC7, 0x8BD2, 0x5B0A,
	0x8BD3, 0x8BEC, 0x5B18,
	0x8BEE, 0x8BEF, 0x5B35,
	0x8BF0, 0x8BF7, 0x5B38,
	0x8BF8, 0x8BFE, 0x5B41,
	0x8C40, 0x8C47, 0x5B48,
	0x8C4B, 0x8C4C, 0x5B60,
	0x8C4D, 0x8C4E, 0x5B67,
	0x8C50, 0x8C52, 0x5B6D,
	0x8C55, 0x8C58, 0x5B76,
	0x8C59, 0x8C5A, 0x5B7B,
	0x8C5B, 0x8C5C, 0x5B7E,
	0x8C60, 0x8C61, 0x5B8D,
	0x8C62, 0x8C64, 0x5B90,
	0x8C68, 0x8C6A, 0x5BA7,
	0x8C6B, 0x8C6E, 0x5BAC,
	0x8C6F, 0x8C70, 0x5BB1,
	0x8C72, 0x8C74, 0x5BBA,
	0x8C75, 0x8C76, 0x5BC0,
	0x8C78, 0x8C7B, 0x5BC8,
	0x8C7C, 0x8C7E, 0x5BCD,
	0x8C81, 0x8C89, 0x5BD4,
	0x8C8B, 0x8C8C, 0x5BE2,
	0x8C8D, 0x8C8E, 0x5BE6,
	0x8C8F, 0x8C93, 0x5BE9,
	0x8C95, 0x8C9B, 0x5BF1,
	0x8C9C, 0x8C9D, 0x5BFD,
	0x8C9F, 0x8CA0, 0x5C02,
	0x8CA2, 0x8CA3, 0x5C07,
	0x8CA4, 0x8CA7, 0x5C0B,
	0x8CA9, 0x8CAA, 0x5C12,
	0x8CAE, 0x8CB1, 0x5C1E,
	0x8CB4, 0x8CB7, 0x5C28,
	0x8CB8, 0x8CBB, 0x5C2D,
	0x8CBC, 0x8CBD, 0x5C32,
	0x8CBE, 0x8CC0, 0x5C35,
	0x8CC1, 0x8CC2, 0x5C43,
	0x8CC3, 0x8CC4, 0x5C46,
	0x8CC5, 0x8CC6, 0x5C4C,
	0x8CC7, 0x8CC9, 0x5C52,
	0x8CCA, 0x8CCC, 0x5C56,
	0x8CCD, 0x8CD0, 0x5C5A,
	0x8CD4, 0x8CDA, 0x5C67,
	0x8CDC, 0x8CE2, 0x5C72,
	0x8CE3, 0x8CE6, 0x5C7B,
	0x8CE8, 0x8CEC, 0x5C83,
	0x8CED, 0x8CEF, 0x5C89,
	0x8CF0, 0x8CF1, 0x5C8E,
	0x8CF2, 0x8CF3, 0x5C92,
	0x8CF5, 0x8CF9, 0x5C9D,
	0x8CFA, 0x8CFE, 0x5CA4,
	0x8D41, 0x8D43, 0x5CAE,
	0x8D47, 0x8D4A, 0x5CB9,
	0x8D4D, 0x8D4E, 0x5CC2,
	0x8D4F, 0x8D54, 0x5CC5,
	0x8D55, 0x8D5A, 0x5CCC,
	0x8D5B, 0x8D60, 0x5CD3,
	0x8D61, 0x8D67, 0x5CDA,
	0x8D68, 0x8D69, 0x5CE2,
	0x8D6C, 0x8D6D, 0x5CEB,
	0x8D6E, 0x8D6F, 0x5CEE,
	0x8D70, 0x8D79, 0x5CF1,
	0x8D7A, 0x8D7E, 0x5CFC,
	0x8D81, 0x8D82, 0x5D04,
	0x8D83, 0x8D88, 0x5D08,
	0x8D89, 0x8D8D, 0x5D0F,
	0x8D8F, 0x8D92, 0x5D17,
	0x8D93, 0x8D94, 0x5D1C,
	0x8D95, 0x8D99, 0x5D1F,
	0x8D9C, 0x8D9E, 0x5D2A,
	0x8D9F, 0x8DA3, 0x5D2F,
	0x8DA4, 0x8DAB, 0x5D35,
	0x8DAC, 0x8DB3, 0x5D3F,
	0x8DB4, 0x8DB5, 0x5D48,
	0x8DB6, 0x8DC0, 0x5D4D,
	0x8DC1, 0x8DC2, 0x5D59,
	0x8DC4, 0x8DCE, 0x5D5E,
	0x8DD0, 0x8DD1, 0x5D6D,
	0x8DD2, 0x8DD5, 0x5D70,
	0x8DD6, 0x8DE2, 0x5D75,
	0x8DE3, 0x8DF8, 0x5D83,
	0x8DF9, 0x8DFB, 0x5D9A,
	0x8DFC, 0x8DFE, 0x5D9E,
	0x8E40, 0x8E55, 0x5DA1,
	0x8E56, 0x8E62, 0x5DB8,
	0x8E63, 0x8E69, 0x5DC6,
	0x8E6A, 0x8E76, 0x5DCE,
	0x8E78, 0x8E79, 0x5DDF,
	0x8E7A, 0x8E7B, 0x5DE3,
	0x8E7D, 0x8E7E, 0x5DEC,
	0x8E81, 0x8E82, 0x5DF5,
	0x8E83, 0x8E87, 0x5DF8,
	0x8E88, 0x8E89, 0x5DFF,
	0x8E8C, 0x8E8E, 0x5E09,
	0x8E8F, 0x8E90, 0x5E0D,
	0x8E91, 0x8E92, 0x5E12,
	0x8E94, 0x8E9B, 0x5E1E,
	0x8E9C, 0x8EA0, 0x5E28,
	0x8EA1, 0x8EA2, 0x5E2F,
	0x8EA3, 0x8EA7, 0x5E32,
	0x8EA8, 0x8EA9, 0x5E39,
	0x8EAA, 0x8EAD, 0x5E3E,
	0x8EAF, 0x8EB4, 0x5E46,
	0x8EB5, 0x8EBB, 0x5E4D,
	0x8EBC, 0x8EC0, 0x5E56,
	0x8EC1, 0x8EC2, 0x5E5C,
	0x8EC3, 0x8EC4, 0x5E5F,
	0x8EC5, 0x8ED3, 0x5E63,
	0x8ED8, 0x8EDA, 0x5E81,
	0x8EDC, 0x8EDD, 0x5E88,
	0x8EDE, 0x8EE0, 0x5E8C,
	0x8EE5, 0x8EE8, 0x5EA1,
	0x8EE9, 0x8EED, 0x5EA8,
	0x8EEE, 0x8EF2, 0x5EAE,
	0x8EF4, 0x8EF7, 0x5EBA,
	0x8EF8, 0x8EFE, 0x5EBF,
	0x8F40, 0x8F42, 0x5EC6,
	0x8F43, 0x8F48, 0x5ECB,
	0x8F49, 0x8F4A, 0x5ED4,
	0x8F4B, 0x8F4E, 0x5ED7,
	0x8F4F, 0x8F5A, 0x5EDC,
	0x8F5C, 0x8F64, 0x5EEB,
	0x8F66, 0x8F67, 0x5EF8,
	0x8F68, 0x8F6A, 0x5EFB,
	0x8F6B, 0x8F6D, 0x5F05,
	0x8F6F, 0x8F71, 0x5F0C,
	0x8F76, 0x8F77, 0x5F19,
	0x8F78, 0x8F7A, 0x5F1C,
	0x8F7B, 0x8F7E, 0x5F21,
	0x8F81, 0x8F82, 0x5F2B,
	0x8F85, 0x8F8B, 0x5F32,
	0x8F8D, 0x8F8F, 0x5F3D,
	0x8F90, 0x8F9E, 0x5F41,
	0x8FA1, 0x8FA4, 0x5F59,
	0x8FA5, 0x8FA7, 0x5F5E,
	0x8FAA, 0x8FAB, 0x5F67,
	0x8FAD, 0x8FAE, 0x5F6E,
	0x8FB0, 0x8FB2, 0x5F74,
	0x8FB5, 0x8FB7, 0x5F7D,
	0x8FBA, 0x8FBC, 0x5F8D,
	0x8FBE, 0x8FBF, 0x5F93,
	0x8FC1, 0x8FC2, 0x5F9A,
	0x8FC3, 0x8FC6, 0x5F9D,
	0x8FC7, 0x8FCC, 0x5FA2,
	0x8FCE, 0x8FCF, 0x5FAB,
	0x8FD0, 0x8FD5, 0x5FAF,
	0x8FD7, 0x8FDA, 0x5FB8,
	0x8FDB, 0x8FDF, 0x5FBE,
	0x8FE0, 0x8FE1, 0x5FC7,
	0x8FE2, 0x8FE3, 0x5FCA,
	0x8FE5, 0x8FE7, 0x5FD3,
	0x8FE8, 0x8FEA, 0x5FDA,
	0x8FEB, 0x8FEC, 0x5FDE,
	0x8FED, 0x8FEE, 0x5FE2,
	0x8FEF, 0x8FF0, 0x5FE5,
	0x8FF1, 0x8FF2, 0x5FE8,
	0x8FF4, 0x8FF5, 0x5FEF,
	0x8FF6, 0x8FF8, 0x5FF2,
	0x8FF9, 0x8FFA, 0x5FF6,
	0x8FFB, 0x8FFC, 0x5FF9,
	0x9040, 0x9041, 0x6008,
	0x9042, 0x9043, 0x600B,
	0x9044, 0x9045, 0x6010,
	0x9047, 0x9048, 0x6017,
	0x904A, 0x904B, 0x601E,
	0x904C, 0x904E, 0x6022,
	0x904F, 0x9051, 0x602C,
	0x9052, 0x9056, 0x6030,
	0x9057, 0x905B, 0x6036,
	0x905C, 0x905D, 0x603D,
	0x905F, 0x9065, 0x6044,
	0x9067, 0x9068, 0x604E,
	0x906A, 0x906B, 0x6053,
	0x906C, 0x906E, 0x6056,
	0x906F, 0x9070, 0x605B,
	0x9071, 0x9074, 0x605E,
	0x9075, 0x9076, 0x6065,
	0x9078, 0x9079, 0x6071,
	0x907A, 0x907B, 0x6074,
	0x9080, 0x9081, 0x6081,
	0x9082, 0x9085, 0x6085,
	0x9086, 0x9087, 0x608A,
	0x9088, 0x908B, 0x608E,
	0x908E, 0x9090, 0x6097,
	0x9093, 0x9094, 0x60A1,
	0x9095, 0x9096, 0x60A4,
	0x9098, 0x9099, 0x60A9,
	0x909D, 0x909F, 0x60B5,
	0x90A0, 0x90A1, 0x60B9,
	0x90A2, 0x90A9, 0x60BD,
	0x90AA, 0x90AC, 0x60C7,
	0x90AD, 0x90B1, 0x60CC,
	0x90B2, 0x90B4, 0x60D2,
	0x90B5, 0x90B6, 0x60D6,
	0x90BA, 0x90BE, 0x60E1,
	0x90C0, 0x90C1, 0x60F1,
	0x90C3, 0x90C4, 0x60F7,
	0x90C5, 0x90C9, 0x60FB,
	0x90CA, 0x90CD, 0x6102,
	0x90CF, 0x90D1, 0x610A,
	0x90D2, 0x90D6, 0x6110,
	0x90D7, 0x90DA, 0x6116,
	0x90DB, 0x90DE, 0x611B,
	0x90DF, 0x90E0, 0x6121,
	0x90E2, 0x90E4, 0x6128,
	0x90E5, 0x90F7, 0x612C,
	0x90F8, 0x90FE, 0x6140,
	0x9144, 0x9145, 0x614F,
	0x9146, 0x9148, 0x6152,
	0x9149, 0x914F, 0x6156,
	0x9150, 0x9153, 0x615E,
	0x9154, 0x9157, 0x6163,
	0x9158, 0x915E, 0x6169,
	0x915F, 0x9162, 0x6171,
	0x9164, 0x9176, 0x6178,
	0x9177, 0x9178, 0x618C,
	0x9179, 0x917D, 0x618F,
	0x9180, 0x9186, 0x6196,
	0x9187, 0x918F, 0x619E,
	0x9190, 0x9191, 0x61AA,
	0x9192, 0x919B, 0x61AD,
	0x919C, 0x91A1, 0x61B8,
	0x91A2, 0x91A4, 0x61BF,
	0x91A5, 0x91A9, 0x61C3,
	0x91AB, 0x91AF, 0x61CC,
	0x91B1, 0x91C1, 0x61D5,
	0x91C2, 0x91CF, 0x61E7,
	0x91D0, 0x91D8, 0x61F6,
	0x91D9, 0x91DE, 0x6200,
	0x91E1, 0x91E2, 0x6213,
	0x91E4, 0x91E6, 0x621C,
	0x91E9, 0x91EC, 0x6226,
	0x91EF, 0x91F2, 0x622F,
	0x91F3, 0x91F4, 0x6235,
	0x91F5, 0x91F9, 0x6238,
	0x91FB, 0x91FD, 0x6244,
	0x9240, 0x9241, 0x624F,
	0x9242, 0x9244, 0x6255,
	0x9245, 0x9246, 0x6259,
	0x9247, 0x924D, 0x625C,
	0x924E, 0x924F, 0x6264,
	0x9251, 0x9252, 0x6271,
	0x9253, 0x9254, 0x6274,
	0x9255, 0x9256, 0x6277,
	0x9257, 0x9258, 0x627A,
	0x925A, 0x925C, 0x6281,
	0x925D, 0x9260, 0x6285,
	0x9261, 0x9266, 0x628B,
	0x9269, 0x926B, 0x629C,
	0x926D, 0x926E, 0x62A6,
	0x926F, 0x9270, 0x62A9,
	0x9271, 0x9274, 0x62AD,
	0x9275, 0x9277, 0x62B2,
	0x9278, 0x927A, 0x62B6,
	0x927D, 0x927E, 0x62C0,
	0x9285, 0x9286, 0x62DD,
	0x9287, 0x9288, 0x62E0,
	0x928A, 0x928B, 0x62EA,
	0x928F, 0x9292, 0x62F8,
	0x9294, 0x9297, 0x6303,
	0x9298, 0x929B, 0x630A,
	0x929C, 0x929D, 0x630F,
	0x929E, 0x92A1, 0x6312,
	0x92A2, 0x92A4, 0x6317,
	0x92A6, 0x92A7, 0x6326,
	0x92A9, 0x92AB, 0x632C,
	0x92AC, 0x92AD, 0x6330,
	0x92AE, 0x92B3, 0x6333,
	0x92B4, 0x92B5, 0x633B,
	0x92B6, 0x92B9, 0x633E,
	0x92BB, 0x92BC, 0x6347,
	0x92BE, 0x92C1, 0x6351,
	0x92C2, 0x92C9, 0x6356,
	0x92CB, 0x92CD, 0x6364,
	0x92CF, 0x92D1, 0x636A,
	0x92D2, 0x92D3, 0x636F,
	0x92D4, 0x92D7, 0x6372,
	0x92D8, 0x92D9, 0x6378,
	0x92DA, 0x92DD, 0x637C,
	0x92DF, 0x92E2, 0x6383,
	0x92E6, 0x92E8, 0x6393,
	0x92EA, 0x92F0, 0x6399,
	0x92F6, 0x92F7, 0x63B1,
	0x92F8, 0x92F9, 0x63B5,
	0x92FD, 0x92FE, 0x63BF,
	0x9340, 0x9342, 0x63C1,
	0x9344, 0x9345, 0x63C7,
	0x9346, 0x9348, 0x63CA,
	0x934A, 0x934C, 0x63D3,
	0x934D, 0x9353, 0x63D7,
	0x9356, 0x935A, 0x63E4,
	0x935B, 0x935C, 0x63EB,
	0x935D, 0x9360, 0x63EE,
	0x9364, 0x9367, 0x63F9,
	0x9369, 0x936A, 0x6403,
	0x936B, 0x936F, 0x6406,
	0x9370, 0x9371, 0x640D,
	0x9372, 0x9373, 0x6411,
	0x9374, 0x9379, 0x6415,
	0x937C, 0x937E, 0x6422,
	0x9381, 0x9383, 0x6427,
	0x9385, 0x938A, 0x642E,
	0x938B, 0x938F, 0x6435,
	0x9390, 0x9391, 0x643B,
	0x9394, 0x9395, 0x6442,
	0x9397, 0x939D, 0x644B,
	0x939F, 0x93A1, 0x6455,
	0x93A2, 0x93A6, 0x6459,
	0x93A7, 0x93AE, 0x645F,
	0x93B0, 0x93B2, 0x646A,
	0x93B3, 0x93BC, 0x646E,
	0x93BD, 0x93C3, 0x647B,
	0x93C6, 0x93CE, 0x6488,
	0x93CF, 0x93D0, 0x6493,
	0x93D1, 0x93D2, 0x6497,
	0x93D3, 0x93D6, 0x649A,
	0x93D7, 0x93DB, 0x649F,
	0x93DC, 0x93DF, 0x64A5,
	0x93E0, 0x93E1, 0x64AA,
	0x93E3, 0x93E6, 0x64B1,
	0x93EA, 0x93EC, 0x64BD,
	0x93EE, 0x93EF, 0x64C3,
	0x93F0, 0x93F6, 0x64C6,
	0x93F9, 0x93FC, 0x64D3,
	0x93FD, 0x93FE, 0x64D9,
	0x9440, 0x9442, 0x64DB,
	0x9443, 0x9445, 0x64DF,
	0x9448, 0x9460, 0x64E7,
	0x9461, 0x9468, 0x6501,
	0x9469, 0x9470, 0x650A,
	0x9471, 0x9475, 0x6513,
	0x9476, 0x947E, 0x6519,
	0x9480, 0x9482, 0x6522,
	0x9483, 0x9487, 0x6526,
	0x9488, 0x9489, 0x652C,
	0x948A, 0x948D, 0x6530,
	0x9490, 0x9491, 0x653C,
	0x9492, 0x9496, 0x6540,
	0x9497, 0x9498, 0x6546,
	0x9499, 0x949A, 0x654A,
	0x949B, 0x949C, 0x654D,
	0x949E, 0x94A0, 0x6552,
	0x94A1, 0x94A2, 0x6557,
	0x94A5, 0x94A7, 0x655F,
	0x94A8, 0x94A9, 0x6564,
	0x94AA, 0x94AD, 0x6567,
	0x94AE, 0x94B0, 0x656D,
	0x94B3, 0x94B4, 0x6575,
	0x94B5, 0x94C3, 0x6578,
	0x94C4, 0x94C6, 0x6588,
	0x94C7, 0x94C9, 0x658D,
	0x94CB, 0x94CD, 0x6594,
	0x94D0, 0x94D1, 0x659D,
	0x94D3, 0x94D4, 0x65A2,
	0x94DA, 0x94E1, 0x65B1,
	0x94E2, 0x94E3, 0x65BA,
	0x94E4, 0x94E6, 0x65BE,
	0x94E8, 0x94EB, 0x65C7,
	0x94ED, 0x94EE, 0x65D0,
	0x94EF, 0x94F1, 0x65D3,
	0x94F2, 0x94F9, 0x65D8,
	0x94FB, 0x94FC, 0x65E3,
	0x94FD, 0x94FE, 0x65EA,
	0x9540, 0x9543, 0x65F2,
	0x9544, 0x9545, 0x65F8,
	0x9546, 0x954A, 0x65FB,
	0x954C, 0x954D, 0x6604,
	0x954E, 0x9550, 0x6607,
	0x9553, 0x9555, 0x6610,
	0x9556, 0x9558, 0x6616,
	0x9559, 0x955B, 0x661A,
	0x955D, 0x9560, 0x6621,
	0x9562, 0x9565, 0x6629,
	0x9568, 0x9569, 0x6632,
	0x956A, 0x956E, 0x6637,
	0x9570, 0x9571, 0x663F,
	0x9573, 0x9579, 0x6644,
	0x957A, 0x957B, 0x664D,
	0x957C, 0x957D, 0x6650,
	0x9581, 0x9584, 0x665B,
	0x9586, 0x9587, 0x6662,
	0x958A, 0x958E, 0x6669,
	0x958F, 0x9591, 0x6671,
	0x9593, 0x9594, 0x6678,
	0x9595, 0x9597, 0x667B,
	0x9598, 0x959A, 0x667F,
	0x959C, 0x959D, 0x6685,
	0x959E, 0x95A1, 0x6688,
	0x95A2, 0x95A5, 0x668D,
	0x95A6, 0x95A9, 0x6692,
	0x95AA, 0x95AE, 0x6698,
	0x95AF, 0x95B7, 0x669E,
	0x95B8, 0x95BC, 0x66A9,
	0x95BD, 0x95C1, 0x66AF,
	0x95C2, 0x95C5, 0x66B5,
	0x95C6, 0x95C9, 0x66BA,
	0x95CA, 0x95E3, 0x66BF,
	0x95E5, 0x95EC, 0x66DE,
	0x95ED, 0x95EE, 0x66E7,
	0x95EF, 0x95F4, 0x66EA,
	0x95F6, 0x95F7, 0x66F5,
	0x95F9, 0x95FA, 0x66FA,
	0x95FC, 0x95FE, 0x6701,
	0x9640, 0x9643, 0x6704,
	0x9645, 0x9646, 0x670E,
	0x9647, 0x9649, 0x6711,
	0x964B, 0x964D, 0x6718,
	0x9650, 0x9655, 0x6720,
	0x965A, 0x965B, 0x6732,
	0x965C, 0x965F, 0x6736,
	0x9660, 0x9661, 0x673B,
	0x9662, 0x9663, 0x673E,
	0x9665, 0x9666, 0x6744,
	0x9668, 0x9669, 0x674A,
	0x966C, 0x966D, 0x6754,
	0x966E, 0x9672, 0x6757,
	0x9674, 0x9676, 0x6762,
	0x9677, 0x9678, 0x6766,
	0x9679, 0x967A, 0x676B,
	0x9680, 0x9683, 0x6778,
	0x9686, 0x9687, 0x6782,
	0x9688, 0x9689, 0x6785,
	0x968C, 0x968F, 0x678C,
	0x9690, 0x9693, 0x6791,
	0x9697, 0x9699, 0x679F,
	0x969F, 0x96A0, 0x67B1,
	0x96A2, 0x96A9, 0x67B9,
	0x96AB, 0x96B4, 0x67C5,
	0x96B5, 0x96B7, 0x67D5,
	0x96BB, 0x96BC, 0x67E3,
	0x96BD, 0x96BF, 0x67E6,
	0x96C0, 0x96C1, 0x67EA,
	0x96C2, 0x96C3, 0x67ED,
	0x96C5, 0x96CC, 0x67F5,
	0x96CE, 0x96D1, 0x6801,
	0x96D6, 0x96D7, 0x6814,
	0x96D8, 0x96DC, 0x6818,
	0x96DD, 0x96DF, 0x681E,
	0x96E0, 0x96E6, 0x6822,
	0x96E7, 0x96ED, 0x682B,
	0x96EE, 0x96F0, 0x6834,
	0x96F1, 0x96F2, 0x683A,
	0x96F9, 0x96FE, 0x6856,
	0x9740, 0x9743, 0x685C,
	0x9745, 0x974C, 0x686C,
	0x974E, 0x9756, 0x6878,
	0x9759, 0x9760, 0x6887,
	0x9761, 0x9763, 0x6890,
	0x9764, 0x9766, 0x6894,
	0x9767, 0x9770, 0x6898,
	0x9771, 0x9773, 0x68A3,
	0x9774, 0x9777, 0x68A9,
	0x9779, 0x977A, 0x68B1,
	0x977C, 0x977E, 0x68B6,
	0x9780, 0x9786, 0x68B9,
	0x9788, 0x978D, 0x68C3,
	0x9790, 0x9793, 0x68CE,
	0x9794, 0x9795, 0x68D3,
	0x9796, 0x9797, 0x68D6,
	0x9799, 0x979D, 0x68DB,
	0x979E, 0x979F, 0x68E1,
	0x97A0, 0x97A9, 0x68E4,
	0x97AB, 0x97AD, 0x68F2,
	0x97AE, 0x97B0, 0x68F6,
	0x97B2, 0x97B5, 0x68FD,
	0x97B6, 0x97B8, 0x6902,
	0x97B9, 0x97BD, 0x6906,
	0x97C1, 0x97CC, 0x6913,
	0x97CD, 0x97CF, 0x6921,
	0x97D0, 0x97D7, 0x6925,
	0x97D8, 0x97D9, 0x692E,
	0x97DA, 0x97DC, 0x6931,
	0x97DD, 0x97E0, 0x6935,
	0x97E1, 0x97E3, 0x693A,
	0x97E5, 0x97E6, 0x6940,
	0x97E7, 0x97F7, 0x6943,
	0x97F8, 0x97F9, 0x6955,
	0x97FA, 0x97FB, 0x6958,
	0x97FC, 0x97FD, 0x695B,
	0x9840, 0x9841, 0x6961,
	0x9842, 0x9843, 0x6964,
	0x9844, 0x9847, 0x6967,
	0x9848, 0x9849, 0x696C,
	0x984A, 0x984B, 0x696F,
	0x984C, 0x9850, 0x6972,
	0x9851, 0x9852, 0x697A,
	0x9853, 0x9855, 0x697D,
	0x9859, 0x985B, 0x698A,
	0x985C, 0x9861, 0x698E,
	0x9862, 0x9863, 0x6996,
	0x9864, 0x9865, 0x6999,
	0x9866, 0x986F, 0x699D,
	0x9870, 0x9871, 0x69A9,
	0x9873, 0x9875, 0x69AE,
	0x9876, 0x9877, 0x69B2,
	0x9878, 0x9879, 0x69B5,
	0x987A, 0x987C, 0x69B8,
	0x987D, 0x987E, 0x69BC,
	0x9880, 0x9882, 0x69BE,
	0x9883, 0x988A, 0x69C2,
	0x988E, 0x9890, 0x69D1,
	0x9891, 0x9896, 0x69D5,
	0x9897, 0x9899, 0x69DC,
	0x989A, 0x98A5, 0x69E1,
	0x98A6, 0x98A9, 0x69EE,
	0x98AA, 0x98B3, 0x69F3,
	0x98B5, 0x98BE, 0x6A00,
	0x98BF, 0x98CA, 0x6A0B,
	0x98CB, 0x98D0, 0x6A19,
	0x98D2, 0x98D7, 0x6A22,
	0x98D9, 0x98DC, 0x6A2B,
	0x98DE, 0x98E0, 0x6A32,
	0x98E1, 0x98E7, 0x6A36,
	0x98E8, 0x98EC, 0x6A3F,
	0x98ED, 0x98EE, 0x6A45,
	0x98EF, 0x98F6, 0x6A48,
	0x98F7, 0x98FD, 0x6A51,
	0x9940, 0x9944, 0x6A5C,
	0x9945, 0x9947, 0x6A62,
	0x9948, 0x9952, 0x6A66,
	0x9953, 0x9959, 0x6A72,
	0x995A, 0x995B, 0x6A7A,
	0x995C, 0x995E, 0x6A7D,
	0x995F, 0x9961, 0x6A81,
	0x9962, 0x996A, 0x6A85,
	0x996C, 0x9970, 0x6A92,
	0x9971, 0x9978, 0x6A98,
	0x9979, 0x997E, 0x6AA1,
	0x9980, 0x9981, 0x6AA7,
	0x9983, 0x99F5, 0x6AAD,
	0x99F6, 0x99F7, 0x6B25,
	0x99F8, 0x99FE, 0x6B28,
	0x9A40, 0x9A42, 0x6B2F,
	0x9A43, 0x9A46, 0x6B33,
	0x9A48, 0x9A4A, 0x6B3B,
	0x9A4B, 0x9A4E, 0x6B3F,
	0x9A4F, 0x9A50, 0x6B44,
	0x9A52, 0x9A53, 0x6B4A,
	0x9A54, 0x9A5F, 0x6B4D,
	0x9A60, 0x9A67, 0x6B5A,
	0x9A68, 0x9A69, 0x6B68,
	0x9A6A, 0x9A77, 0x6B6B,
	0x9A79, 0x9A7C, 0x6B7D,
	0x9A81, 0x9A84, 0x6B8E,
	0x9A85, 0x9A86, 0x6B94,
	0x9A87, 0x9A89, 0x6B97,
	0x9A8A, 0x9A8E, 0x6B9C,
	0x9A8F, 0x9A96, 0x6BA2,
	0x9A97, 0x9A9E, 0x6BAB,
	0x9AA0, 0x9AA6, 0x6BB8,
	0x9AA8, 0x9AA9, 0x6BC3,
	0x9AAA, 0x9AAE, 0x6BC6,
	0x9AB1, 0x9AB2, 0x6BD0,
	0x9AB5, 0x9AB9, 0x6BDC,
	0x9ABA, 0x9AC1, 0x6BE2,
	0x9AC2, 0x9AC4, 0x6BEC,
	0x9AC5, 0x9AC7, 0x6BF0,
	0x9AC9, 0x9ACB, 0x6BF6,
	0x9ACC, 0x9ACE, 0x6BFA,
	0x9ACF, 0x9AD5, 0x6BFE,
	0x9AD6, 0x9ADA, 0x6C08,
	0x9ADE, 0x9AE0, 0x6C1C,
	0x9AE4, 0x9AE6, 0x6C2B,
	0x9AE9, 0x9AEA, 0x6C36,
	0x9AEB, 0x9AEE, 0x6C39,
	0x9AEF, 0x9AF0, 0x6C3E,
	0x9AF1, 0x9AF3, 0x6C43,
	0x9AF5, 0x9AF9, 0x6C4B,
	0x9AFA, 0x9AFC, 0x6C51,
	0x9B40, 0x9B41, 0x6C59,
	0x9B42, 0x9B43, 0x6C62,
	0x9B44, 0x9B46, 0x6C65,
	0x9B47, 0x9B4B, 0x6C6B,
	0x9B4F, 0x9B50, 0x6C77,
	0x9B51, 0x9B53, 0x6C7A,
	0x9B54, 0x9B55, 0x6C7F,
	0x9B58, 0x9B59, 0x6C8A,
	0x9B5A, 0x9B5B, 0x6C8D,
	0x9B5C, 0x9B5D, 0x6C91,
	0x9B5E, 0x9B61, 0x6C95,
	0x9B63, 0x9B65, 0x6C9C,
	0x9B6A, 0x9B6B, 0x6CAF,
	0x9B6C, 0x9B6F, 0x6CB4,
	0x9B71, 0x9B74, 0x6CC0,
	0x9B75, 0x9B77, 0x6CC6,
	0x9B79, 0x9B7B, 0x6CCD,
	0x9B7C, 0x9B7D, 0x6CD1,
	0x9B80, 0x9B81, 0x6CD9,
	0x9B82, 0x9B83, 0x6CDC,
	0x9B86, 0x9B87, 0x6CE6,
	0x9B89, 0x9B8A, 0x6CEC,
	0x9B8E, 0x9B8F, 0x6CFF,
	0x9B90, 0x9B91, 0x6D02,
	0x9B92, 0x9B93, 0x6D05,
	0x9B94, 0x9B96, 0x6D08,
	0x9B98, 0x9B9A, 0x6D0F,
	0x9B9B, 0x9B9E, 0x6D13,
	0x9BA0, 0x9BA1, 0x6D1C,
	0x9BA2, 0x9BA7, 0x6D1F,
	0x9BA9, 0x9BAA, 0x6D28,
	0x9BAB, 0x9BAC, 0x6D2C,
	0x9BAD, 0x9BAE, 0x6D2F,
	0x9BB0, 0x9BB2, 0x6D36,
	0x9BB4, 0x9BB5, 0x6D3F,
	0x9BBB, 0x9BBE, 0x6D55,
	0x9BC2, 0x9BC3, 0x6D61,
	0x9BC4, 0x9BC5, 0x6D64,
	0x9BC6, 0x9BC7, 0x6D67,
	0x9BC8, 0x9BCA, 0x6D6B,
	0x9BCB, 0x9BCE, 0x6D70,
	0x9BCF, 0x9BD0, 0x6D75,
	0x9BD1, 0x9BD3, 0x6D79,
	0x9BD4, 0x9BD8, 0x6D7D,
	0x9BD9, 0x9BDA, 0x6D83,
	0x9BDB, 0x9BDC, 0x6D86,
	0x9BDD, 0x9BDE, 0x6D8A,
	0x9BE0, 0x9BE1, 0x6D8F,
	0x9BE3, 0x9BE7, 0x6D96,
	0x9BEB, 0x9BEC, 0x6DAC,
	0x9BED, 0x9BEE, 0x6DB0,
	0x9BEF, 0x9BF0, 0x6DB3,
	0x9BF1, 0x9BF2, 0x6DB6,
	0x9BF3, 0x9BF8, 0x6DB9,
	0x9BF9, 0x9BFB, 0x6DC1,
	0x9BFC, 0x9BFE, 0x6DC8,
	0x9C40, 0x9C43, 0x6DCD,
	0x9C44, 0x9C47, 0x6DD2,
	0x9C49, 0x9C4B, 0x6DDA,
	0x9C4D, 0x9C4E, 0x6DE2,
	0x9C50, 0x9C53, 0x6DE7,
	0x9C55, 0x9C56, 0x6DEF,
	0x9C58, 0x9C5A, 0x6DF4,
	0x9C5D, 0x9C64, 0x6DFD,
	0x9C65, 0x9C68, 0x6E06,
	0x9C6B, 0x9C6C, 0x6E12,
	0x9C6E, 0x9C6F, 0x6E18,
	0x9C70, 0x9C71, 0x6E1B,
	0x9C72, 0x9C73, 0x6E1E,
	0x9C75, 0x9C77, 0x6E26,
	0x9C7B, 0x9C7C, 0x6E30,
	0x9C80, 0x9C81, 0x6E36,
	0x9C83, 0x9C8A, 0x6E3B,
	0x9C8B, 0x9C92, 0x6E45,
	0x9C93, 0x9C96, 0x6E4F,
	0x9C99, 0x9C9A, 0x6E59,
	0x9C9B, 0x9C9D, 0x6E5C,
	0x9C9E, 0x9CA8, 0x6E60,
	0x9CA9, 0x9CAA, 0x6E6C,
	0x9CAB, 0x9CB9, 0x6E6F,
	0x9CBA, 0x9CBC, 0x6E80,
	0x9CBE, 0x9CBF, 0x6E87,
	0x9CC0, 0x9CC4, 0x6E8A,
	0x9CC5, 0x9CCB, 0x6E91,
	0x9CCC, 0x9CCE, 0x6E99,
	0x9CCF, 0x9CD0, 0x6E9D,
	0x9CD1, 0x9CD2, 0x6EA0,
	0x9CD3, 0x9CD4, 0x6EA3,
	0x9CD6, 0x9CD7, 0x6EA8,
	0x9CD8, 0x9CDB, 0x6EAB,
	0x9CDF, 0x9CE0, 0x6EB8,
	0x9CE2, 0x9CE4, 0x6EBE,
	0x9CE5, 0x9CE8, 0x6EC3,
	0x9CE9, 0x9CEB, 0x6EC8,
	0x9CEC, 0x9CEE, 0x6ECC,
	0x9CF2, 0x9CF3, 0x6ED8,
	0x9CF4, 0x9CF6, 0x6EDB,
	0x9CF9, 0x9CFE, 0x6EEA,
	0x9D40, 0x9D43, 0x6EF0,
	0x9D44, 0x9D47, 0x6EF5,
	0x9D48, 0x9D4F, 0x6EFA,
	0x9D50, 0x9D52, 0x6F03,
	0x9D53, 0x9D54, 0x6F07,
	0x9D55, 0x9D59, 0x6F0A,
	0x9D5A, 0x9D5C, 0x6F10,
	0x9D5D, 0x9D66, 0x6F16,
	0x9D67, 0x9D69, 0x6F21,
	0x9D6A, 0x9D6D, 0x6F25,
	0x9D72, 0x9D73, 0x6F34,
	0x9D74, 0x9D7A, 0x6F37,
	0x9D7B, 0x9D7E, 0x6F3F,
	0x9D80, 0x9D82, 0x6F43,
	0x9D83, 0x9D85, 0x6F48,
	0x9D87, 0x9D90, 0x6F4E,
	0x9D91, 0x9D93, 0x6F59,
	0x9D95, 0x9D97, 0x6F5F,
	0x9D98, 0x9D9A, 0x6F63,
	0x9D9B, 0x9DA0, 0x6F67,
	0x9DA1, 0x9DA3, 0x6F6F,
	0x9DA5, 0x9DA7, 0x6F75,
	0x9DAA, 0x9DB0, 0x6F7D,
	0x9DB1, 0x9DB3, 0x6F85,
	0x9DB4, 0x9DB5, 0x6F8A,
	0x9DB6, 0x9DC2, 0x6F8F,
	0x9DC3, 0x9DC6, 0x6F9D,
	0x9DC7, 0x9DCB, 0x6FA2,
	0x9DCC, 0x9DD6, 0x6FA8,
	0x9DD7, 0x9DD8, 0x6FB4,
	0x9DD9, 0x9DDA, 0x6FB7,
	0x9DDB, 0x9DE0, 0x6FBA,
	0x9DE2, 0x9DE7, 0x6FC3,
	0x9DE8, 0x9DEE, 0x6FCA,
	0x9DEF, 0x9DF9, 0x6FD3,
	0x9DFB, 0x9DFE, 0x6FE2,
	0x9E40, 0x9E47, 0x6FE6,
	0x9E48, 0x9E68, 0x6FF0,
	0x9E69, 0x9E70, 0x7012,
	0x9E71, 0x9E77, 0x701C,
	0x9E78, 0x9E7E, 0x7024,
	0x9E80, 0x9E89, 0x702B,
	0x9E8A, 0x9E8C, 0x7036,
	0x9E8D, 0x9E9E, 0x703A,
	0x9E9F, 0x9EA0, 0x704D,
	0x9EA1, 0x9EAE, 0x7050,
	0x9EAF, 0x9EBA, 0x705F,
	0x9EBC, 0x9EBF, 0x7071,
	0x9EC1, 0x9EC3, 0x7079,
	0x9EC5, 0x9EC8, 0x7081,
	0x9EC9, 0x9ECB, 0x7086,
	0x9ECC, 0x9ECE, 0x708B,
	0x9ECF, 0x9ED1, 0x708F,
	0x9ED3, 0x9ED4, 0x7097,
	0x9ED5, 0x9ED6, 0x709A,
	0x9ED7, 0x9EE3, 0x709E,
	0x9EE6, 0x9EE8, 0x70B4,
	0x9EEA, 0x9EEB, 0x70BE,
	0x9EEC, 0x9EEF, 0x70C4,
	0x9EF1, 0x9EFD, 0x70CB,
	0x9F40, 0x9F42, 0x70DC,
	0x9F43, 0x9F46, 0x70E0,
	0x9F4A, 0x9F50, 0x70F0,
	0x9F52, 0x9F54, 0x70FA,
	0x9F55, 0x9F5F, 0x70FE,
	0x9F60, 0x9F64, 0x710B,
	0x9F65, 0x9F66, 0x7111,
	0x9F69, 0x9F73, 0x711B,
	0x9F74, 0x9F7B, 0x7127,
	0x9F7C, 0x9F7E, 0x7132,
	0x9F81, 0x9F8E, 0x7137,
	0x9F8F, 0x9F92, 0x7146,
	0x9F95, 0x9FA1, 0x714F,
	0x9FA3, 0x9FA7, 0x715F,
	0x9FA9, 0x9FAD, 0x7169,
	0x9FAE, 0x9FB0, 0x716F,
	0x9FB1, 0x9FB4, 0x7174,
	0x9FB6, 0x9FB7, 0x717B,
	0x9FB8, 0x9FBD, 0x717E,
	0x9FBE, 0x9FC2, 0x7185,
	0x9FC3, 0x9FC6, 0x718B,
	0x9FC7, 0x9FCA, 0x7190,
	0x9FCB, 0x9FCD, 0x7195,
	0x9FCE, 0x9FD2, 0x719A,
	0x9FD3, 0x9FD9, 0x71A1,
	0x9FDA, 0x9FDC, 0x71A9,
	0x9FDD, 0x9FE2, 0x71AD,
	0x9FE4, 0x9FE6, 0x71B6,
	0x9FE7, 0x9FEF, 0x71BA,
	0x9FF0, 0x9FF9, 0x71C4,
	0x9FFA, 0x9FFE, 0x71CF,
	0xA040, 0xA049, 0x71D6,
	0xA04A, 0xA04D, 0x71E1,
	0xA04F, 0xA054, 0x71E8,
	0xA055, 0xA05E, 0x71EF,
	0xA05F, 0xA06A, 0x71FA,
	0xA06B, 0xA07E, 0x7207,
	0xA080, 0xA081, 0x721B,
	0xA082, 0xA08B, 0x721E,
	0xA08E, 0xA090, 0x722D,
	0xA091, 0xA093, 0x7232,
	0xA097, 0xA09D, 0x7240,
	0xA09E, 0xA0A0, 0x7249,
	0xA0A1, 0xA0A4, 0x724E,
	0xA0A5, 0xA0A7, 0x7253,
	0xA0A8, 0xA0A9, 0x7257,
	0xA0AE, 0xA0B0, 0x7263,
	0xA0B2, 0xA0B5, 0x726A,
	0xA0B6, 0xA0B7, 0x7270,
	0xA0B8, 0xA0B9, 0x7273,
	0xA0BA, 0xA0BC, 0x7276,
	0xA0BD, 0xA0BF, 0x727B,
	0xA0C0, 0xA0C1, 0x7282,
	0xA0C2, 0xA0C6, 0x7285,
	0xA0C9, 0xA0CA, 0x7290,
	0xA0CB, 0xA0D6, 0x7293,
	0xA0D7, 0xA0E2, 0x72A0,
	0xA0E4, 0xA0E6, 0x72B1,
	0xA0E8, 0xA0EE, 0x72BA,
	0xA0EF, 0xA0F1, 0x72C5,
	0xA0F2, 0xA0F5, 0x72C9,
	0xA0F8, 0xA0FB, 0x72D3,
	0xA0FD, 0xA0FE, 0x72DA,
	0xA1A1, 0xA1A3, 0x3000,
	0xA1AE, 0xA1AF, 0x2018,
	0xA1B0, 0xA1B1, 0x201C,
	0xA1B2, 0xA1B3, 0x3014,
	0xA1B4, 0xA1BB, 0x3008,
	0xA1BC, 0xA1BD, 0x3016,
	0xA1BE, 0xA1BF, 0x3010,
	0xA1C4, 0xA1C5, 0x2227,
	0xA1DA, 0xA1DB, 0x226E,
	0xA1DC, 0xA1DD, 0x2264,
	0xA1E4, 0xA1E5, 0x2032,
	0xA1E9, 0xA1EA, 0xFFE0,
	0xA1FB, 0xA1FC, 0x2190,
	0xA2A1, 0xA2AA, 0x2170,
	0xA2B1, 0xA2C4, 0x2488,
	0xA2C5, 0xA2D8, 0x2474,
	0xA2D9, 0xA2E2, 0x2460,
	0xA2E5, 0xA2EE, 0x3220,
	0xA2F1, 0xA2FC, 0x2160,
	0xA3A1, 0xA3A3, 0xFF01,
	0xA3A5, 0xA3FD, 0xFF05,
	0xA4A1, 0xA4F3, 0x3041,
	0xA5A1, 0xA5F6, 0x30A1,
	0xA6A1, 0xA6B1, 0x0391,
	0xA6B2, 0xA6B8, 0x03A3,
	0xA6C1, 0xA6D1, 0x03B1,
	0xA6D2, 0xA6D8, 0x03C3,
	0xA6E0, 0xA6E1, 0xFE35,
	0xA6E2, 0xA6E3, 0xFE39,
	0xA6E4, 0xA6E5, 0xFE3F,
	0xA6E6, 0xA6E7, 0xFE3D,
	0xA6E8, 0xA6EB, 0xFE41,
	0xA6EE, 0xA6EF, 0xFE3B,
	0xA6F0, 0xA6F1, 0xFE37,
	0xA6F4, 0xA6F5, 0xFE33,
	0xA7A1, 0xA7A6, 0x0410,
	0xA7A8, 0xA7C1, 0x0416,
	0xA7D1, 0xA7D6, 0x0430,
	0xA7D8, 0xA7F1, 0x0436,
	0xA840, 0xA841, 0x02CA,
	0xA849, 0xA84C, 0x2196,
	0xA851, 0xA852, 0x2266,
	0xA854, 0xA877, 0x2550,
	0xA878, 0xA87E, 0x2581,
	0xA880, 0xA887, 0x2588,
	0xA888, 0xA88A, 0x2593,
	0xA88B, 0xA88C, 0x25BC,
	0xA88D, 0xA890, 0x25E2,
	0xA894, 0xA895, 0x301D,
	0xA8C5, 0xA8E9, 0x3105,
	0xA940, 0xA948, 0x3021,
	0xA94A, 0xA94B, 0x338E,
	0xA94C, 0xA94E, 0x339C,
	0xA952, 0xA953, 0x33D1,
	0xA961, 0xA962, 0x309B,
	0xA963, 0xA964, 0x30FD,
	0xA966, 0xA967, 0x309D,
	0xA968, 0xA971, 0xFE49,
	0xA972, 0xA975, 0xFE54,
	0xA976, 0xA97E, 0xFE59,
	0xA980, 0xA984, 0xFE62,
	0xA985, 0xA988, 0xFE68,
	0xA9A4, 0xA9EF, 0x2500,
	0xAA40, 0xAA41, 0x72DC,
	0xAA43, 0xAA48, 0x72E2,
	0xAA49, 0xAA4A, 0x72EA,
	0xAA4B, 0xAA4C, 0x72F5,
	0xAA4E, 0xAA51, 0x72FD,
	0xAA53, 0xAA58, 0x7304,
	0xAA59, 0xAA5B, 0x730B,
	0xAA5C, 0xAA5F, 0x730F,
	0xAA61, 0xAA63, 0x7318,
	0xAA64, 0xAA65, 0x731F,
	0xAA66, 0xAA67, 0x7323,
	0xAA68, 0xAA6A, 0x7326,
	0xAA6C, 0xAA6D, 0x732F,
	0xAA6E, 0xAA6F, 0x7332,
	0xAA70, 0xAA71, 0x7335,
	0xAA72, 0xAA75, 0x733A,
	0xAA76, 0xAA7E, 0x7340,
	0xAA80, 0xAA83, 0x7349,
	0xAA84, 0xAA85, 0x734E,
	0xAA87, 0xAA8A, 0x7353,
	0xAA8B, 0xAA92, 0x7358,
	0xAA93, 0xAA9D, 0x7361,
	0xAA9F, 0xAAA0, 0x7370,
	0xAB40, 0xAB4B, 0x7372,
	0xAB4C, 0xAB50, 0x737F,
	0xAB51, 0xAB52, 0x7385,
	0xAB55, 0xAB56, 0x738C,
	0xAB57, 0xAB58, 0x738F,
	0xAB59, 0xAB5C, 0x7392,
	0xAB5D, 0xAB60, 0x7397,
	0xAB61, 0xAB63, 0x739C,
	0xAB64, 0xAB65, 0x73A0,
	0xAB66, 0xAB6B, 0x73A3,
	0xAB6D, 0xAB6E, 0x73AC,
	0xAB70, 0xAB72, 0x73B4,
	0xAB73, 0xAB74, 0x73B8,
	0xAB75, 0xAB78, 0x73BC,
	0xAB7A, 0xAB7E, 0x73C3,
	0xAB80, 0xAB81, 0x73CB,
	0xAB83, 0xAB89, 0x73D2,
	0xAB8A, 0xAB8D, 0x73DA,
	0xAB8F, 0xAB92, 0x73E1,
	0xAB95, 0xAB97, 0x73EA,
	0xAB98, 0xAB9B, 0x73EE,
	0xAB9C, 0xABA0, 0x73F3,
	0xAC40, 0xAC4A, 0x73F8,
	0xAC4C, 0xAC4D, 0x7407,
	0xAC4E, 0xAC51, 0x740B,
	0xAC52, 0xAC5A, 0x7411,
	0xAC5B, 0xAC60, 0x741C,
	0xAC61, 0xAC62, 0x7423,
	0xAC68, 0xAC69, 0x7431,
	0xAC6A, 0xAC6E, 0x7437,
	0xAC6F, 0xAC72, 0x743D,
	0xAC73, 0xAC7E, 0x7442,
	0xAC80, 0xAC86, 0x744E,
	0xAC8A, 0xAC96, 0x7460,
	0xAC97, 0xAC98, 0x746E,
	0xAC99, 0xAC9D, 0x7471,
	0xAC9E, 0xACA0, 0x7478,
	0xAD40, 0xAD42, 0x747B,
	0xAD45, 0xAD47, 0x7484,
	0xAD48, 0xAD4A, 0x7488,
	0xAD4B, 0xAD4C, 0x748C,
	0xAD4E, 0xAD58, 0x7491,
	0xAD5A, 0xAD61, 0x749F,
	0xAD62, 0xAD71, 0x74AA,
	0xAD72, 0xAD7E, 0x74BB,
	0xAD80, 0xAD89, 0x74C8,
	0xAD8A, 0xAD92, 0x74D3,
	0xAD97, 0xAD9D, 0x74E7,
	0xAD9E, 0xADA0, 0x74F0,
	0xAE42, 0xAE48, 0x74F8,
	0xAE49, 0xAE4C, 0x7500,
	0xAE4D, 0xAE54, 0x7505,
	0xAE58, 0xAE5B, 0x7514,
	0xAE5D, 0xAE5E, 0x751D,
	0xAE5F, 0xAE63, 0x7520,
	0xAE64, 0xAE65, 0x7526,
	0xAE6B, 0xAE6C, 0x753C,
	0xAE6E, 0xAE71, 0x7541,
	0xAE72, 0xAE73, 0x7546,
	0xAE74, 0xAE75, 0x7549,
	0xAE77, 0xAE7A, 0x7550,
	0xAE7B, 0xAE7E, 0x7555,
	0xAE80, 0xAE87, 0x755D,
	0xAE88, 0xAE8A, 0x7567,
	0xAE8B, 0xAE91, 0x756B,
	0xAE93, 0xAE95, 0x7575,
	0xAE96, 0xAE9A, 0x757A,
	0xAE9B, 0xAE9D, 0x7580,
	0xAE9E, 0xAE9F, 0x7584,
	0xAF40, 0xAF42, 0x7588,
	0xAF43, 0xAF45, 0x758C,
	0xAF4A, 0xAF4B, 0x759B,
	0xAF4E, 0xAF52, 0x75A6,
	0xAF54, 0xAF55, 0x75B6,
	0xAF56, 0xAF57, 0x75BA,
	0xAF58, 0xAF5A, 0x75BF,
	0xAF5C, 0xAF5D, 0x75CB,
	0xAF5E, 0xAF61, 0x75CE,
	0xAF64, 0xAF65, 0x75D9,
	0xAF66, 0xAF67, 0x75DC,
	0xAF68, 0xAF6A, 0x75DF,
	0xAF6D, 0xAF70, 0x75EC,
	0xAF71, 0xAF72, 0x75F2,
	0xAF73, 0xAF76, 0x75F5,
	0xAF77, 0xAF78, 0x75FA,
	0xAF79, 0xAF7A, 0x75FD,
	0xAF7D, 0xAF7E, 0x7606,
	0xAF80, 0xAF81, 0x7608,
	0xAF83, 0xAF85, 0x760D,
	0xAF86, 0xAF89, 0x7611,
	0xAF8C, 0xAF8E, 0x761C,
	0xAF91, 0xAF92, 0x7627,
	0xAF94, 0xAF95, 0x762E,
	0xAF96, 0xAF97, 0x7631,
	0xAF98, 0xAF99, 0x7636,
	0xAF9A, 0xAF9C, 0x7639,
	0xAF9E, 0xAF9F, 0x7641,
	0xB040, 0xB046, 0x7645,
	0xB047, 0xB04C, 0x764E,
	0xB04E, 0xB052, 0x7657,
	0xB054, 0xB057, 0x765F,
	0xB058, 0xB05E, 0x7664,
	0xB05F, 0xB061, 0x766C,
	0xB062, 0xB069, 0x7670,
	0xB06A, 0xB06B, 0x7679,
	0xB06D, 0xB06F, 0x767F,
	0xB072, 0xB073, 0x7689,
	0xB074, 0xB075, 0x768C,
	0xB076, 0xB077, 0x768F,
	0xB079, 0xB07A, 0x7694,
	0xB07B, 0xB07C, 0x7697,
	0xB07D, 0xB07E, 0x769A,
	0xB080, 0xB087, 0x769C,
	0xB088, 0xB090, 0x76A5,
	0xB091, 0xB092, 0x76AF,
	0xB094, 0xB09D, 0x76B5,
	0xB09E, 0xB09F, 0x76C0,
	0xB143, 0xB144, 0x76CB,
	0xB147, 0xB148, 0x76D9,
	0xB149, 0xB14B, 0x76DC,
	0xB14C, 0xB150, 0x76E0,
	0xB151, 0xB158, 0x76E6,
	0xB15B, 0xB15D, 0x76F5,
	0xB15E, 0xB15F, 0x76FA,
	0xB161, 0xB162, 0x76FF,
	0xB163, 0xB164, 0x7702,
	0xB165, 0xB166, 0x7705,
	0xB169, 0xB173, 0x770E,
	0xB174, 0xB177, 0x771B,
	0xB179, 0xB17B, 0x7723,
	0xB17D, 0xB17E, 0x772A,
	0xB182, 0xB186, 0x7730,
	0xB189, 0xB18B, 0x773D,
	0xB18D, 0xB18F, 0x7744,
	0xB190, 0xB197, 0x7748,
	0xB198, 0xB19F, 0x7752,
	0xB1E6, 0xB1E7, 0x8FA8,
	0xB240, 0xB243, 0x775D,
	0xB246, 0xB247, 0x7769,
	0xB248, 0xB253, 0x776D,
	0xB254, 0xB256, 0x777A,
	0xB257, 0xB259, 0x7781,
	0xB25A, 0xB25F, 0x7786,
	0xB260, 0xB261, 0x778F,
	0xB262, 0xB26D, 0x7793,
	0xB26F, 0xB270, 0x77A3,
	0xB274, 0xB276, 0x77AD,
	0xB277, 0xB278, 0x77B1,
	0xB27A, 0xB27E, 0x77B6,
	0xB282, 0xB28E, 0x77C0,
	0xB28F, 0xB297, 0x77CE,
	0xB298, 0xB29A, 0x77D8,
	0xB29B, 0xB29F, 0x77DD,
	0xB343, 0xB346, 0x77EF,
	0xB347, 0xB348, 0x77F4,
	0xB34A, 0xB34D, 0x77F9,
	0xB34E, 0xB353, 0x7803,
	0xB354, 0xB355, 0x780A,
	0xB356, 0xB358, 0x780E,
	0xB35E, 0xB360, 0x7820,
	0xB363, 0xB364, 0x782A,
	0xB365, 0xB366, 0x782E,
	0xB367, 0xB369, 0x7831,
	0xB36A, 0xB36B, 0x7835,
	0xB36E, 0xB371, 0x7841,
	0xB373, 0xB376, 0x7848,
	0xB37A, 0xB37B, 0x7853,
	0xB37C, 0xB37E, 0x7858,
	0xB380, 0xB381, 0x785B,
	0xB382, 0xB38D, 0x785E,
	0xB38E, 0xB395, 0x786F,
	0xB396, 0xB399, 0x7878,
	0xB39A, 0xB3A0, 0x787D,
	0xB440, 0xB442, 0x7884,
	0xB444, 0xB445, 0x788A,
	0xB446, 0xB447, 0x788F,
	0xB449, 0xB44B, 0x7894,
	0xB44D, 0xB44E, 0x789D,
	0xB453, 0xB45A, 0x78A8,
	0xB45B, 0xB45E, 0x78B5,
	0xB45F, 0xB462, 0x78BA,
	0xB463, 0xB464, 0x78BF,
	0xB465, 0xB467, 0x78C2,
	0xB468, 0xB46A, 0x78C6,
	0xB46B, 0xB46E, 0x78CC,
	0xB46F, 0xB471, 0x78D1,
	0xB472, 0xB474, 0x78D6,
	0xB475, 0xB47E, 0x78DA,
	0xB480, 0xB483, 0x78E4,
	0xB484, 0xB486, 0x78E9,
	0xB487, 0xB48B, 0x78ED,
	0xB48D, 0xB48E, 0x78F5,
	0xB48F, 0xB490, 0x78F8,
	0xB491, 0xB496, 0x78FB,
	0xB497, 0xB499, 0x7902,
	0xB49A, 0xB4A0, 0x7906,
	0xB540, 0xB545, 0x790D,
	0xB546, 0xB54F, 0x7914,
	0xB550, 0xB554, 0x791F,
	0xB555, 0xB563, 0x7925,
	0xB564, 0xB568, 0x7935,
	0xB56B, 0xB56E, 0x7942,
	0xB570, 0xB578, 0x794A,
	0xB579, 0xB57A, 0x7954,
	0xB57B, 0xB57C, 0x7958,
	0xB582, 0xB585, 0x7969,
	0xB587, 0xB58D, 0x7970,
	0xB58F, 0xB593, 0x797B,
	0xB594, 0xB595, 0x7982,
	0xB596, 0xB599, 0x7986,
	0xB59A, 0xB59D, 0x798B,
	0xB59E, 0xB5A0, 0x7990,
	0xB640, 0xB646, 0x7993,
	0xB647, 0xB652, 0x799B,
	0xB653, 0xB65D, 0x79A8,
	0xB65E, 0xB662, 0x79B4,
	0xB666, 0xB667, 0x79C4,
	0xB668, 0xB669, 0x79C7,
	0xB66C, 0xB66E, 0x79CE,
	0xB66F, 0xB670, 0x79D3,
	0xB671, 0xB672, 0x79D6,
	0xB673, 0xB678, 0x79D9,
	0xB679, 0xB67B, 0x79E0,
	0xB682, 0xB688, 0x79F1,
	0xB689, 0xB68A, 0x79F9,
	0xB68C, 0xB68D, 0x79FE,
	0xB68F, 0xB690, 0x7A04,
	0xB691, 0xB694, 0x7A07,
	0xB696, 0xB69A, 0x7A0F,
	0xB69B, 0xB69C, 0x7A15,
	0xB69D, 0xB69E, 0x7A18,
	0xB69F, 0xB6A0, 0x7A1B,
	0xB742, 0xB743, 0x7A21,
	0xB744, 0xB752, 0x7A24,
	0xB753, 0xB755, 0x7A34,
	0xB759, 0xB75E, 0x7A40,
	0xB75F, 0xB768, 0x7A47,
	0xB769, 0xB76D, 0x7A52,
	0xB76E, 0xB77E, 0x7A58,
	0xB780, 0xB786, 0x7A69,
	0xB787, 0xB789, 0x7A71,
	0xB78B, 0xB78E, 0x7A7B,
	0xB792, 0xB795, 0x7A89,
	0xB796, 0xB798, 0x7A8E,
	0xB799, 0xB79A, 0x7A93,
	0xB79B, 0xB79D, 0x7A99,
	0xB79F, 0xB7A0, 0x7AA1,
	0xB840, 0xB841, 0x7AA3,
	0xB843, 0xB845, 0x7AA9,
	0xB846, 0xB84A, 0x7AAE,
	0xB84B, 0xB855, 0x7AB4,
	0xB856, 0xB860, 0x7AC0,
	0xB861, 0xB86A, 0x7ACC,
	0xB86B, 0xB86C, 0x7AD7,
	0xB86D, 0xB870, 0x7ADA,
	0xB871, 0xB872, 0x7AE1,
	0xB874, 0xB879, 0x7AE7,
	0xB87B, 0xB87E, 0x7AF0,
	0xB880, 0xB884, 0x7AF4,
	0xB885, 0xB886, 0x7AFB,
	0xB888, 0xB88A, 0x7B00,
	0xB88E, 0xB890, 0x7B0C,
	0xB892, 0xB893, 0x7B12,
	0xB894, 0xB896, 0x7B16,
	0xB898, 0xB899, 0x7B1C,
	0xB89B, 0xB89D, 0x7B21,
	0xB940, 0xB941, 0x7B2F,
	0xB943, 0xB946, 0x7B34,
	0xB94A, 0xB94F, 0x7B3F,
	0xB953, 0xB954, 0x7B4D,
	0xB95A, 0xB95B, 0x7B5E,
	0xB95D, 0xB967, 0x7B63,
	0xB968, 0xB969, 0x7B6F,
	0xB96A, 0xB96B, 0x7B73,
	0xB96F, 0xB970, 0x7B7C,
	0xB972, 0xB975, 0x7B81,
	0xB976, 0xB97C, 0x7B86,
	0xB97D, 0xB97E, 0x7B8E,
	0xB980, 0xB982, 0x7B91,
	0xB984, 0xB987, 0x7B98,
	0xB988, 0xB98A, 0x7B9E,
	0xB98B, 0xB98D, 0x7BA3,
	0xB98E, 0xB990, 0x7BAE,
	0xB991, 0xB992, 0x7BB2,
	0xB993, 0xB995, 0x7BB5,
	0xB996, 0xB99D, 0x7BB9,
	0xB99E, 0xB9A0, 0x7BC2,
	0xBA41, 0xBA44, 0x7BC8,
	0xBA45, 0xBA48, 0x7BCD,
	0xBA4A, 0xBA4E, 0x7BD4,
	0xBA4F, 0xBA50, 0x7BDB,
	0xBA51, 0xBA53, 0x7BDE,
	0xBA54, 0xBA56, 0x7BE2,
	0xBA57, 0xBA59, 0x7BE7,
	0xBA5A, 0xBA5C, 0x7BEB,
	0xBA5D, 0xBA5E, 0x7BEF,
	0xBA5F, 0xBA63, 0x7BF2,
	0xBA64, 0xBA67, 0x7BF8,
	0xBA69, 0xBA70, 0x7BFF,
	0xBA71, 0xBA73, 0x7C08,
	0xBA74, 0xBA75, 0x7C0D,
	0xBA76, 0xBA7B, 0x7C10,
	0xBA7C, 0xBA7E, 0x7C17,
	0xBA80, 0xBA84, 0x7C1A,
	0xBA85, 0xBA8A, 0x7C20,
	0xBA8B, 0xBA8C, 0x7C28,
	0xBA8D, 0xBA99, 0x7C2B,
	0xBA9A, 0xBA9F, 0x7C39,
	0xBB40, 0xBB49, 0x7C43,
	0xBB4A, 0xBB6E, 0x7C4E,
	0xBB6F, 0xBB74, 0x7C75,
	0xBB75, 0xBB7E, 0x7C7E,
	0xBB81, 0xBB87, 0x7C8A,
	0xBB88, 0xBB89, 0x7C93,
	0xBB8B, 0xBB8D, 0x7C99,
	0xBB8E, 0xBB8F, 0x7CA0,
	0xBB91, 0xBB94, 0x7CA6,
	0xBB95, 0xBB97, 0x7CAB,
	0xBB98, 0xBB99, 0x7CAF,
	0xBB9A, 0xBB9E, 0x7CB4,
	0xBB9F, 0xBBA0, 0x7CBA,
	0xBC40, 0xBC41, 0x7CBF,
	0xBC42, 0xBC44, 0x7CC2,
	0xBC48, 0xBC4E, 0x7CCE,
	0xBC50, 0xBC51, 0x7CDA,
	0xBC52, 0xBC53, 0x7CDD,
	0xBC54, 0xBC5A, 0x7CE1,
	0xBC5B, 0xBC60, 0x7CE9,
	0xBC61, 0xBC68, 0x7CF0,
	0xBC69, 0xBC6A, 0x7CF9,
	0xBC6B, 0xBC78, 0x7CFC,
	0xBC79, 0xBC7E, 0x7D0B,
	0xBC80, 0xBC8E, 0x7D11,
	0xBC90, 0xBC93, 0x7D23,
	0xBC94, 0xBC96, 0x7D28,
	0xBC97, 0xBC99, 0x7D2C,
	0xBC9A, 0xBCA0, 0x7D30,
	0xBD40, 0xBD76, 0x7D37,
	0xBD77, 0xBD7E, 0x7D6F,
	0xBD80, 0xBDA0, 0x7D78,
	0xBE40, 0xBE4C, 0x7D99,
	0xBE4D, 0xBE53, 0x7DA7,
	0xBE54, 0xBE7E, 0x7DAF,
	0xBE80, 0xBEA0, 0x7DDA,
	0xBF40, 0xBF7E, 0x7DFB,
	0xBF81, 0xBF85, 0x7E3C,
	0xBF86, 0xBF8A, 0x7E42,
	0xBF8B, 0xBFA0, 0x7E48,
	0xC040, 0xC063, 0x7E5E,
	0xC064, 0xC07B, 0x7E83,
	0xC07C, 0xC07E, 0x7E9C,
	0xC082, 0xC083, 0x7EBB,
	0xC08D, 0xC093, 0x7F3B,
	0xC095, 0xC09E, 0x7F46,
	0xC09F, 0xC0A0, 0x7F52,
	0xC142, 0xC145, 0x7F5B,
	0xC147, 0xC14B, 0x7F63,
	0xC14C, 0xC14E, 0x7F6B,
	0xC14F, 0xC150, 0x7F6F,
	0xC152, 0xC155, 0x7F75,
	0xC156, 0xC159, 0x7F7A,
	0xC15A, 0xC15B, 0x7F7F,
	0xC15C, 0xC163, 0x7F82,
	0xC166, 0xC16A, 0x7F8F,
	0xC16B, 0xC16F, 0x7F95,
	0xC170, 0xC171, 0x7F9B,
	0xC173, 0xC174, 0x7FA2,
	0xC175, 0xC176, 0x7FA5,
	0xC177, 0xC17D, 0x7FA8,
	0xC180, 0xC184, 0x7FB3,
	0xC185, 0xC186, 0x7FBA,
	0xC189, 0xC18B, 0x7FC2,
	0xC18C, 0xC18F, 0x7FC6,
	0xC192, 0xC196, 0x7FCF,
	0xC197, 0xC198, 0x7FD6,
	0xC199, 0xC19E, 0x7FD9,
	0xC19F, 0xC1A0, 0x7FE2,
	0xC241, 0xC242, 0x7FE7,
	0xC243, 0xC246, 0x7FEA,
	0xC249, 0xC24F, 0x7FF4,
	0xC250, 0xC252, 0x7FFD,
	0xC254, 0xC257, 0x8007,
	0xC258, 0xC259, 0x800E,
	0xC25C, 0xC25D, 0x801A,
	0xC25E, 0xC260, 0x801D,
	0xC262, 0xC263, 0x8023,
	0xC264, 0xC269, 0x802B,
	0xC26C, 0xC26D, 0x8039,
	0xC270, 0xC271, 0x8040,
	0xC272, 0xC273, 0x8044,
	0xC274, 0xC276, 0x8047,
	0xC277, 0xC27A, 0x804E,
	0xC27C, 0xC27E, 0x8055,
	0xC281, 0xC28E, 0x805B,
	0xC28F, 0xC294, 0x806B,
	0xC295, 0xC2A0, 0x8072,
	0xC341, 0xC342, 0x8081,
	0xC346, 0xC34B, 0x808D,
	0xC34C, 0xC34D, 0x8094,
	0xC352, 0xC354, 0x80A6,
	0xC358, 0xC359, 0x80B5,
	0xC35A, 0xC35B, 0x80B8,
	0xC35E, 0xC362, 0x80C7,
	0xC363, 0xC369, 0x80CF,
	0xC36B, 0xC36C, 0x80DF,
	0xC36D, 0xC36E, 0x80E2,
	0xC375, 0xC378, 0x80FE,
	0xC379, 0xC37B, 0x8103,
	0xC37C, 0xC37D, 0x8107,
	0xC384, 0xC386, 0x811B,
	0xC387, 0xC393, 0x811F,
	0xC394, 0xC395, 0x812D,
	0xC397, 0xC399, 0x8133,
	0xC39B, 0xC39F, 0x8139,
	0xC440, 0xC445, 0x8140,
	0xC448, 0xC44A, 0x814D,
	0xC44C, 0xC44E, 0x8156,
	0xC44F, 0xC453, 0x815B,
	0xC454, 0xC457, 0x8161,
	0xC45A, 0xC45C, 0x816A,
	0xC45E, 0xC45F, 0x8172,
	0xC460, 0xC463, 0x8175,
	0xC465, 0xC469, 0x8183,
	0xC46B, 0xC46E, 0x818B,
	0xC470, 0xC475, 0x8192,
	0xC476, 0xC477, 0x8199,
	0xC478, 0xC47C, 0x819E,
	0xC47D, 0xC47E, 0x81A4,
	0xC482, 0xC489, 0x81AB,
	0xC48A, 0xC48F, 0x81B4,
	0xC490, 0xC493, 0x81BC,
	0xC494, 0xC495, 0x81C4,
	0xC496, 0xC498, 0x81C7,
	0xC49A, 0xC4A0, 0x81CD,
	0xC540, 0xC54E, 0x81D4,
	0xC54F, 0xC551, 0x81E4,
	0xC552, 0xC553, 0x81E8,
	0xC555, 0xC559, 0x81EE,
	0xC55A, 0xC55F, 0x81F5,
	0xC563, 0xC567, 0x8207,
	0xC568, 0xC569, 0x820E,
	0xC56C, 0xC571, 0x8215,
	0xC574, 0xC577, 0x8224,
	0xC57C, 0xC57D, 0x823C,
	0xC580, 0xC583, 0x8240,
	0xC584, 0xC585, 0x8245,
	0xC588, 0xC58A, 0x824C,
	0xC58B, 0xC592, 0x8250,
	0xC594, 0xC597, 0x825B,
	0xC598, 0xC59F, 0x8260,
	0xC640, 0xC643, 0x826A,
	0xC645, 0xC648, 0x8275,
	0xC649, 0xC64A, 0x827B,
	0xC64B, 0xC64C, 0x8280,
	0xC64E, 0xC650, 0x8285,
	0xC654, 0xC657, 0x8293,
	0xC658, 0xC659, 0x829A,
	0xC65C, 0xC65D, 0x82A2,
	0xC660, 0xC661, 0x82B5,
	0xC662, 0xC664, 0x82BA,
	0xC665, 0xC666, 0x82BF,
	0xC667, 0xC668, 0x82C2,
	0xC669, 0xC66A, 0x82C5,
	0xC66E, 0xC66F, 0x82D9,
	0xC672, 0xC675, 0x82E7,
	0xC676, 0xC678, 0x82EC,
	0xC67A, 0xC67B, 0x82F2,
	0xC67C, 0xC67D, 0x82F5,
	0xC681, 0xC685, 0x82FC,
	0xC686, 0xC687, 0x830A,
	0xC68A, 0xC68B, 0x8312,
	0xC68D, 0xC68E, 0x8318,
	0xC68F, 0xC698, 0x831D,
	0xC699, 0xC69A, 0x8329,
	0xC740, 0xC741, 0x833E,
	0xC742, 0xC743, 0x8341,
	0xC744, 0xC745, 0x8344,
	0xC747, 0xC74B, 0x834A,
	0xC74D, 0xC751, 0x8355,
	0xC754, 0xC75A, 0x8370,
	0xC75B, 0xC75C, 0x8379,
	0xC75D, 0xC763, 0x837E,
	0xC764, 0xC765, 0x8387,
	0xC766, 0xC769, 0x838A,
	0xC76A, 0xC76C, 0x838F,
	0xC76D, 0xC770, 0x8394,
	0xC771, 0xC772, 0x8399,
	0xC775, 0xC77B, 0x83A1,
	0xC77C, 0xC77E, 0x83AC,
	0xC783, 0xC784, 0x83BE,
	0xC785, 0xC787, 0x83C2,
	0xC789, 0xC78A, 0x83C8,
	0xC78C, 0xC78D, 0x83CD,
	0xC78E, 0xC791, 0x83D0,
	0xC794, 0xC796, 0x83D9,
	0xC798, 0xC79A, 0x83E2,
	0xC79B, 0xC79D, 0x83E6,
	0xC79E, 0xC7A0, 0x83EB,
	0xC840, 0xC841, 0x83EE,
	0xC842, 0xC846, 0x83F3,
	0xC847, 0xC849, 0x83FA,
	0xC84A, 0xC84C, 0x83FE,
	0xC84F, 0xC852, 0x8407,
	0xC854, 0xC859, 0x8412,
	0xC85A, 0xC85C, 0x8419,
	0xC85D, 0xC862, 0x841E,
	0xC863, 0xC86A, 0x8429,
	0xC86B, 0xC870, 0x8432,
	0xC871, 0xC873, 0x8439,
	0xC874, 0xC87B, 0x843E,
	0xC87C, 0xC87E, 0x8447,
	0xC880, 0xC886, 0x844A,
	0xC887, 0xC88B, 0x8452,
	0xC88D, 0xC890, 0x845D,
	0xC892, 0xC896, 0x8464,
	0xC898, 0xC89A, 0x846E,
	0xC89F, 0xC8A0, 0x847B,
	0xC940, 0xC944, 0x847D,
	0xC945, 0xC948, 0x8483,
	0xC94B, 0xC952, 0x848F,
	0xC954, 0xC955, 0x849A,
	0xC956, 0xC959, 0x849D,
	0xC95A, 0xC966, 0x84A2,
	0xC967, 0xC968, 0x84B0,
	0xC96A, 0xC96C, 0x84B5,
	0xC96D, 0xC96E, 0x84BB,
	0xC971, 0xC972, 0x84C2,
	0xC973, 0xC976, 0x84C5,
	0xC977, 0xC978, 0x84CB,
	0xC979, 0xC97A, 0x84CE,
	0xC97C, 0xC97D, 0x84D4,
	0xC980, 0xC984, 0x84D8,
	0xC986, 0xC987, 0x84E1,
	0xC989, 0xC98D, 0x84E7,
	0xC98E, 0xC990, 0x84ED,
	0xC991, 0xC99B, 0x84F1,
	0xC99C, 0xC99D, 0x84FD,
	0xC99E, 0xC9A0, 0x8500,
	0xC9E0, 0xC9E1, 0x820C,
	0xCA40, 0xCA48, 0x8503,
	0xCA49, 0xCA4C, 0x850D,
	0xCA4E, 0xCA50, 0x8514,
	0xCA51, 0xCA52, 0x8518,
	0xCA53, 0xCA56, 0x851B,
	0xCA58, 0xCA60, 0x8522,
	0xCA61, 0xCA6A, 0x852D,
	0xCA6B, 0xCA6F, 0x853E,
	0xCA70, 0xCA73, 0x8544,
	0xCA74, 0xCA7E, 0x854B,
	0xCA80, 0xCA81, 0x8557,
	0xCA82, 0xCA85, 0x855A,
	0xCA86, 0xCA8A, 0x855F,
	0xCA8B, 0xCA8D, 0x8565,
	0xCA8E, 0xCA96, 0x8569,
	0xCA98, 0xCA9B, 0x8575,
	0xCA9C, 0xCA9D, 0x857C,
	0xCA9E, 0xCAA0, 0x857F,
	0xCB40, 0xCB41, 0x8582,
	0xCB43, 0xCB49, 0x8588,
	0xCB4A, 0xCB54, 0x8590,
	0xCB55, 0xCB5B, 0x859D,
	0xCB5C, 0xCB5E, 0x85A5,
	0xCB60, 0xCB62, 0x85AB,
	0xCB63, 0xCB68, 0x85B1,
	0xCB6A, 0xCB70, 0x85BA,
	0xCB71, 0xCB77, 0x85C2,
	0xCB78, 0xCB7C, 0x85CA,
	0xCB7D, 0xCB7E, 0x85D1,
	0xCB81, 0xCB86, 0x85D6,
	0xCB87, 0xCB8D, 0x85DD,
	0xCB8E, 0xCB91, 0x85E5,
	0xCB92, 0xCBA0, 0x85EA,
	0xCC40, 0xCC41, 0x85F9,
	0xCC42, 0xCC44, 0x85FC,
	0xCC45, 0xCC49, 0x8600,
	0xCC4A, 0xCC54, 0x8606,
	0xCC55, 0xCC58, 0x8612,
	0xCC59, 0xCC68, 0x8617,
	0xCC6A, 0xCC77, 0x862A,
	0xCC78, 0xCC7A, 0x8639,
	0xCC7B, 0xCC7E, 0x863D,
	0xCC80, 0xCC8B, 0x8641,
	0xCC8C, 0xCC8D, 0x8652,
	0xCC8E, 0xCC92, 0x8655,
	0xCC93, 0xCC95, 0x865B,
	0xCC96, 0xCC98, 0x865F,
	0xCC99, 0xCCA0, 0x8663,
	0xCD41, 0xCD42, 0x866F,
	0xCD43, 0xCD49, 0x8672,
	0xCD4A, 0xCD50, 0x8683,
	0xCD51, 0xCD55, 0x868E,
	0xCD57, 0xCD5C, 0x8696,
	0xCD5D, 0xCD61, 0x869E,
	0xCD62, 0xCD63, 0x86A5,
	0xCD65, 0xCD66, 0x86AD,
	0xCD67, 0xCD68, 0x86B2,
	0xCD69, 0xCD6B, 0x86B7,
	0xCD6C, 0xCD70, 0x86BB,
	0xCD71, 0xCD73, 0x86C1,
	0xCD76, 0xCD77, 0x86CC,
	0xCD78, 0xCD79, 0x86D2,
	0xCD7A, 0xCD7C, 0x86D5,
	0xCD81, 0xCD84, 0x86E0,
	0xCD85, 0xCD88, 0x86E5,
	0xCD89, 0xCD8B, 0x86EA,
	0xCD8D, 0xCD8F, 0x86F5,
	0xCD90, 0xCD93, 0x86FA,
	0xCD96, 0xCD98, 0x8704,
	0xCD99, 0xCD9A, 0x870B,
	0xCD9B, 0xCD9E, 0x870E,
	0xCE43, 0xCE44, 0x871F,
	0xCE46, 0xCE48, 0x8726,
	0xCE49, 0xCE4C, 0x872A,
	0xCE4D, 0xCE4E, 0x872F,
	0xCE4F, 0xCE50, 0x8732,
	0xCE51, 0xCE52, 0x8735,
	0xCE53, 0xCE55, 0x8738,
	0xCE56, 0xCE57, 0x873C,
	0xCE58, 0xCE5E, 0x8740,
	0xCE5F, 0xCE60, 0x874A,
	0xCE62, 0xCE65, 0x874F,
	0xCE66, 0xCE68, 0x8754,
	0xCE6A, 0xCE6F, 0x875A,
	0xCE70, 0xCE71, 0x8761,
	0xCE72, 0xCE79, 0x8766,
	0xCE7B, 0xCE7D, 0x8771,
	0xCE80, 0xCE83, 0x8777,
	0xCE84, 0xCE86, 0x877F,
	0xCE88, 0xCE89, 0x8786,
	0xCE8A, 0xCE8B, 0x8789,
	0xCE8D, 0xCE91, 0x878E,
	0xCE92, 0xCE94, 0x8794,
	0xCE95, 0xCE9B, 0x8798,
	0xCE9C, 0xCEA0, 0x87A0,
	0xCF40, 0xCF42, 0x87A5,
	0xCF43, 0xCF44, 0x87A9,
	0xCF46, 0xCF48, 0x87B0,
	0xCF4A, 0xCF4D, 0x87B6,
	0xCF4E, 0xCF4F, 0x87BB,
	0xCF50, 0xCF51, 0x87BE,
	0xCF52, 0xCF56, 0x87C1,
	0xCF57, 0xCF59, 0x87C7,
	0xCF5A, 0xCF5E, 0x87CC,
	0xCF5F, 0xCF65, 0x87D4,
	0xCF66, 0xCF69, 0x87DC,
	0xCF6A, 0xCF6D, 0x87E1,
	0xCF6E, 0xCF71, 0x87E6,
	0xCF72, 0xCF74, 0x87EB,
	0xCF75, 0xCF7E, 0x87EF,
	0xCF80, 0xCF83, 0x87FA,
	0xCF84, 0xCF87, 0x87FF,
	0xCF88, 0xCF8D, 0x8804,
	0xCF8E, 0xCF95, 0x880B,
	0xCF97, 0xCF9A, 0x8817,
	0xCF9B, 0xCF9F, 0x881C,
	0xD040, 0xD04D, 0x8824,
	0xD04E, 0xD053, 0x8833,
	0xD054, 0xD055, 0x883A,
	0xD056, 0xD058, 0x883D,
	0xD059, 0xD05B, 0x8841,
	0xD05C, 0xD061, 0x8846,
	0xD062, 0xD067, 0x884E,
	0xD068, 0xD069, 0x8855,
	0xD06B, 0xD071, 0x885A,
	0xD072, 0xD073, 0x8866,
	0xD078, 0xD07B, 0x8873,
	0xD07C, 0xD07E, 0x8878,
	0xD080, 0xD081, 0x887B,
	0xD084, 0xD085, 0x8886,
	0xD086, 0xD087, 0x8889,
	0xD089, 0xD08C, 0x888E,
	0xD08D, 0xD08F, 0x8893,
	0xD090, 0xD094, 0x8897,
	0xD095, 0xD099, 0x889D,
	0xD09B, 0xD0A0, 0x88A5,
	0xD141, 0xD143, 0x88AE,
	0xD144, 0xD148, 0x88B2,
	0xD149, 0xD14C, 0x88B8,
	0xD14D, 0xD150, 0x88BD,
	0xD151, 0xD152, 0x88C3,
	0xD153, 0xD154, 0x88C7,
	0xD155, 0xD158, 0x88CA,
	0xD159, 0xD15B, 0x88CF,
	0xD15D, 0xD15E, 0x88D6,
	0xD15F, 0xD163, 0x88DA,
	0xD164, 0xD165, 0x88E0,
	0xD166, 0xD167, 0x88E6,
	0xD168, 0xD16E, 0x88E9,
	0xD170, 0xD172, 0x88F5,
	0xD173, 0xD174, 0x88FA,
	0xD176, 0xD178, 0x88FF,
	0xD179, 0xD17E, 0x8903,
	0xD181, 0xD185, 0x890B,
	0xD187, 0xD18B, 0x8914,
	0xD18C, 0xD190, 0x891C,
	0xD191, 0xD193, 0x8922,
	0xD194, 0xD197, 0x8926,
	0xD198, 0xD19B, 0x892C,
	0xD19C, 0xD19E, 0x8931,
	0xD240, 0xD248, 0x8938,
	0xD249, 0xD24A, 0x8942,
	0xD24B, 0xD263, 0x8945,
	0xD264, 0xD269, 0x8960,
	0xD26A, 0xD27D, 0x8967,
	0xD280, 0xD281, 0x897D,
	0xD284, 0xD285, 0x8984,
	0xD286, 0xD2A0, 0x8987,
	0xD340, 0xD35E, 0x89A2,
	0xD361, 0xD363, 0x89D3,
	0xD364, 0xD366, 0x89D7,
	0xD369, 0xD36C, 0x89DF,
	0xD36E, 0xD371, 0x89E7,
	0xD372, 0xD374, 0x89EC,
	0xD375, 0xD377, 0x89F0,
	0xD378, 0xD37E, 0x89F4,
	0xD380, 0xD384, 0x89FB,
	0xD385, 0xD38A, 0x8A01,
	0xD38B, 0xD3A0, 0x8A08,
	0xD3A9, 0xD3AA, 0x8424,
	0xD440, 0xD45F, 0x8A1E,
	0xD460, 0xD468, 0x8A3F,
	0xD469, 0xD47E, 0x8A49,
	0xD480, 0xD499, 0x8A5F,
	0xD49A, 0xD4A0, 0x8A7A,
	0xD540, 0xD547, 0x8A81,
	0xD548, 0xD54F, 0x8A8B,
	0xD550, 0xD57E, 0x8A94,
	0xD580, 0xD5A0, 0x8AC3,
	0xD640, 0xD662, 0x8AE4,
	0xD663, 0xD67E, 0x8B08,
	0xD680, 0xD681, 0x8B24,
	0xD682, 0xD6A0, 0x8B27,
	0xD6C1, 0xD6C2, 0x81F3,
	0xD740, 0xD75F, 0x8B46,
	0xD760, 0xD764, 0x8B67,
	0xD765, 0xD77E, 0x8B6D,
	0xD780, 0xD798, 0x8B87,
	0xD840, 0xD848, 0x8C38,
	0xD849, 0xD84C, 0x8C42,
	0xD84E, 0xD84F, 0x8C4A,
	0xD850, 0xD857, 0x8C4D,
	0xD858, 0xD85B, 0x8C56,
	0xD85C, 0xD861, 0x8C5B,
	0xD862, 0xD868, 0x8C63,
	0xD869, 0xD86F, 0x8C6C,
	0xD870, 0xD873, 0x8C74,
	0xD874, 0xD87A, 0x8C7B,
	0xD87B, 0xD87C, 0x8C83,
	0xD87D, 0xD87E, 0x8C86,
	0xD882, 0xD888, 0x8C8D,
	0xD889, 0xD88B, 0x8C95,
	0xD88C, 0xD8A0, 0x8C99,
	0xD8DB, 0xD8DC, 0x523F,
	0xD940, 0xD97E, 0x8CAE,
	0xD980, 0xD9A0, 0x8CED,
	0xDA40, 0xDA4E, 0x8D0E,
	0xDA50, 0xDA51, 0x8D51,
	0xDA55, 0xDA57, 0x8D68,
	0xDA59, 0xDA5A, 0x8D6E,
	0xDA5B, 0xDA5C, 0x8D71,
	0xDA5D, 0xDA65, 0x8D78,
	0xDA66, 0xDA67, 0x8D82,
	0xDA68, 0xDA6B, 0x8D86,
	0xDA6C, 0xDA70, 0x8D8C,
	0xDA71, 0xDA72, 0x8D92,
	0xDA73, 0xDA7C, 0x8D95,
	0xDA7D, 0xDA7E, 0x8DA0,
	0xDA81, 0xDA8D, 0x8DA4,
	0xDA8F, 0xDA90, 0x8DB6,
	0xDA94, 0xDA96, 0x8DC0,
	0xDA98, 0xDA9B, 0x8DC7,
	0xDA9E, 0xDAA0, 0x8DD2,
	0xDAA6, 0xDAA7, 0x8BA6,
	0xDAA9, 0xDAAA, 0x8BB4,
	0xDAAC, 0xDAAD, 0x8BC2,
	0xDAB1, 0xDAB3, 0x8BD2,
	0xDAB5, 0xDAB6, 0x8BD8,
	0xDAB8, 0xDAB9, 0x8BDF,
	0xDABB, 0xDABC, 0x8BE8,
	0xDAC3, 0xDAC4, 0x8BFF,
	0xDACA, 0xDACB, 0x8C11,
	0xDACC, 0xDACE, 0x8C14,
	0xDAD3, 0xDAD5, 0x8C1F,
	0xDAD8, 0xDAD9, 0x8C2A,
	0xDADA, 0xDADB, 0x8C2E,
	0xDADC, 0xDADD, 0x8C32,
	0xDADE, 0xDADF, 0x8C35,
	0xDB41, 0xDB42, 0x8DD8,
	0xDB44, 0xDB46, 0x8DE0,
	0xDB47, 0xDB49, 0x8DE5,
	0xDB4B, 0xDB4C, 0x8DED,
	0xDB4D, 0xDB4F, 0x8DF0,
	0xDB53, 0xDB59, 0x8DFE,
	0xDB5A, 0xDB5C, 0x8E06,
	0xDB5E, 0xDB5F, 0x8E0D,
	0xDB60, 0xDB63, 0x8E10,
	0xDB64, 0xDB6B, 0x8E15,
	0xDB6C, 0xDB6D, 0x8E20,
	0xDB6E, 0xDB72, 0x8E24,
	0xDB76, 0xDB78, 0x8E32,
	0xDB79, 0xDB7B, 0x8E36,
	0xDB7C, 0xDB7D, 0x8E3B,
	0xDB82, 0xDB83, 0x8E45,
	0xDB84, 0xDB88, 0x8E4C,
	0xDB89, 0xDB8E, 0x8E53,
	0xDB8F, 0xDB9A, 0x8E5A,
	0xDB9B, 0xDB9C, 0x8E67,
	0xDB9D, 0xDB9E, 0x8E6A,
	0xDBBE, 0xDBBF, 0x52AC,
	0xDBDC, 0xDBDD, 0x572E,
	0xDC42, 0xDC46, 0x8E77,
	0xDC47, 0xDC48, 0x8E7D,
	0xDC4A, 0xDC4C, 0x8E82,
	0xDC4E, 0xDC54, 0x8E88,
	0xDC55, 0xDC57, 0x8E91,
	0xDC58, 0xDC5E, 0x8E95,
	0xDC60, 0xDC6B, 0x8E9F,
	0xDC6C, 0xDC6D, 0x8EAD,
	0xDC6E, 0xDC6F, 0x8EB0,
	0xDC70, 0xDC76, 0x8EB3,
	0xDC77, 0xDC7E, 0x8EBB,
	0xDC80, 0xDC8A, 0x8EC3,
	0xDC8B, 0xDCA0, 0x8ECF,
	0xDCC8, 0xDCC9, 0x82CB,
	0xDCE3, 0xDCE4, 0x8314,
	0xDCE9, 0xDCEA, 0x835B,
	0xDD40, 0xDD7E, 0x8EE5,
	0xDD80, 0xDDA0, 0x8F24,
	0xDDA6, 0xDDA7, 0x836D,
	0xDDAA, 0xDDAB, 0x83B3,
	0xDDCE, 0xDDCF, 0x83F8,
	0xDDDB, 0xDDDC, 0x8487,
	0xDE40, 0xDE60, 0x8F45,
	0xDE66, 0xDE68, 0x8FA0,
	0xDE69, 0xDE6C, 0x8FA4,
	0xDE6E, 0xDE71, 0x8FAC,
	0xDE72, 0xDE75, 0x8FB2,
	0xDE76, 0xDE77, 0x8FB7,
	0xDE78, 0xDE7A, 0x8FBA,
	0xDE7B, 0xDE7C, 0x8FBF,
	0xDE80, 0xDE84, 0x8FC9,
	0xDE87, 0xDE88, 0x8FD6,
	0xDE8A, 0xDE8B, 0x8FE0,
	0xDE90, 0xDE91, 0x8FF1,
	0xDE92, 0xDE94, 0x8FF4,
	0xDE95, 0xDE97, 0x8FFA,
	0xDE98, 0xDE99, 0x8FFE,
	0xDE9A, 0xDE9B, 0x9007,
	0xDF42, 0xDF44, 0x9023,
	0xDF45, 0xDF4A, 0x9027,
	0xDF4B, 0xDF4F, 0x9030,
	0xDF51, 0xDF52, 0x9039,
	0xDF54, 0xDF55, 0x903F,
	0xDF57, 0xDF58, 0x9045,
	0xDF59, 0xDF5D, 0x9048,
	0xDF5F, 0xDF61, 0x9054,
	0xDF62, 0xDF63, 0x9059,
	0xDF64, 0xDF69, 0x905C,
	0xDF6B, 0xDF6C, 0x9066,
	0xDF6D, 0xDF70, 0x9069,
	0xDF71, 0xDF75, 0x906F,
	0xDF76, 0xDF7C, 0x9076,
	0xDF80, 0xDF83, 0x9084,
	0xDF84, 0xDF85, 0x9089,
	0xDF86, 0xDF8A, 0x908C,
	0xDF91, 0xDF93, 0x909E,
	0xDF94, 0xDF95, 0x90A4,
	0xDF96, 0xDF98, 0x90A7,
	0xDF9D, 0xDF9E, 0x90BC,
	0xDF9F, 0xDFA0, 0x90BF,
	0xDFA2, 0xDFA3, 0x64B7,
	0xDFBC, 0xDFBE, 0x5452,
	0xDFCB, 0xDFCC, 0x549A,
	0xDFD8, 0xDFD9, 0x54D3,
	0xDFE0, 0xDFE1, 0x54D9,
	0xDFE3, 0xDFE4, 0x54A9,
	0xDFEF, 0xDFF0, 0x5522,
	0xE040, 0xE041, 0x90C2,
	0xE043, 0xE044, 0x90C8,
	0xE045, 0xE047, 0x90CB,
	0xE049, 0xE04B, 0x90D4,
	0xE04C, 0xE04E, 0x90D8,
	0xE04F, 0xE051, 0x90DE,
	0xE052, 0xE054, 0x90E3,
	0xE055, 0xE056, 0x90E9,
	0xE059, 0xE05C, 0x90F0,
	0xE05D, 0xE05F, 0x90F5,
	0xE060, 0xE063, 0x90F9,
	0xE064, 0xE066, 0x90FF,
	0xE068, 0xE07B, 0x9105,
	0xE07C, 0xE07E, 0x911A,
	0xE081, 0xE083, 0x911F,
	0xE084, 0xE08E, 0x9124,
	0xE090, 0xE096, 0x9132,
	0xE097, 0xE09F, 0x913A,
	0xE0A3, 0xE0A5, 0x5575,
	0xE0B6, 0xE0B7, 0x55BD,
	0xE0BF, 0xE0C0, 0x55EB,
	0xE0C7, 0xE0C8, 0x55F2,
	0xE0C9, 0xE0CA, 0x55CC,
	0xE0E7, 0xE0E8, 0x567B,
	0xE0FD, 0xE0FE, 0x5E3B,
	0xE141, 0xE142, 0x9147,
	0xE144, 0xE147, 0x9153,
	0xE148, 0xE149, 0x9158,
	0xE14A, 0xE14B, 0x915B,
	0xE14C, 0xE14D, 0x915F,
	0xE14E, 0xE150, 0x9166,
	0xE154, 0xE156, 0x917A,
	0xE157, 0xE15B, 0x9180,
	0xE15F, 0xE160, 0x918E,
	0xE161, 0xE167, 0x9193,
	0xE168, 0xE16D, 0x919C,
	0xE16E, 0xE173, 0x91A4,
	0xE174, 0xE175, 0x91AB,
	0xE176, 0xE179, 0x91B0,
	0xE17A, 0xE17D, 0x91B6,
	0xE180, 0xE18A, 0x91BC,
	0xE18E, 0xE197, 0x91D2,
	0xE198, 0xE1A0, 0x91DD,
	0xE1AD, 0xE1AE, 0x5C98,
	0xE1C0, 0xE1C1, 0x5D02,
	0xE1EE, 0xE1EF, 0x72B7,
	0xE240, 0xE27E, 0x91E6,
	0xE280, 0xE2A0, 0x9225,
	0xE2BC, 0xE2C1, 0x9967,
	0xE2CA, 0xE2CB, 0x9990,
	0xE2CC, 0xE2CE, 0x9993,
	0xE2EA, 0xE2EB, 0x6005,
	0xE2FA, 0xE2FB, 0x6078,
	0xE340, 0xE36D, 0x9246,
	0xE36E, 0xE37E, 0x9275,
	0xE380, 0xE387, 0x9286,
	0xE388, 0xE3A0, 0x928F,
	0xE3C9, 0xE3CA, 0x95F5,
	0xE3CD, 0xE3CE, 0x9603,
	0xE3D1, 0xE3D4, 0x960A,
	0xE3D7, 0xE3D9, 0x9615,
	0xE3DA, 0xE3DB, 0x9619,
	0xE3E8, 0xE3E9, 0x6C68,
	0xE3F1, 0xE3F2, 0x6CF7,
	0xE440, 0xE445, 0x92A8,
	0xE446, 0xE45E, 0x92AF,
	0xE45F, 0xE47E, 0x92C9,
	0xE480, 0xE4A0, 0x92E9,
	0xE4B8, 0xE4B9, 0x6D93,
	0xE4D4, 0xE4D5, 0x6E53,
	0xE4EB, 0xE4EC, 0x6F46,
	0xE540, 0xE573, 0x930A,
	0xE574, 0xE57E, 0x933F,
	0xE580, 0xE59F, 0x934A,
	0xE5D3, 0xE5D4, 0x9035,
	0xE5D8, 0xE5D9, 0x9051,
	0xE5FC, 0xE5FD, 0x59A9,
	0xE640, 0xE662, 0x936C,
	0xE663, 0xE67E, 0x9390,
	0xE680, 0xE69D, 0x93AC,
	0xE69E, 0xE6A0, 0x93CB,
	0xE6AB, 0xE6AC, 0x5A05,
	0xE6E1, 0xE6E2, 0x9A77,
	0xE6E6, 0xE6E7, 0x9A80,
	0xE6EC, 0xE6ED, 0x9A92,
	0xE6F0, 0xE6F2, 0x9A9B,
	0xE6F3, 0xE6F4, 0x9A9F,
	0xE6F5, 0xE6F6, 0x9AA2,
	0xE6FD, 0xE6FE, 0x7EA8,
	0xE740, 0xE747, 0x93CE,
	0xE748, 0xE77E, 0x93D7,
	0xE780, 0xE7A0, 0x940E,
	0xE7A4, 0xE7A6, 0x7EC0,
	0xE7A8, 0xE7A9, 0x7ECB,
	0xE7AE, 0xE7AF, 0x7EE0,
	0xE7B2, 0xE7B3, 0x7EEE,
	0xE7B4, 0xE7B5, 0x7EF1,
	0xE7B8, 0xE7B9, 0x7EFA,
	0xE7BB, 0xE7BD, 0x7F01,
	0xE7BE, 0xE7BF, 0x7F07,
	0xE7C0, 0xE7C1, 0x7F0B,
	0xE7C3, 0xE7C4, 0x7F11,
	0xE7CA, 0xE7D0, 0x7F21,
	0xE7D1, 0xE7D4, 0x7F2A,
	0xE7D5, 0xE7D9, 0x7F2F,
	0xE840, 0xE84E, 0x942F,
	0xE84F, 0xE87A, 0x943F,
	0xE87B, 0xE87E, 0x946C,
	0xE880, 0xE894, 0x9470,
	0xE89A, 0xE89B, 0x94D3,
	0xE8AD, 0xE8AE, 0x7480,
	0xE8B2, 0xE8B3, 0x74A8,
	0xE8B8, 0xE8BA, 0x97EA,
	0xE8E0, 0xE8E1, 0x6832,
	0xE8E2, 0xE8E3, 0x6860,
	0xE8FC, 0xE8FD, 0x691F,
	0xE94A, 0xE94B, 0x9574,
	0xE94C, 0xE953, 0x9577,
	0xE954, 0xE97E, 0x9580,
	0xE980, 0xE9A0, 0x95AB,
	0xE9B4, 0xE9B5, 0x6987,
	0xE9CB, 0xE9CC, 0x6A17,
	0xE9E2, 0xE9E3, 0x6B81,
	0xE9E6, 0xE9E7, 0x6B92,
	0xE9E9, 0xE9EA, 0x6B9A,
	0xE9EF, 0xE9F1, 0x8F71,
	0xE9F2, 0xE9F3, 0x8F75,
	0xE9F6, 0xE9F7, 0x8F79,
	0xE9FA, 0xE9FB, 0x8F81,
	0xEA40, 0xEA5B, 0x95CC,
	0xEA64, 0xEA6A, 0x9623,
	0xEA6B, 0xEA6D, 0x962B,
	0xEA6E, 0xEA6F, 0x962F,
	0xEA70, 0xEA73, 0x9637,
	0xEA78, 0xEA79, 0x964E,
	0xEA7A, 0xEA7C, 0x9651,
	0xEA7D, 0xEA7E, 0x9656,
	0xEA80, 0xEA82, 0x9658,
	0xEA83, 0xEA85, 0x965C,
	0xEA88, 0xEA89, 0x9665,
	0xEA8B, 0xEA8F, 0x966D,
	0xEA91, 0xEA9D, 0x9678,
	0xEA9F, 0xEAA0, 0x9689,
	0xEAA1, 0xEAA3, 0x8F8D,
	0xEAB8, 0xEABA, 0x65EE,
	0xEAD6, 0xEAD7, 0x66DB,
	0xEADA, 0xEADB, 0x8D32,
	0xEAE0, 0xEAE1, 0x8D45,
	0xEAE2, 0xEAE3, 0x8D48,
	0xEAE9, 0xEAEB, 0x89CA,
	0xEAEC, 0xEAEF, 0x89CE,
	0xEAF5, 0xEAF6, 0x727E,
	0xEB42, 0xEB44, 0x9691,
	0xEB45, 0xEB46, 0x9695,
	0xEB47, 0xEB48, 0x969A,
	0xEB49, 0xEB52, 0x969D,
	0xEB53, 0xEB5A, 0x96A8,
	0xEB5B, 0xEB5C, 0x96B1,
	0xEB5D, 0xEB5E, 0x96B4,
	0xEB5F, 0xEB60, 0x96B7,
	0xEB61, 0xEB62, 0x96BA,
	0xEB64, 0xEB65, 0x96C2,
	0xEB67, 0xEB68, 0x96CA,
	0xEB69, 0xEB6A, 0x96D0,
	0xEB6B, 0xEB6C, 0x96D3,
	0xEB6D, 0xEB76, 0x96D6,
	0xEB77, 0xEB7D, 0x96E1,
	0xEB80, 0xEB82, 0x96EC,
	0xEB83, 0xEB85, 0x96F0,
	0xEB86, 0xEB87, 0x96F4,
	0xEB89, 0xEB8C, 0x96FA,
	0xEB8E, 0xEB8F, 0x9702,
	0xEB91, 0xEB93, 0x970A,
	0xEB94, 0xEB96, 0x9710,
	0xEB97, 0xEB98, 0x9714,
	0xEB99, 0xEB9D, 0x9717,
	0xEB9F, 0xEBA0, 0x971F,
	0xEBAE, 0xEBB0, 0x6C18,
	0xEBCA, 0xEBCD, 0x80E7,
	0xEBDA, 0xEBDB, 0x810D,
	0xEBEA, 0xEBEB, 0x8159,
	0xEBEF, 0xEBF0, 0x817C,
	0xEC40, 0xEC48, 0x9721,
	0xEC49, 0xEC4A, 0x972B,
	0xEC4B, 0xEC4C, 0x972E,
	0xEC4E, 0xEC52, 0x9733,
	0xEC53, 0xEC56, 0x973A,
	0xEC57, 0xEC69, 0x973F,
	0xEC6A, 0xEC6B, 0x9754,
	0xEC6C, 0xEC6D, 0x9757,
	0xEC6F, 0xEC70, 0x975C,
	0xEC72, 0xEC73, 0x9763,
	0xEC74, 0xEC76, 0x9766,
	0xEC77, 0xEC7E, 0x976A,
	0xEC82, 0xEC86, 0x9777,
	0xEC87, 0xEC8E, 0x977D,
	0xEC8F, 0xEC93, 0x9786,
	0xEC95, 0xEC97, 0x978E,
	0xEC99, 0xEC9B, 0x9795,
	0xEC9C, 0xECA0, 0x9799,
	0xECA9, 0xECAB, 0x98D1,
	0xECAD, 0xECAE, 0x98D9,
	0xECE8, 0xECE9, 0x6248,
	0xECEE, 0xECEF, 0x795B,
	0xED40, 0xED41, 0x979E,
	0xED42, 0xED43, 0x97A1,
	0xED44, 0xED4A, 0x97A4,
	0xED4D, 0xED4E, 0x97B0,
	0xED50, 0xED7E, 0x97B5,
	0xED80, 0xED81, 0x97E4,
	0xED83, 0xED87, 0x97EE,
	0xED89, 0xEDA0, 0x97F7,
	0xEDBA, 0xEDBB, 0x7817,
	0xEDBF, 0xEDC0, 0x781C,
	0xEDC1, 0xEDC3, 0x7839,
	0xEDCC, 0xEDCD, 0x7856,
	0xEDEA, 0xEDEB, 0x9EFB,
	0xEDF0, 0xEDF1, 0x7707,
	0xEDF9, 0xEDFA, 0x7750,
	0xEE40, 0xEE7E, 0x980F,
	0xEE80, 0xEEA0, 0x984E,
	0xEEA9, 0xEEAA, 0x779F,
	0xEEC4, 0xEEC6, 0x9485,
	0xEEC9, 0xEECA, 0x948C,
	0xEECB, 0xEECC, 0x948F,
	0xEED0, 0xEED2, 0x949A,
	0xEED3, 0xEED4, 0x94A3,
	0xEED9, 0xEEDA, 0x94AF,
	0xEEDD, 0xEEE1, 0x94B6,
	0xEEE2, 0xEEE3, 0x94BC,
	0xEEE6, 0xEEEC, 0x94C8,
	0xEEED, 0xEEEF, 0x94D0,
	0xEEF0, 0xEEF2, 0x94D5,
	0xEEF6, 0xEEF8, 0x94DE,
	0xEEFA, 0xEEFB, 0x94E4,
	0xEEFC, 0xEEFD, 0x94E7,
	0xEF40, 0xEF45, 0x986F,
	0xEF4C, 0xEF71, 0x98A8,
	0xEF72, 0xEF73, 0x98CF,
	0xEF75, 0xEF76, 0x98D6,
	0xEF77, 0xEF79, 0x98DB,
	0xEF7A, 0xEF7E, 0x98E0,
	0xEF80, 0xEF81, 0x98E5,
	0xEF82, 0xEFA0, 0x98E9,
	0xEFA3, 0xEFA4, 0x94EE,
	0xEFA5, 0xEFA7, 0x94F3,
	0xEFAA, 0xEFAB, 0x94FC,
	0xEFAF, 0xEFB0, 0x9506,
	0xEFB1, 0xEFB2, 0x9509,
	0xEFB3, 0xEFB5, 0x950D,
	0xEFB6, 0xEFBA, 0x9512,
	0xEFBD, 0xEFBF, 0x951D,
	0xEFC1, 0xEFC2, 0x952A,
	0xEFC5, 0xEFC6, 0x9531,
	0xEFC8, 0xEFCA, 0x9536,
	0xEFCC, 0xEFCD, 0x953E,
	0xEFD0, 0xEFD2, 0x9544,
	0xEFD5, 0xEFD6, 0x954E,
	0xEFD7, 0xEFD9, 0x9552,
	0xEFDA, 0xEFDD, 0x9556,
	0xEFDF, 0xEFE0, 0x955E,
	0xEFE2, 0xEFE3, 0x9561,
	0xEFE4, 0xEFEC, 0x9564,
	0xEFEE, 0xEFF0, 0x9571,
	0xF040, 0xF044, 0x9908,
	0xF045, 0xF046, 0x990E,
	0xF047, 0xF063, 0x9911,
	0xF064, 0xF07E, 0x992F,
	0xF080, 0xF089, 0x994A,
	0xF08A, 0xF096, 0x9956,
	0xF09A, 0xF09B, 0x9978,
	0xF09E, 0xF09F, 0x9982,
	0xF0B1, 0xF0B5, 0x9E28,
	0xF0BB, 0xF0BC, 0x9E39,
	0xF0BE, 0xF0BF, 0x9E41,
	0xF0C1, 0xF0C4, 0x9E46,
	0xF0C5, 0xF0C6, 0x9E4B,
	0xF0CB, 0xF0CD, 0x9E5A,
	0xF0D0, 0xF0D6, 0x9E66,
	0xF0E1, 0xF0E2, 0x75B3,
	0xF142, 0xF14C, 0x999A,
	0xF14D, 0xF14E, 0x99A6,
	0xF14F, 0xF17E, 0x99A9,
	0xF180, 0xF1A0, 0x99D9,
	0xF1B6, 0xF1B7, 0x7A78,
	0xF1ED, 0xF1EF, 0x8025,
	0xF240, 0xF27E, 0x99FA,
	0xF280, 0xF2A0, 0x9A39,
	0xF2A2, 0xF2A3, 0x988C,
	0xF2A6, 0xF2A7, 0x989A,
	0xF2A8, 0xF2A9, 0x989E,
	0xF2AA, 0xF2AB, 0x98A1,
	0xF2AC, 0xF2AD, 0x98A5,
	0xF2CC, 0xF2CD, 0x86F1,
	0xF2ED, 0xF2EE, 0x877D,
	0xF340, 0xF351, 0x9A5A,
	0xF355, 0xF356, 0x9A8D,
	0xF357, 0xF358, 0x9A94,
	0xF35B, 0xF361, 0x9AA9,
	0xF362, 0xF365, 0x9AB2,
	0xF368, 0xF36A, 0x9ABD,
	0xF36B, 0xF36C, 0x9AC3,
	0xF36D, 0xF371, 0x9AC6,
	0xF372, 0xF375, 0x9ACD,
	0xF377, 0xF37A, 0x9AD4,
	0xF37B, 0xF37E, 0x9AD9,
	0xF380, 0xF381, 0x9ADD,
	0xF383, 0xF386, 0x9AE2,
	0xF387, 0xF38A, 0x9AE7,
	0xF38D, 0xF395, 0x9AF0,
	0xF397, 0xF39D, 0x9AFC,
	0xF39E, 0xF3A0, 0x9B04,
	0xF3C0, 0xF3C1, 0x7F44,
	0xF3C6, 0xF3C7, 0x7B03,
	0xF3E5, 0xF3E6, 0x7BA6,
	0xF3F6, 0xF3F7, 0x7BE5,
	0xF441, 0xF446, 0x9B09,
	0xF447, 0xF449, 0x9B10,
	0xF44A, 0xF454, 0x9B14,
	0xF455, 0xF457, 0x9B20,
	0xF458, 0xF462, 0x9B24,
	0xF463, 0xF464, 0x9B30,
	0xF465, 0xF46C, 0x9B33,
	0xF46D, 0xF470, 0x9B3D,
	0xF472, 0xF474, 0x9B4A,
	0xF477, 0xF478, 0x9B52,
	0xF479, 0xF47E, 0x9B55,
	0xF480, 0xF4A0, 0x9B5B,
	0xF4A8, 0xF4A9, 0x8201,
	0xF4AD, 0xF4AF, 0x8221,
	0xF4B6, 0xF4B7, 0x8233,
	0xF4C9, 0xF4CA, 0x7FAF,
	0xF4D4, 0xF4D5, 0x7CBC,
	0xF4D8, 0xF4D9, 0x7CCC,
	0xF4FC, 0xF4FD, 0x914E,
	0xF540, 0xF57E, 0x9B7C,
	0xF580, 0xF5A0, 0x9BBB,
	0xF5A6, 0xF5A7, 0x917D,
	0xF5B0, 0xF5B1, 0x91A2,
	0xF5B3, 0xF5B5, 0x91AD,
	0xF5C5, 0xF5C6, 0x8DD6,
	0xF5C9, 0xF5CA, 0x8DCE,
	0xF5CE, 0xF5CF, 0x8DF7,
	0xF5E4, 0xF5E5, 0x8E41,
	0xF5E6, 0xF5E7, 0x8E51,
	0xF640, 0xF67E, 0x9BDC,
	0xF680, 0xF6A0, 0x9C1B,
	0xF6B5, 0xF6BA, 0x9F85,
	0xF6C0, 0xF6C1, 0x96BC,
	0xF6D1, 0xF6D4, 0x9C85,
	0xF6D8, 0xF6DA, 0x9C90,
	0xF6DB, 0xF6DC, 0x9C94,
	0xF6DD, 0xF6DE, 0x9C9A,
	0xF6DF, 0xF6E4, 0x9C9E,
	0xF6E5, 0xF6E9, 0x9CA5,
	0xF6EB, 0xF6EC, 0x9CAD,
	0xF6ED, 0xF6F4, 0x9CB0,
	0xF6F5, 0xF6F8, 0x9CBA,
	0xF6F9, 0xF6FC, 0x9CC4,
	0xF6FD, 0xF6FE, 0x9CCA,
	0xF740, 0xF77E, 0x9C3C,
	0xF781, 0xF782, 0x9C7D,
	0xF784, 0xF785, 0x9C83,
	0xF786, 0xF787, 0x9C89,
	0xF78B, 0xF78E, 0x9C96,
	0xF794, 0xF798, 0x9CBE,
	0xF799, 0xF79A, 0x9CC8,
	0xF79B, 0xF79C, 0x9CD1,
	0xF79D, 0xF79E, 0x9CDA,
	0xF79F, 0xF7A0, 0x9CE0,
	0xF7A1, 0xF7A5, 0x9CCC,
	0xF7A6, 0xF7A8, 0x9CD3,
	0xF7A9, 0xF7AB, 0x9CD7,
	0xF7AC, 0xF7AD, 0x9CDC,
	0xF7B2, 0xF7B3, 0x9791,
	0xF7C5, 0xF7C6, 0x9ACB,
	0xF7E1, 0xF7E2, 0x9EBD,
	0xF7E5, 0xF7E6, 0x9E87,
	0xF7EC, 0xF7EE, 0x9EDB,
	0xF7FC, 0xF7FD, 0x9F3D,
	0xF840, 0xF87E, 0x9CE3,
	0xF880, 0xF8A0, 0x9D22,
	0xF940, 0xF97E, 0x9D43,
	0xF980, 0xF9A0, 0x9D82,
	0xFA40, 0xFA7E, 0x9DA3,
	0xFA80, 0xFAA0, 0x9DE2,
	0xFB40, 0xFB5B, 0x9E03,
	0xFB61, 0xFB62, 0x9E3B,
	0xFB66, 0xFB68, 0x9E52,
	0xFB6C, 0xFB6F, 0x9E5F,
	0xFB71, 0xFB72, 0x9E6E,
	0xFB74, 0xFB7D, 0x9E74,
	0xFB81, 0xFB84, 0x9E83,
	0xFB85, 0xFB86, 0x9E89,
	0xFB87, 0xFB8C, 0x9E8C,
	0xFB8D, 0xFB95, 0x9E94,
	0xFB97, 0xFB9C, 0x9EA0,
	0xFB9D, 0xFBA0, 0x9EA7,
	0xFC40, 0xFC48, 0x9EAB,
	0xFC49, 0xFC4B, 0x9EB5,
	0xFC4C, 0xFC4D, 0x9EB9,
	0xFC4F, 0xFC53, 0x9EBF,
	0xFC54, 0xFC57, 0x9EC5,
	0xFC58, 0xFC5A, 0x9ECA,
	0xFC5C, 0xFC5D, 0x9ED2,
	0xFC5E, 0xFC60, 0x9ED5,
	0xFC61, 0xFC62, 0x9ED9,
	0xFC65, 0xFC66, 0x9EE3,
	0xFC69, 0xFC6C, 0x9EEB,
	0xFC6D, 0xFC75, 0x9EF0,
	0xFC78, 0xFC7E, 0x9EFF,
	0xFC80, 0xFC84, 0x9F06,
	0xFC87, 0xFC88, 0x9F11,
	0xFC89, 0xFC8B, 0x9F14,
	0xFC8D, 0xFC92, 0x9F1A,
	0xFC94, 0xFC9C, 0x9F23,
	0xFC9D, 0xFC9E, 0x9F2D,
	0xFC9F, 0xFCA0, 0x9F30,
	0xFD40, 0xFD44, 0x9F32,
	0xFD48, 0xFD4C, 0x9F3F,
	0xFD4D, 0xFD57, 0x9F45,
	0xFD58, 0xFD7E, 0x9F52,
	0xFD80, 0xFD85, 0x9F79,
	0xFD86, 0xFD87, 0x9F81,
	0xFD88, 0xFD93, 0x9F8D,
	0xFD94, 0xFD96, 0x9F9C,
	0xFD97, 0xFD9B, 0x9FA1,
	0xFE40, 0xFE43, 0xFA0C,
	0xFE45, 0xFE46, 0xFA13,
	0xFE48, 0xFE4A, 0xFA1F,
	0xFE4B, 0xFE4C, 0xFA23,
	0xFE4D, 0xFE4F, 0xFA27,
	0xFE50, 0xFEA0, 0xE815
};

// void init_gbk2_utf16();
void init_utf162_gbk();
void init_gbk2_utf16_2();
void init_gbk2_utf16_3();

unsigned short gbk2_utf16_3_size;
unsigned short gbk2_utf16_2_size;

void init_str_convert()
{
	init_gbk2_utf16_2();
	init_gbk2_utf16_3();
	// init_gbk2_utf16();
	init_utf162_gbk();
}

void init_gbk2_utf16_2()
{
	gbk2_utf16_2_size = sizeof gbk2_utf16_2_host / sizeof(short);
}

void init_gbk2_utf16_3()
{
	gbk2_utf16_3_size = sizeof gbk2_utf16_3_host / sizeof(short);
}

// void init_gbk2_utf16()
// {
// 	unsigned short c;
// 	static unsigned short gbk2utf16_host[0x8000] = { 0 };
// 	for (c = 0; c < gbk2_utf16_2_size; c += 2)
// 		gbk2utf16_host[gbk2_utf16_2_host[c] - 0x8000] = gbk2_utf16_2_host[c + 1];
//
// 	for (c = 0; c < gbk2_utf16_3_size; c += 3)
// 		for (unsigned short d = gbk2_utf16_3_host[c]; d <= gbk2_utf16_3_host[c + 1]; d++)
// 			gbk2utf16_host[d - 0x8000] = gbk2_utf16_3_host[c + 2] + d - gbk2_utf16_3_host[c];
//
// 	gpuErrchk(cudaMemcpyToSymbol(gbk2utf16, gbk2utf16_host, sizeof(unsigned short) * 0x8000), true, nullptr);
// }

void init_utf162_gbk()
{
	unsigned short c;
	static unsigned short utf162gbk_host[0x10000] = { 0 };
	for (c = 0; c < gbk2_utf16_2_size; c += 2)
		utf162gbk_host[gbk2_utf16_2_host[c + 1]] = gbk2_utf16_2_host[c];

	for (c = 0; c < gbk2_utf16_3_size; c += 3)
		for (unsigned short d = gbk2_utf16_3_host[c]; d <= gbk2_utf16_3_host[c + 1]; d++)
			utf162gbk_host[gbk2_utf16_3_host[c + 2] + d - gbk2_utf16_3_host[c]] = d;

	gpuErrchk(cudaMemcpyToSymbol(utf162gbk, utf162gbk_host, sizeof(unsigned short) * 0x10000), true, nullptr);
}

// __device__ int gbk_to_utf8(const char* from, unsigned int from_len, char** to, unsigned int* to_len)
// {
// 	char* result = *to;
// 	unsigned i_to = 0;
// 	unsigned flag = 0;
//
// 	if (from_len == 0 || from == nullptr || to == nullptr || result == nullptr)
// 	{
// 		return -1;
// 	}
//
// 	for (unsigned i_from = 0; i_from < from_len; i_from++)
// 	{
// 		if (flag)
// 		{
// 			flag = 0;
// 			const unsigned short tmp =
// 				gbk2utf16[COMPBYTE(from[i_from - 1], from[i_from]) & ~0x8000];
//
// 			if (tmp == 0)
// 				continue;
// 			if (tmp >= 0x800)
// 			{
// 				result[i_to++] = 0xE0 | tmp >> 12;
// 				result[i_to++] = 0x80 | tmp >> 6 & 0x3F;
// 				result[i_to++] = 0x80 | tmp & 0x3F;
// 			}
// 			else if (tmp >= 0x80)
// 			{
// 				result[i_to++] = 0xC0 | tmp >> 6;
// 				result[i_to++] = 0x80 | tmp & 0x3F;
// 			}
// 			else
// 			{
// 				result[i_to++] = tmp;
// 			}
// 		}
// 		else if (from[i_from] < 0)
// 			flag = 1;
// 		else
// 			result[i_to++] = from[i_from];
// 	}
//
// 	result[i_to] = 0;
// 	*to_len = i_to;
// 	return 0;
// }


__device__ int utf8_to_gbk(const char* from, unsigned int from_len, char** to, unsigned int* to_len)
{
	char* result = *to;
	unsigned i_to = 0;

	if (from_len == 0 || from == nullptr || to == nullptr || result == nullptr)
	{
		return -1;
	}

	for (unsigned i_from = 0; i_from < from_len;)
	{
		if (static_cast<unsigned char>(from[i_from]) < 0x80)
		{
			result[i_to++] = from[i_from++];
		}
		else if (static_cast<unsigned char>(from[i_from]) < 0xC2)
		{
			i_from++;
		}
		else if (static_cast<unsigned char>(from[i_from]) < 0xE0)
		{
			if (i_from >= from_len - 1) break;

			const unsigned short tmp = utf162gbk[(from[i_from] & 0x1F) << 6 | from[i_from + 1] & 0x3F];

			if (tmp)
			{
				result[i_to++] = tmp >> 8;
				result[i_to++] = tmp & 0xFF;
			}

			i_from += 2;
		}
		else if (static_cast<unsigned char>(from[i_from]) < 0xF0)
		{
			if (i_from >= from_len - 2) break;

			const unsigned short tmp = utf162gbk[(from[i_from] & 0x0F) << 12
				| (from[i_from + 1] & 0x3F) << 6 | from[i_from + 2] & 0x3F];

			if (tmp)
			{
				result[i_to++] = tmp >> 8;
				result[i_to++] = tmp & 0xFF;
			}

			i_from += 3;
		}
		else
		{
			i_from += 4;
		}
	}

	result[i_to] = 0;
	if (to_len != nullptr)
	{
		*to_len = i_to;
	}
	return 0;
}
