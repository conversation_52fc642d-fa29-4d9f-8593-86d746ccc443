﻿#include "uwp.hpp"
#include <winrt/Windows.Management.Deployment.h>
#include <winrt/Windows.Foundation.Collections.h>
#include <winrt/Windows.ApplicationModel.h> 
#include <winrt/Windows.Storage.h>
#include <winrt/Windows.ApplicationModel.Core.h> 
#include <Windows.h>
#include <mutex>
#include <chrono>
#include <thread>

// 链接所需的静态库
#pragma comment(lib, "windowsapp")

std::string wstring_to_string(const std::wstring& wstr);

// 缓存相关的全局变量
static std::vector<UWP_STRUCT> cached_uwp_vector;
static std::mutex uwp_cache_mutex;
static std::chrono::steady_clock::time_point last_update_time;
static bool is_cache_initialized = false;
static bool is_updating_in_background = false;

// 缓存有效期（1分钟）
static constexpr std::chrono::minutes CACHE_TIMEOUT{ 1 };

// 内部函数：实际获取UWP应用信息
std::vector<UWP_STRUCT> get_uwp_apps_internal()
{
	std::vector<UWP_STRUCT> uwp_vector;
	try
	{
		// 初始化 WinRT 单元
		winrt::init_apartment();

		// 创建 PackageManager 实例
		winrt::Windows::Management::Deployment::PackageManager packageManager;

		// 获取当前用户的所有包
		const auto& packages = packageManager.FindPackages();

		// 遍历每个包并打印信息
		for (const auto& package : packages)
		{
			try
			{
				if (package.IsFramework())
				{
					continue;
				}

				// 获取包的状态
				const auto& status = package.Status();

				// 确保包已安装且状态正常
				if (!status.VerifyIsOK() || status.NotAvailable() || status.Disabled())
				{
					continue;
				}

				const auto entries = package.GetAppListEntriesAsync().get();

				UWP_STRUCT uwp_struct;
				if (entries.Size() > 0)
				{
					const auto entry = entries.GetAt(0);
					if (const auto app_info = entry.AppInfo())
					{
						uwp_struct.AppUserModelId = app_info.AppUserModelId().c_str();
					}
				}

				if (uwp_struct.AppUserModelId.empty())
				{
					continue;
				}

				// 获取并打印 DisplayName
				uwp_struct.DisplayName = package.DisplayName().c_str();

				// 获取 Id 对象
				const auto id = package.Id();

				// 打印 Id 的各个属性
				uwp_struct.Name = id.Name().c_str();

				const auto version = id.Version();
				uwp_struct.Version = std::to_wstring(version.Major) + L"." + std::to_wstring(version.Minor) + L"."
					+ std::to_wstring(version.Build) + L"." + std::to_wstring(version.Revision);

				uwp_struct.Architecture = static_cast<int>(id.Architecture());
				uwp_struct.ResourceId = id.ResourceId().c_str();
				uwp_struct.Publisher = id.Publisher().c_str();
				uwp_struct.PublisherId = id.PublisherId().c_str();
				uwp_struct.FullName = id.FullName().c_str();
				uwp_struct.FamilyName = id.FamilyName().c_str();

				if (const auto installed_location = package.InstalledLocation())
				{
					uwp_struct.InstallLocation = installed_location.Path().c_str();
				}

				uwp_vector.emplace_back(uwp_struct);
			}
			catch (const winrt::hresult_error& e)
			{
				const std::wstring error_info = e.message().c_str();
				const auto error_info_str = wstring_to_string(error_info);
				fprintf(stderr, "Get UWP apps info error, code: %d, message: %s\n", e.code().value, error_info_str.c_str());
				continue;
			}
		}
	}
	catch (const std::exception& e)
	{
		fprintf(stderr, "Error getting UWP apps: %s\n", e.what());
	}
	catch (...)
	{
		fprintf(stderr, "Unknown error occurred while getting UWP apps\n");
	}

	return uwp_vector;
}

// 检查缓存是否过期
bool is_cache_expired()
{
	if (!is_cache_initialized)
	{
		return true;
	}

	const auto now = std::chrono::steady_clock::now();
	const auto elapsed = now - last_update_time;
	return elapsed >= CACHE_TIMEOUT;
}

// 更新缓存数据
void update_cache()
{
	auto new_data = get_uwp_apps_internal();
	cached_uwp_vector = std::move(new_data);
	last_update_time = std::chrono::steady_clock::now();
	is_cache_initialized = true;
}

// 后台更新函数
void background_update_cache()
{
	try
	{
		auto new_data = get_uwp_apps_internal();

		// 获取锁并更新缓存
		std::lock_guard<std::mutex> lock(uwp_cache_mutex);
		cached_uwp_vector = std::move(new_data);
		last_update_time = std::chrono::steady_clock::now();
		is_cache_initialized = true;
		is_updating_in_background = false;
	}
	catch (...)
	{
		// 如果后台更新失败，重置标志
		std::lock_guard<std::mutex> lock(uwp_cache_mutex);
		is_updating_in_background = false;
	}
}

// 主要接口函数 - 基于超时的缓存
std::vector<UWP_STRUCT> get_all_uwp()
{
	std::lock_guard<std::mutex> lock(uwp_cache_mutex);

	// 检查缓存是否过期
	if (is_cache_expired())
	{
		// 如果有现有数据且没有正在进行后台更新，则启动后台更新
		if (is_cache_initialized && !cached_uwp_vector.empty() && !is_updating_in_background)
		{
			is_updating_in_background = true;
			// 启动后台线程进行更新，分离线程避免阻塞
			std::thread(background_update_cache).detach();
			// 返回现有数据
			return cached_uwp_vector;
		}
		// 如果没有现有数据或者已经在后台更新，则同步更新
		else if (!is_cache_initialized || cached_uwp_vector.empty())
		{
			update_cache();
		}
		// 如果正在后台更新，直接返回现有数据
	}

	return cached_uwp_vector;
}

// 强制刷新缓存
void refresh_uwp_cache()
{
	std::lock_guard<std::mutex> lock(uwp_cache_mutex);
	is_updating_in_background = false;  // 停止可能正在进行的后台更新
	update_cache();
}

// 清理缓存（现在只需要清空数据）
void cleanup_uwp_cache()
{
	std::lock_guard<std::mutex> lock(uwp_cache_mutex);
	cached_uwp_vector.clear();
	is_cache_initialized = false;
	is_updating_in_background = false;  // 重置后台更新标志
}

std::string wstring_to_string(const std::wstring& wstr)
{
	std::string str;
	size_t size;
	str.resize(wstr.length());
	wcstombs_s(&size, &str[0], str.size() + 1, wstr.c_str(), wstr.size());
	return str;
}