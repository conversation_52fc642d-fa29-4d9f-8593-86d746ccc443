﻿// dllmain.cpp : Defines the entry point for the DLL application.
#include "pch.h"
#include <iomanip>
#include <Windows.h>
#include <bcrypt.h>
#include <vector>
#include <string>
#include <sstream>
#include <WbemIdl.h>
#include <comdef.h>

#pragma comment(lib, "bcrypt.lib")
#pragma comment(lib, "wbemuuid.lib")

#define NT_SUCCESS(Status) (((NTSTATUS)(Status)) >= 0)

char g_key[65]{};
std::once_flag g_flag;

std::string sha256(const std::string& input);
std::string getBIOSSerial();

extern "C"
{
	__declspec(dllexport) char* getkey();
}


// Function to compute SHA-256 using Windows CNG
std::string sha256(const std::string& input)
{
	BCRYPT_ALG_HANDLE hAlg = NULL;
	BCRYPT_HASH_HANDLE hHash = NULL;
	NTSTATUS status;
	DWORD hashLength = 32; // SHA-256 is 32 bytes
	std::vector<BYTE> hash(hashLength);
	std::string result;

	// Open algorithm provider
	status = BCryptOpenAlgorithmProvider(&hAlg, BCRYPT_SHA256_ALGORITHM, NULL, 0);
	if (!NT_SUCCESS(status))
	{
		fprintf(stderr, "BCryptOpenAlgorithmProvider failed");
		return "";
	}

	// Create hash object
	status = BCryptCreateHash(hAlg, &hHash, NULL, 0, NULL, 0, 0);
	if (!NT_SUCCESS(status))
	{
		fprintf(stderr, "BCryptCreateHash failed");
		BCryptCloseAlgorithmProvider(hAlg, 0);
		return "";
	}

	// Hash input data
	status = BCryptHashData(hHash, (PBYTE)input.c_str(), (ULONG)input.length(), 0);
	if (!NT_SUCCESS(status))
	{
		fprintf(stderr, "BCryptHashData failed");
		BCryptDestroyHash(hHash);
		BCryptCloseAlgorithmProvider(hAlg, 0);
		return "";
	}

	// Finalize hash
	status = BCryptFinishHash(hHash, hash.data(), hashLength, 0);
	if (!NT_SUCCESS(status))
	{
		fprintf(stderr, "BCryptFinishHash failed");
		BCryptDestroyHash(hHash);
		BCryptCloseAlgorithmProvider(hAlg, 0);
		return "";
	}

	// Convert to hex string
	std::stringstream ss;
	for (DWORD i = 0; i < hashLength; i++)
	{
		ss << std::hex << std::setw(2) << std::setfill('0') << (int)hash[i];
	}
	result = ss.str();

	// Cleanup
	BCryptDestroyHash(hHash);
	BCryptCloseAlgorithmProvider(hAlg, 0);
	return result;
}

std::string getBIOSSerial()
{
	std::string serialNumber = "unknown";
	HRESULT hres;

	hres = CoInitializeEx(0, COINIT_MULTITHREADED);
	if (FAILED(hres)) return serialNumber;

	hres = CoInitializeSecurity(
		NULL, -1, NULL, NULL, RPC_C_AUTHN_LEVEL_DEFAULT,
		RPC_C_IMP_LEVEL_IMPERSONATE, NULL, EOAC_NONE, NULL);
	if (FAILED(hres))
	{
		CoUninitialize();
		return serialNumber;
	}

	IWbemLocator* pLoc = NULL;
	hres = CoCreateInstance(CLSID_WbemLocator, 0, CLSCTX_INPROC_SERVER,
		IID_IWbemLocator, (LPVOID*)&pLoc);
	if (FAILED(hres))
	{
		CoUninitialize();
		return serialNumber;
	}

	IWbemServices* pSvc = NULL;
	hres = pLoc->ConnectServer(_bstr_t(L"ROOT\\CIMV2"), NULL, NULL, 0, NULL, 0, 0, &pSvc);
	if (FAILED(hres))
	{
		pLoc->Release();
		CoUninitialize();
		return serialNumber;
	}

	hres = CoSetProxyBlanket(pSvc, RPC_C_AUTHN_WINNT, RPC_C_AUTHZ_NONE, NULL,
		RPC_C_AUTHN_LEVEL_CALL, RPC_C_IMP_LEVEL_IMPERSONATE, NULL, EOAC_NONE);
	if (FAILED(hres))
	{
		pSvc->Release();
		pLoc->Release();
		CoUninitialize();
		return serialNumber;
	}

	IEnumWbemClassObject* pEnumerator = NULL;
	hres = pSvc->ExecQuery(
		bstr_t("WQL"), bstr_t("SELECT SerialNumber FROM Win32_BIOS"),
		WBEM_FLAG_FORWARD_ONLY | WBEM_FLAG_RETURN_IMMEDIATELY, NULL, &pEnumerator);
	if (FAILED(hres))
	{
		pSvc->Release();
		pLoc->Release();
		CoUninitialize();
		return serialNumber;
	}

	IWbemClassObject* pclsObj = NULL;
	ULONG uReturn = 0;
	while (pEnumerator)
	{
		hres = pEnumerator->Next(WBEM_INFINITE, 1, &pclsObj, &uReturn);
		if (uReturn == 0) break;

		VARIANT vtProp;
		VariantInit(&vtProp);
		hres = pclsObj->Get(L"SerialNumber", 0, &vtProp, 0, 0);
		if (SUCCEEDED(hres) && vtProp.vt == VT_BSTR) {
			serialNumber = _bstr_t(vtProp.bstrVal);
		}
		VariantClear(&vtProp);
		pclsObj->Release();
	}

	pEnumerator->Release();
	pSvc->Release();
	pLoc->Release();
	CoUninitialize();
	return serialNumber;
}

char* getkey()
{
	std::call_once(g_flag, []
		{
			std::string result;

			// Leaf 0: Vendor ID and highest leaf
			int cpuInfo[4] = { 0 };
			__cpuid(cpuInfo, 0);
			char vendor[13] = { 0 };
			memcpy(vendor + 0, &cpuInfo[1], 4); // EBX
			memcpy(vendor + 4, &cpuInfo[3], 4); // EDX
			memcpy(vendor + 8, &cpuInfo[2], 4); // ECX
			result += std::string(vendor, 12); // e.g., "GenuineIntel"
			result += std::to_string(cpuInfo[0]); // Highest leaf

			// Leaf 1: Processor signature and feature flags
			__cpuid(cpuInfo, 1);
			cpuInfo[1] &= 0x00FFFFFF; // Mask out APIC ID
			result += std::to_string(cpuInfo[0]); // EAX: Signature
			result += std::to_string(cpuInfo[1]); // EBX: Brand index, etc.
			result += std::to_string(cpuInfo[2]); // ECX: Features
			result += std::to_string(cpuInfo[3]); // EDX: Features

			// Extended leaves: Brand string
			char brand[48] = { 0 };
			for (int i = 0; i < 3; i++)
			{
				__cpuid(cpuInfo, 0x80000002 + i);
				memcpy(brand + i * 16, cpuInfo, 16);
			}
			result += std::string(brand); // e.g., "Intel(R) Core(TM) i7-12700"
			result += getBIOSSerial();

			auto&& cpu_sha256 = sha256(result);
			strcpy_s(g_key, cpu_sha256.c_str());
		});
	return g_key;
}
