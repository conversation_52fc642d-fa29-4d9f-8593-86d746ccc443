package file.engine.entity;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ConfigEntity {
    @SerializedName("cacheNumLimit")
    private int cacheNumLimit;

    @SerializedName("updateTimeLimit")
    private int updateTimeLimit;

    @SerializedName("ignorePath")
    private String ignorePath;

    @SerializedName("priorityFolder")
    private String priorityFolder;

    @SerializedName("disks")
    private String disks;

    @SerializedName("isEnableGpuAccelerate")
    private boolean isEnableGpuAccelerate;

    @SerializedName("gpuDevice")
    private String gpuDevice;

    @SerializedName("searchThreadNumber")
    // 无作用配置，线程池移动到PathMatcher中，该配置不起作用
    private int searchThreadNumber;

    @SerializedName("llm")
    private String llm;

    @SerializedName("llmConfigs")
    private Map<String, Object> llmConfigs;

    @SerializedName("dataTypeSuffixMap")
    private Map<String, List<String>> dataTypeSuffixMap;

    @SerializedName("isEnableFuzzyMatch")
    private boolean isEnableFuzzyMatch;

    @SerializedName("advancedConfigs")
    private AdvancedConfigEntity advancedConfigEntity;
}
