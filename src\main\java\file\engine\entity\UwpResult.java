package file.engine.entity;

import file.engine.configs.AllConfigs;
import file.engine.services.DatabaseService;
import file.engine.services.utils.PathMatchUtil;

import java.util.Objects;

/**
 * 用于生成jni头文件，禁止使用Lombok
 */
public final class UwpResult {

    private final String displayName;

    private final String name;

    private String highlightName;

    private final String version;

    private final int architecture;

    private final String resourceId;

    private final String publisher;

    private final String publisherId;

    private final String fullName;

    private final String familyName;

    private final String installLocation;

    private final String appUserModelId;

    /**
     * Do Not Remove，用于PathMatcher dll构造对象
     */
    public UwpResult(String displayName,
                     String name,
                     String version,
                     int architecture,
                     String resourceId,
                     String publisher,
                     String publisherId,
                     String fullName,
                     String familyName,
                     String installLocation,
                     String appUserModelId) {
        this.displayName = displayName;
        this.name = name;
        this.version = version;
        this.architecture = architecture;
        this.resourceId = resourceId;
        this.publisher = publisher;
        this.publisherId = publisherId;
        this.fullName = fullName;
        this.familyName = familyName;
        this.installLocation = installLocation;
        this.appUserModelId = appUserModelId;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getName() {
        return name;
    }

    public String getVersion() {
        return version;
    }

    public int getArchitecture() {
        return architecture;
    }

    public String getResourceId() {
        return resourceId;
    }

    public String getPublisher() {
        return publisher;
    }

    public String getPublisherId() {
        return publisherId;
    }

    public String getFullName() {
        return fullName;
    }

    public String getFamilyName() {
        return familyName;
    }

    public String getInstallLocation() {
        return installLocation;
    }

    public String getAppUserModelId() {
        return appUserModelId;
    }

    public String getHighlightName() {
        return highlightName;
    }

    public void setHighlightName(DatabaseService.SearchInfo searchInfo) {
        boolean enableFuzzyMatch = AllConfigs
                .getInstance()
                .getConfigEntity()
                .isEnableFuzzyMatch();

        var highlightPair = PathMatchUtil.highlightKeywords(
                this.displayName,
                "",
                searchInfo.isIgnoreCase(),
                searchInfo.keywords(),
                searchInfo.keywordsLowerCase(),
                searchInfo.isKeywordPath(),
                enableFuzzyMatch
        );
        this.highlightName = highlightPair.right();
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof UwpResult uwpResult)) return false;
        return architecture == uwpResult.architecture && Objects.equals(displayName, uwpResult.displayName) && Objects.equals(name, uwpResult.name) && Objects.equals(version, uwpResult.version) && Objects.equals(resourceId, uwpResult.resourceId) && Objects.equals(publisher, uwpResult.publisher) && Objects.equals(publisherId, uwpResult.publisherId) && Objects.equals(fullName, uwpResult.fullName) && Objects.equals(familyName, uwpResult.familyName) && Objects.equals(installLocation, uwpResult.installLocation) && Objects.equals(appUserModelId, uwpResult.appUserModelId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(displayName, name, version, architecture, resourceId, publisher, publisherId, fullName, familyName, installLocation, appUserModelId);
    }
}
