#pragma once
#include <chrono>
#include <string>
#include <vector>
#include <concurrent_vector.h>
#include <atomic>
#include <regex>
#define MAX_PATH_LENGTH 500
#define MAX_KEYWORDS_NUMBER 150
#define MAX_STR_WRONG_CHAR 2

using path_match_struct = struct path_match_struct_ {
	char parent_path_var[MAX_PATH_LENGTH]{};
	char file_name_var[MAX_PATH_LENGTH]{};
	size_t file_size;
	uint64_t modify_time;
};

using search_info = class tmp_search_info
{
public:
	std::atomic_uint result_counter = 0;
	std::regex re;
	tmp_search_info(const int search_case_num, const bool is_ignore_case, const char* search_text,
		const std::vector<std::string>& keywords, const std::vector<std::string>& keywords_lower_case,
		const std::vector<bool>& is_keyword_path, const bool is_enable_fuzzy_match)
		: search_case_num(search_case_num),
		is_ignore_case(is_ignore_case),
		search_text(search_text),
		keywords(keywords),
		keywords_lower_case(keywords_lower_case),
		is_keyword_path(is_keyword_path),
		is_enable_fuzzy_match(is_enable_fuzzy_match)
	{
		if (search_case_num & 1 << 3)
		{
			this->re = std::regex(search_text);
		}
	}

	tmp_search_info(tmp_search_info&) = delete;
	tmp_search_info() = delete;

	~tmp_search_info() = default;

	[[nodiscard]] int get_search_case_num() const
	{
		return search_case_num;
	}

	[[nodiscard]] bool is_ignore_search_case() const
	{
		return is_ignore_case;
	}

	[[nodiscard]] std::string get_search_text() const
	{
		return search_text;
	}

	[[nodiscard]] const std::vector<std::string>* get_keywords() const
	{
		return &keywords;
	}

	[[nodiscard]] const std::vector<std::string>* get_keywords_lower_case() const
	{
		return &keywords_lower_case;
	}

	[[nodiscard]] const std::vector<bool>* is_keyword_path_value() const
	{
		return &is_keyword_path;
	}

	[[nodiscard]] bool is_enable_fuzzy_match_value() const
	{
		return is_enable_fuzzy_match;
	}

private:
	int search_case_num;
	bool is_ignore_case;
	std::string search_text;
	std::vector<std::string> keywords;
	std::vector<std::string> keywords_lower_case;
	std::vector<bool> is_keyword_path;
	bool is_enable_fuzzy_match;
};

using search_result = struct tmp_search_result
{
	std::string path;
	std::chrono::system_clock::time_point modify_date;
	size_t file_size = 0;
	bool fuzzy_matched = false;
};

static constexpr int spell_value[] = {
	-20319, -20317, -20304, -20295, -20292, -20283, -20265, -20257, -20242, -20230, -20051, -20036, -20032, -20026,
	-20002, -19990, -19986, -19982, -19976, -19805, -19784, -19775, -19774, -19763, -19756, -19751, -19746, -19741,
	-19739, -19728,
	-19725, -19715, -19540, -19531, -19525, -19515, -19500, -19484, -19479, -19467, -19289, -19288, -19281, -19275,
	-19270, -19263,
	-19261, -19249, -19243, -19242, -19238, -19235, -19227, -19224, -19218, -19212, -19038, -19023, -19018, -19006,
	-19003, -18996,
	-18977, -18961, -18952, -18783, -18774, -18773, -18763, -18756, -18741, -18735, -18731, -18722, -18710, -18697,
	-18696, -18526,
	-18518, -18501, -18490, -18478, -18463, -18448, -18447, -18446, -18239, -18237, -18231, -18220, -18211, -18201,
	-18184, -18183,
	-18181, -18012, -17997, -17988, -17970, -17964, -17961, -17950, -17947, -17931, -17928, -17922, -17759, -17752,
	-17733, -17730,
	-17721, -17703, -17701, -17697, -17692, -17683, -17676, -17496, -17487, -17482, -17468, -17454, -17433, -17427,
	-17417, -17202,
	-17185, -16983, -16970, -16942, -16915, -16733, -16708, -16706, -16689, -16664, -16657, -16647, -16474, -16470,
	-16465, -16459,
	-16452, -16448, -16433, -16429, -16427, -16423, -16419, -16412, -16407, -16403, -16401, -16393, -16220, -16216,
	-16212, -16205,
	-16202, -16187, -16180, -16171, -16169, -16158, -16155, -15959, -15958, -15944, -15933, -15920, -15915, -15903,
	-15889, -15878,
	-15707, -15701, -15681, -15667, -15661, -15659, -15652, -15640, -15631, -15625, -15454, -15448, -15436, -15435,
	-15419, -15416,
	-15408, -15394, -15385, -15377, -15375, -15369, -15363, -15362, -15183, -15180, -15165, -15158, -15153, -15150,
	-15149, -15144,
	-15143, -15141, -15140, -15139, -15128, -15121, -15119, -15117, -15110, -15109, -14941, -14937, -14933, -14930,
	-14929, -14928,
	-14926, -14922, -14921, -14914, -14908, -14902, -14894, -14889, -14882, -14873, -14871, -14857, -14678, -14674,
	-14670, -14668,
	-14663, -14654, -14645, -14630, -14594, -14429, -14407, -14399, -14384, -14379, -14368, -14355, -14353, -14345,
	-14170, -14159,
	-14151, -14149, -14145, -14140, -14137, -14135, -14125, -14123, -14122, -14112, -14109, -14099, -14097, -14094,
	-14092, -14090,
	-14087, -14083, -13917, -13914, -13910, -13907, -13906, -13905, -13896, -13894, -13878, -13870, -13859, -13847,
	-13831, -13658,
	-13611, -13601, -13406, -13404, -13400, -13398, -13395, -13391, -13387, -13383, -13367, -13359, -13356, -13343,
	-13340, -13329,
	-13326, -13318, -13147, -13138, -13120, -13107, -13096, -13095, -13091, -13076, -13068, -13063, -13060, -12888,
	-12875, -12871,
	-12860, -12858, -12852, -12849, -12838, -12831, -12829, -12812, -12802, -12607, -12597, -12594, -12585, -12556,
	-12359, -12346,
	-12320, -12300, -12120, -12099, -12089, -12074, -12067, -12058, -12039, -11867, -11861, -11847, -11831, -11798,
	-11781, -11604,
	-11589, -11536, -11358, -11340, -11339, -11324, -11303, -11097, -11077, -11067, -11055, -11052, -11045, -11041,
	-11038, -11024,
	-11020, -11019, -11018, -11014, -10838, -10832, -10815, -10800, -10790, -10780, -10764, -10587, -10544, -10533,
	-10519, -10331,
	-10329, -10328, -10322, -10315, -10309, -10307, -10296, -10281, -10274, -10270, -10262, -10260, -10256, -10254
};

static constexpr char spell_dict[396][7] = {
	"a", "ai", "an", "ang", "ao", "ba", "bai", "ban", "bang", "bao", "bei", "ben", "beng", "bi", "bian", "biao",
	"bie", "bin", "bing", "bo", "bu", "ca", "cai", "can", "cang", "cao", "ce", "ceng", "cha", "chai", "chan",
	"chang", "chao", "che", "chen",
	"cheng", "chi", "chong", "chou", "chu", "chuai", "chuan", "chuang", "chui", "chun", "chuo", "ci", "cong", "cou",
	"cu", "cuan", "cui",
	"cun", "cuo", "da", "dai", "dan", "dang", "dao", "de", "deng", "di", "dian", "diao", "die", "ding", "diu",
	"dong", "dou", "du", "duan",
	"dui", "dun", "duo", "e", "en", "er", "fa", "fan", "fang", "fei", "fen", "feng", "fo", "fou", "fu", "ga", "gai",
	"gan", "gang", "gao",
	"ge", "gei", "gen", "geng", "gong", "gou", "gu", "gua", "guai", "guan", "guang", "gui", "gun", "guo", "ha",
	"hai", "han", "hang",
	"hao", "he", "hei", "hen", "heng", "hong", "hou", "hu", "hua", "huai", "huan", "huang", "hui", "hun", "huo",
	"ji", "jia", "jian",
	"jiang", "jiao", "jie", "jin", "jing", "jiong", "jiu", "ju", "juan", "jue", "jun", "ka", "kai", "kan", "kang",
	"kao", "ke", "ken",
	"keng", "kong", "kou", "ku", "kua", "kuai", "kuan", "kuang", "kui", "kun", "kuo", "la", "lai", "lan", "lang",
	"lao", "le", "lei",
	"leng", "li", "lia", "lian", "liang", "liao", "lie", "lin", "ling", "liu", "long", "lou", "lu", "lv", "luan",
	"lue", "lun", "luo",
	"ma", "mai", "man", "mang", "mao", "me", "mei", "men", "meng", "mi", "mian", "miao", "mie", "min", "ming",
	"miu", "mo", "mou", "mu",
	"na", "nai", "nan", "nang", "nao", "ne", "nei", "nen", "neng", "ni", "nian", "niang", "niao", "nie", "nin",
	"ning", "niu", "nong",
	"nu", "nv", "nuan", "nue", "nuo", "o", "ou", "pa", "pai", "pan", "pang", "pao", "pei", "pen", "peng", "pi",
	"pian", "piao", "pie",
	"pin", "ping", "po", "pu", "qi", "qia", "qian", "qiang", "qiao", "qie", "qin", "qing", "qiong", "qiu", "qu",
	"quan", "que", "qun",
	"ran", "rang", "rao", "re", "ren", "reng", "ri", "rong", "rou", "ru", "ruan", "rui", "run", "ruo", "sa", "sai",
	"san", "sang",
	"sao", "se", "sen", "seng", "sha", "shai", "shan", "shang", "shao", "she", "shen", "sheng", "shi", "shou",
	"shu", "shua",
	"shuai", "shuan", "shuang", "shui", "shun", "shuo", "si", "song", "sou", "su", "suan", "sui", "sun", "suo",
	"ta", "tai",
	"tan", "tang", "tao", "te", "teng", "ti", "tian", "tiao", "tie", "ting", "tong", "tou", "tu", "tuan", "tui",
	"tun", "tuo",
	"wa", "wai", "wan", "wang", "wei", "wen", "weng", "wo", "wu", "xi", "xia", "xian", "xiang", "xiao", "xie",
	"xin", "xing",
	"xiong", "xiu", "xu", "xuan", "xue", "xun", "ya", "yan", "yang", "yao", "ye", "yi", "yin", "ying", "yo", "yong",
	"you",
	"yu", "yuan", "yue", "yun", "za", "zai", "zan", "zang", "zao", "ze", "zei", "zen", "zeng", "zha", "zhai",
	"zhan", "zhang",
	"zhao", "zhe", "zhen", "zheng", "zhi", "zhong", "zhou", "zhu", "zhua", "zhuai", "zhuan", "zhuang", "zhui",
	"zhun", "zhuo",
	"zi", "zong", "zou", "zu", "zuan", "zui", "zun", "zuo"
};

// -1 匹配失败  0 全字匹配  1 模糊匹配
int not_matched(const char parent_path[MAX_PATH_LENGTH],
	const char file_name[MAX_PATH_LENGTH],
	bool is_ignore_case,
	const std::vector<std::string>& keywords,
	const std::vector<std::string>& keywords_lower_case,
	int keywords_length,
	const std::vector<bool>* is_keyword_path,
	bool enable_fuzzy_match);

// -1 匹配失败  0 全字匹配  1 模糊匹配
int match_func(const char parent_path_arr[MAX_PATH_LENGTH], const char file_name_arr[MAX_PATH_LENGTH], const search_info* info);

bool is_str_contains_chinese(const char* source);

void convert_to_pinyin(const char* chinese_str, char* output_str, const size_t output_size,
	char* pinyin_initials, const size_t pinyin_init_size);

int is_dir_or_file(const char* path);

inline bool is_file_exist(const std::string& path);

std::chrono::system_clock::time_point decode_file_time(uint64_t timestamp);

bool pattern_char_match(const std::string_view& pattern, const std::string_view& str);

bool fuzzy_match(const std::string& pattern, const std::string& str);

size_t calc_str_distance(const std::string& pattern, const std::string& str);

void is_path_matched_keywords(const path_match_struct& path_info,
	search_info* search_info,
	Concurrency::concurrent_vector<search_result*>& matched_path_vec);

std::wstring string2wstring(const std::string& str);
