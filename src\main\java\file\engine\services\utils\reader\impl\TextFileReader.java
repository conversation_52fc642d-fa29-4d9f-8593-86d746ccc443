package file.engine.services.utils.reader.impl;

import file.engine.services.utils.reader.ContentReader;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.MalformedInputException;
import java.nio.file.Files;
import java.nio.file.Path;

@Slf4j
public class TextFileReader implements ContentReader {

    @Override
    public String readContent(String fileAbsPath) {
        try {
            Path path = Path.of(fileAbsPath);
            String mimeType = Files.probeContentType(path);
            if (mimeType != null) {
                if (mimeType.startsWith("text/") || mimeType.startsWith("application/json")) {
                    try {
                        return Files.readString(path);
                    } catch (MalformedInputException e) {
                        StringBuilder stringBuilder = new StringBuilder();
                        try (var reader = new BufferedReader(new FileReader(fileAbsPath,
                                Charset.forName(System.getProperty("sun.jnu.encoding"))))) {
                            String line;
                            while ((line = reader.readLine()) != null) {
                                stringBuilder.append(line);
                                stringBuilder.append("\n");
                            }
                            return stringBuilder.toString();
                        } catch (IOException ignored) {
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("error {}", e.getMessage(), e);
        }
        return "";
    }
}
