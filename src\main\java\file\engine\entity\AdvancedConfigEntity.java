package file.engine.entity;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class AdvancedConfigEntity {

    private long waitForSearchTasksTimeoutInMills;

    private boolean isDeleteUsnOnExit;

    private long restartMonitorDiskThreadTimeoutInMills;

    private boolean isReadPictureByLLM;

    private boolean isEnableContentIndex;

    private int minCacheBlockNumber;

    private int maxCacheBlockNumber;

    private int minGpuCacheBlockNumber;

    private int maxGpuCacheBlockNumber;
}
