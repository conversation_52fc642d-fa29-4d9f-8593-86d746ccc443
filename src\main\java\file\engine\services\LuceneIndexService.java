package file.engine.services;

import file.engine.annotation.EventListener;
import file.engine.configs.AllConfigs;
import file.engine.configs.Constants;
import file.engine.dllInterface.Encryption;
import file.engine.event.handler.Event;
import file.engine.event.handler.EventManagement;
import file.engine.event.handler.impl.BootSystemEvent;
import file.engine.event.handler.impl.database.StartSearchEvent;
import file.engine.services.utils.LuceneIndexUtil;
import file.engine.services.utils.PathMatchUtil;
import file.engine.services.utils.connection.SQLiteUtil;
import file.engine.utils.RegexUtil;
import file.engine.utils.ThreadPoolUtil;
import file.engine.utils.file.FileUtil;
import file.engine.utils.system.properties.IsDebug;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.document.Document;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Base64;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

@Slf4j
public class LuceneIndexService {

    private static final ConcurrentHashMap<String, LuceneIndexUtil> luceneIndexMap = new ConcurrentHashMap<>();

    @SneakyThrows
    @EventListener(listenClass = BootSystemEvent.class, runAfter = DatabaseService.class)
    private static void startLucene(Event event) {
        if (!AllConfigs.getInstance().getConfigEntity().getAdvancedConfigEntity().isEnableContentIndex()) {
            return;
        }
        File[] files = new File(Constants.LUCENE_DATA_BASE_DIR).listFiles();
        if (files != null && files.length != 0) {
            log.info("index has been created");
        }
        String availableDisks = AllConfigs.getInstance().getAvailableDisks();
        String[] disks = RegexUtil.comma.split(availableDisks);
        for (String disk : disks) {
            if (disk == null || disk.isBlank()) {
                continue;
            }
            // 取第一个字符
            String diskChar = String.valueOf(disk.charAt(0));
            LuceneIndexUtil luceneIndexUtil = new LuceneIndexUtil(Constants.LUCENE_DATA_BASE_DIR + diskChar);
            setRefreshThread(luceneIndexUtil);
            luceneIndexMap.put(diskChar, luceneIndexUtil);
        }
        createIndex();
    }

    public static void writeLuceneScannedFile(List<String> scannedKeys) throws IOException {
        try {
            String content;
            if (scannedKeys == null || scannedKeys.isEmpty()) {
                content = "";
            } else {
                StringBuilder sb = new StringBuilder();
                for (String scannedKey : scannedKeys) {
                    sb.append(scannedKey).append("\n");
                }
                content = sb.toString();
            }

            // 使用Encryption API获取密钥并加密内容
            String encryptionKey = Encryption.INSTANCE.getkey();
            String encryptedContent = encryptString(content, encryptionKey);

            try (var writer = new BufferedWriter(new OutputStreamWriter(
                    new FileOutputStream(Constants.LUCENE_SCANNED_DATABASE_RECORD_FILE), StandardCharsets.UTF_8))) {
                writer.write(encryptedContent);
            }

            if (IsDebug.isDebug) {
                log.info("Lucene扫描记录已加密保存到文件: {}", Constants.LUCENE_SCANNED_DATABASE_RECORD_FILE);
            }
        } catch (Exception e) {
            log.error("写入加密的Lucene扫描文件失败: {}", e.getMessage(), e);
            throw new IOException("写入加密的Lucene扫描文件失败", e);
        }
    }

    private static List<String> readLuceneScannedFile() {
        try {
            if (FileUtil.isFileNotExist(Constants.LUCENE_SCANNED_DATABASE_RECORD_FILE)) {
                return Collections.emptyList();
            }

            // 读取加密的文件内容
            String encryptedContent;
            try (var reader = new BufferedReader(new InputStreamReader(
                    new FileInputStream(Constants.LUCENE_SCANNED_DATABASE_RECORD_FILE), StandardCharsets.UTF_8))) {
                StringBuilder sb = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    sb.append(line);
                }
                encryptedContent = sb.toString();
            }

            if (encryptedContent.isEmpty()) {
                return Collections.emptyList();
            }

            // 使用Encryption API获取密钥并解密内容
            String encryptionKey = Encryption.INSTANCE.getkey();
            String decryptedContent = decryptString(encryptedContent, encryptionKey);

            if (decryptedContent.isEmpty()) {
                return Collections.emptyList();
            }

            // 按行分割内容
            return Arrays.asList(decryptedContent.split("\n"));

        } catch (Exception e) {
            log.error("读取加密的Lucene扫描文件失败: {}", e.getMessage(), e);
            // 如果是解密失败，可能是文件损坏，删除文件重新开始
            try {
                Files.deleteIfExists(Path.of(Constants.LUCENE_SCANNED_DATABASE_RECORD_FILE));
            } catch (IOException ex) {
                log.warn("删除损坏的Lucene扫描缓存文件失败: {}", ex.getMessage());
            }
            return Collections.emptyList();
        }
    }

    private static ArrayList<String> scanDatabaseAndCreateCache(String disk, LuceneIndexUtil luceneIndexUtil, List<String> scannedKeys) {
        if (luceneIndexUtil.getIndexingFlag() == LuceneIndexUtil.IndexStatus.INDEXING) {
            return new ArrayList<>(scannedKeys);
        }
        luceneIndexUtil.setIndexingFlag(LuceneIndexUtil.IndexStatus.INDEXING);
        log.info("start to create lucene cache for disk {}", disk);
        ArrayList<String> scannedKeysList = new ArrayList<>(scannedKeys);

        try (var conn = SQLiteUtil.getDbConnection(disk)) {
            HashMap<String, Integer> priorityMap = DatabaseService.getInstance().getPriorityMap();
            EventManagement eventManagement = EventManagement.getInstance();
            for (int i = 0; i <= Constants.MAX_TABLE_NUM; i++) {
                if (luceneIndexUtil.getIndexingFlag() == LuceneIndexUtil.IndexStatus.STOPPING) {
                    break;
                }
                var queryTemplate = "SELECT folder.PATH || NAME AS PATH FROM list" + i +
                                    " INNER JOIN folder ON folder.ID = " + "list" + i + ".FOLDER_ID" + " " + "WHERE PRIORITY=";
                try (var stmt = conn.createStatement()) {
                    for (Map.Entry<String, Integer> entry : priorityMap.entrySet()) {
                        if (luceneIndexUtil.getIndexingFlag() == LuceneIndexUtil.IndexStatus.STOPPING) {
                            break;
                        }
                        Integer priority = entry.getValue();
                        String key = disk + "," + "list" + i + "," + priority;
                        if (scannedKeysList.contains(key)) {
                            continue;
                        }
                        try (ResultSet resultSet = stmt.executeQuery(queryTemplate + priority)) {
                            int countLength = 0;
                            while (resultSet.next() &&
                                   eventManagement.notMainExit() &&
                                   luceneIndexUtil.getIndexingFlag() != LuceneIndexUtil.IndexStatus.STOPPING) {
                                String path = resultSet.getString("PATH");
                                int length = luceneIndexUtil.indexFile(new File(path));
                                countLength += length;
                                if (countLength > Constants.MAX_SINGLE_FILE_SIZE_LUCENE) {
                                    luceneIndexUtil.commit();
                                    countLength = 0;
                                }
                            }
                        } finally {
                            scannedKeysList.add(key);
                        }
                    }
                } catch (SQLException sqlException) {
                    log.error("error {}", sqlException.getMessage(), sqlException);
                }
            }
            luceneIndexUtil.commit();
        } catch (Exception e) {
            log.error("error: {}", e.getMessage(), e);
        } finally {
            luceneIndexUtil.setIndexingFlag(LuceneIndexUtil.IndexStatus.IDLE);
        }
        log.info("finish creating lucene cache for disk {}", disk);
        return scannedKeysList;
    }

    public static void createIndex() {
        ArrayList<String> scannedKeys = new ArrayList<>(readLuceneScannedFile());
        try {
            for (Map.Entry<String, LuceneIndexUtil> entry : luceneIndexMap.entrySet()) {
                String disk = entry.getKey();
                LuceneIndexUtil luceneIndexUtil = entry.getValue();
                scannedKeys = scanDatabaseAndCreateCache(disk, luceneIndexUtil, scannedKeys);
            }
        } catch (Exception e) {
            log.error("error {}", e.getMessage(), e);
        } finally {
            try {
                writeLuceneScannedFile(scannedKeys);
            } catch (IOException e) {
                log.error("error {}", e.getMessage(), e);
            }
        }
    }

    public static void stopAllIndex() {
        luceneIndexMap.forEach((k, v) -> v.setIndexingFlag(LuceneIndexUtil.IndexStatus.STOPPING));
    }

    public static boolean isIndexStopped() {
        for (Map.Entry<String, LuceneIndexUtil> entry : luceneIndexMap.entrySet()) {
            LuceneIndexUtil v = entry.getValue();
            if (v.getIndexingFlag() != LuceneIndexUtil.IndexStatus.IDLE) {
                return false;
            }
        }
        return true;
    }

    public static void addToIndex(String path) {
        String diskChar = String.valueOf(path.charAt(0));
        LuceneIndexUtil luceneIndexUtil = luceneIndexMap.get(diskChar);
        if (luceneIndexUtil == null) {
            return;
        }
        luceneIndexUtil.indexFile(new File(path));
    }

    public static void removeFromIndex(String path) {
        String diskChar = String.valueOf(path.charAt(0));
        LuceneIndexUtil luceneIndexUtil = luceneIndexMap.get(diskChar);
        if (luceneIndexUtil == null) {
            return;
        }
        luceneIndexUtil.remove(new File(path));
    }

    @EventListener(listenClass = StartSearchEvent.class)
    private static void startSearch(Event event) {
        StartSearchEvent startSearchEvent = (StartSearchEvent) event;
        startSearchEvent.getReturnValue().ifPresent(ret -> {
            var searchTask = (DatabaseService.SearchTask) ret;
            List<String> searchCaseList = startSearchEvent.searchCase.get() == null ?
                    Collections.emptyList() : Arrays.asList(startSearchEvent.searchCase.get());
            if (searchCaseList.contains(PathMatchUtil.SearchCase.DIRECTORY) ||
                !AllConfigs.getInstance().getConfigEntity().getAdvancedConfigEntity().isEnableContentIndex()) {
                searchTask.setFullTextSearchDone(true);
                return;
            }

            int maxResultNumber = startSearchEvent.maxResultNum / luceneIndexMap.size();
            CountDownLatch countDownLatch = new CountDownLatch(luceneIndexMap.size());
            luceneIndexMap.forEach((diskChar, luceneIndexUtil) ->
                    ThreadPoolUtil.INSTANCE.executeTask(() -> {
                        try {
                            List<Document> search = luceneIndexUtil.search(startSearchEvent.searchText.get(), maxResultNumber);
                            List<String> list = search.stream().map(doc -> doc.get(LuceneIndexUtil.FILE_FULL_PATH_FILED_NAME)).toList();
                            searchTask.addResultsToTempList(list, true);
                        } finally {
                            countDownLatch.countDown();
                        }
                    }));
            ThreadPoolUtil.INSTANCE.executeTask(() -> {
                try {
                    if (!countDownLatch.await(60, TimeUnit.SECONDS)) {
                        log.error("search task timed out");
                    }
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                } finally {
                    searchTask.setFullTextSearchDone(true);
                }
            });
        });
    }

    private static void setRefreshThread(LuceneIndexUtil luceneIndexUtil) {
        ThreadPoolUtil.INSTANCE.executeTask(() -> {
            EventManagement eventManagement = EventManagement.getInstance();
            int count = 0;
            while (eventManagement.notMainExit()) {
                ++count;
                if (count >= 5) {
                    count = 0;
                    try {
                        luceneIndexUtil.maybeRefresh();
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }
                try {
                    TimeUnit.SECONDS.sleep(1);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        });
    }

    /**
     * 使用AES加密字符串
     *
     * @param plainText 明文
     * @param key       密钥
     * @return 加密后的Base64字符串
     */
    private static String encryptString(String plainText, String key) throws Exception {
        // 确保密钥长度为16字节（AES-128）
        String aesKey = key;
        if (aesKey.length() > 16) {
            aesKey = aesKey.substring(0, 16);
        } else if (aesKey.length() < 16) {
            // 如果密钥不足16位，用0填充
            StringBuilder sb = new StringBuilder(aesKey);
            while (sb.length() < 16) {
                sb.append('0');
            }
            aesKey = sb.toString();
        }

        SecretKeySpec secretKey = new SecretKeySpec(aesKey.getBytes(StandardCharsets.UTF_8), "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    /**
     * 使用AES解密字符串
     *
     * @param encryptedText 加密的Base64字符串
     * @param key           密钥
     * @return 解密后的明文
     */
    private static String decryptString(String encryptedText, String key) throws Exception {
        // 确保密钥长度为16字节（AES-128）
        String aesKey = key;
        if (aesKey.length() > 16) {
            aesKey = aesKey.substring(0, 16);
        } else if (aesKey.length() < 16) {
            // 如果密钥不足16位，用0填充
            StringBuilder sb = new StringBuilder(aesKey);
            while (sb.length() < 16) {
                sb.append('0');
            }
            aesKey = sb.toString();
        }

        SecretKeySpec secretKey = new SecretKeySpec(aesKey.getBytes(StandardCharsets.UTF_8), "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedText));
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }
}
