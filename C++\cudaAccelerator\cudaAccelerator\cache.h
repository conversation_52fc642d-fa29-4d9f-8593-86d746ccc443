﻿#pragma once
#include <atomic>
#include <string>
#include <concurrent_unordered_set.h>

/**
 * \brief 存储数据结构
 * dev_strs: 保存在显存中的当前缓存中的字符串
 * str_total_bytes: 总共占用多少字节
 * str_remain_blank_bytes：当前dev_strs有多少空闲空间
 * record_num：当前有多少个record
 * dev_str_addr: dev_strs中每个字符串的起始地址
 * str_length_array: dev_strs中每个字符串的长度，str_length_array size(即str_addr_capacity)等于dev_str_addr size
 * str_addr_capacity: dev_strs 最多可以存下多少个字符串，也等于str_length_array的size，值为record_num + CACHE_REMAIN_BLANK_SIZE_IN_BYTES / MAX_PATH_LENGTH
 *                    当新添加的缓存str长度小于MAX_PATH_LENGTH，使得record_num大于str_addr_capacity，然而str_remain_blank_bytes仍大于0，即还有剩余空间时
 *                    将会重新计算str_addr_capacity以及str_length_array，避免内存浪费。
 *                    详情参见add_records_to_cache()函数 if (cache->str_data.str_addr_capacity <= index)分支
 * global_parent_path_index_array: dev_strs中每个字符串对应的前缀在global_cache中的内存地址
 *                                   global_parent_path_index_array size 等于dev_str_addr size
 * record_hash：每个record的hash，用于判断重复
 * 
 * global_parent_path_index_array实现对每个文件路径的文件夹路径进行存储，dev_strs中只存文件名，所有的文件夹路径
 * 存放到全局缓存中进行重用。
 * global_parent_path_index_array每一个对象为size_t，对应dev_strs中每个字符串的parent path对应的hash
 * global_parent_path_index_array[i]取出来的值通过global_cache::get_address_by_hash(hash)获取显存中对应的字符串首地址指针，
 * reinterpret为const char*之后即为dev_str_addr[i]的前缀
 * global_cache::get_address_by_hash(global_parent_path_index_array[i]) + dev_str_addr[i]即为文件路径
 */
using cache_data = struct cache_data
{
    char* dev_strs = nullptr;
    size_t str_total_bytes = 0;
    size_t str_remain_blank_bytes = 0;
    std::atomic_uint64_t record_num;
    size_t* dev_str_addr = nullptr;
    size_t str_addr_capacity = 0;
    unsigned short* str_length_array = nullptr;
    size_t* global_parent_path_index_array = nullptr;
    concurrency::concurrent_unordered_set<size_t> record_hash;
};

using collected_result_data = struct collected_results_struct
{
    std::string key;
    std::string result;
};

/**
 * \brief 缓存struct
 * str_data：数据struct
 * dev_output：字符串匹配后输出位置，下标与cache_data中一一对应，dev_output中数据为1代表匹配成功
 * is_cache_valid：数据是否有效
 * is_match_done：是否匹配全部完成 
 * is_output_done：是否已经存入容器 0 代表没有开始  1 代表正在收集  2代表完成
 */
using list_cache = struct cache_struct
{
    cache_data str_data;
    char* dev_output_bitmap = nullptr;
    size_t output_bitmap_size = 0;
    bool is_cache_valid = false;
    std::atomic_bool is_match_done;
    std::atomic_int is_output_done;
    unsigned matched_number = 0;
};


using stop_signal = struct stop_signal_struct
{
    bool is_stop_collect = false;
    bool* dev_is_stop_collect = nullptr;
};

std::string get_cache_info(const std::string& key, const list_cache* cache);
bool is_stop();
void set_stop(bool b);
void init_stop_signal();
bool* get_dev_stop_signal();
void free_stop_signal();
