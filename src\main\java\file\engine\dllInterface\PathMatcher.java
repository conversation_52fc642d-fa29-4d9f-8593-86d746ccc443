package file.engine.dllInterface;

import file.engine.entity.SearchResult;
import file.engine.entity.UwpResult;

import java.nio.file.Path;
import java.util.function.Consumer;

public enum PathMatcher {
    INSTANCE;

    static {
        System.load(Path.of("pathMatcher.dll").toAbsolutePath().toString());
    }

    public native long prepareSearchInfo(String[] searchCase,
                                         boolean isIgnoreCase,
                                         String searchText,
                                         String[] keywords,
                                         String[] keywordsLowerCase,
                                         boolean[] isKeywordPath,
                                         boolean isFuzzyMatchEnable);

    public native void releaseSearchInfo(long preparedSearchInfoId);

    public native void match(String sql,
                             String dbPath,
                             long preparedSearchInfoId,
                             int maxResultNumber,
                             long connectionId,
                             int threadId,
                             Consumer<SearchResult> resultConsumer);

    public native long openConnections(String[] dbPath);

    public native void closeConnections(long connectionId);

    /**
     * 必须和match函数一致，也使用consumer接口，JNI中对class进行了复用
     *
     * @param uwpResultConsumer uwp result consumer
     */
    public native void iterateUwpApps(Consumer<UwpResult> uwpResultConsumer);

    public native void refreshUwpApps();

    public native void cleanupUwpApps();
}
