﻿// dllmain.cpp : 定义 DLL 应用程序的入口点。
#include "BS_thread_pool.hpp"
#include "framework.h"
#include <concurrent_unordered_map.h>
#include "kernels.cuh"
#include "file_engine_dllInterface_gpu_CudaAccelerator.h"
#include "cache.h"
#include "constans.h"
#include "cuda_copy_vector_util.h"
#include "str_convert.cuh"
#include <Shlwapi.h>
#include <Pdh.h>
#include <unordered_map>
#include <d3d.h>
#include <dxgi.h>
#include <shared_mutex>
#include <concurrent_queue.h>
#include "global_cache.h"
#include <concurrent_vector.h>
#ifdef DEBUG_OUTPUT
#include <iostream>
#endif

#pragma comment(lib, "cudart.lib")
#pragma comment(lib, "Shlwapi.lib")
#pragma comment(lib, "dxgi.lib")
#pragma comment(lib, "pdh.lib")

void clear_cache(const std::string& key);
void clear_all_cache();
bool has_cache(const std::string& key);
void add_records_to_cache(const std::string& key, const std::vector<std::string>& records);
void remove_records_from_cache(const std::string& key, std::vector<std::string>& records);
void generate_search_case(JNIEnv* env, std::vector<std::string>& search_case_vec, jobjectArray search_case);
void collect_results(std::atomic_uint& result_counter,
	const unsigned max_results,
	const std::vector<std::string>& search_case_vec,
	Concurrency::concurrent_vector<collected_result_data>& collected_results);
bool is_record_repeat(const std::string& record, const list_cache* cache);
void release_all();
int is_dir_or_file(const char* path);
inline bool is_file_exist(const char* path);
std::wstring string2wstring(const std::string& str);
void create_and_insert_cache(const std::vector<std::string>& records_vec, size_t total_bytes, const std::string& key);

std::shared_mutex cache_lock;
concurrency::concurrent_unordered_map<std::wstring, DXGI_ADAPTER_DESC> gpu_name_adapter_map;
concurrency::concurrent_unordered_map<std::string, list_cache*> cache_map;
std::hash<std::string> hasher;
std::atomic_bool exit_flag = false;
static int current_using_device = 0;
std::atomic_bool is_results_number_exceed = false;
static JavaVM* jvm;
IDXGIFactory* p_dxgi_factory = nullptr;

static BS::thread_pool<>* collect_results_thread_pool_ptr;
static std::once_flag init_thread_pool_flag;
static jclass g_bi_consumer_class;
static jmethodID g_bi_consumer_collector;


/*
 * Class:     file_engine_dllInterface_gpu_CudaAccelerator
 * Method:    getDevices
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jobjectArray JNICALL Java_file_engine_dllInterface_gpu_CudaAccelerator_getDevices
(JNIEnv* env, jobject)
{
	int device_count = 0;
	std::vector<std::string> device_vec;
	gpuErrchk(cudaGetDeviceCount(&device_count), false, "get device number failed.");
	for (int i = 0; i < device_count; ++i)
	{
		cudaDeviceProp prop;
		gpuErrchk(cudaGetDeviceProperties(&prop, i), false, "get device info failed.");
		device_vec.emplace_back(prop.name);
	}
	auto&& string_clazz = env->FindClass("java/lang/String");
	auto&& gpu_device_count = device_vec.size();
	auto&& object_arr = env->NewObjectArray(static_cast<jsize>(gpu_device_count), string_clazz, nullptr);
	for (UINT i = 0; i < gpu_device_count; ++i)
	{
		auto&& device_name = device_vec[i];
		env->SetObjectArrayElement(object_arr, i, env->NewStringUTF(device_name.c_str()));
	}
	env->DeleteLocalRef(string_clazz);
	return object_arr;
}

/*
 * Class:     file_engine_dllInterface_gpu_CudaAccelerator
 * Method:    setDevice
 * Signature: (I)Z
 */
JNIEXPORT jboolean JNICALL Java_file_engine_dllInterface_gpu_CudaAccelerator_setDevice
(JNIEnv*, jobject, jint device_number_jint)
{
	if (device_number_jint == current_using_device)
	{
		return true;
	}
	std::unique_lock ulck(cache_lock);
	release_all();
	if (set_using_device(device_number_jint))
	{
		current_using_device = device_number_jint;
		init_stop_signal();
		init_cuda_search_memory();
		init_str_convert();
		return true;
	}
	return false;
}

/*
 * Class:     file_engine_dllInterface_gpu_CudaAccelerator
 * Method:    release
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_gpu_CudaAccelerator_release
(JNIEnv*, jobject)
{
	std::unique_lock ulck(cache_lock);
	exit_flag = true;
	release_all();
}

/*
 * Class:     file_engine_dllInterface_gpu_CudaAccelerator
 * Method:    initialize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_gpu_CudaAccelerator_initialize
(JNIEnv* env, jobject)
{
	init_stop_signal();
	init_cuda_search_memory();
	init_str_convert();
	//默认使用第一个设备,current_using_device=0
	set_using_device(current_using_device);
	if (env->GetJavaVM(&jvm) != JNI_OK)
	{
		env->ThrowNew(env->FindClass("java/lang/Exception"), "get JavaVM ptr failed.");
		return;
	}
	set_jvm_ptr_in_kernel(jvm);
	if (CreateDXGIFactory(__uuidof(IDXGIFactory), reinterpret_cast<void**>(&p_dxgi_factory)) != S_OK)
	{
		env->ThrowNew(env->FindClass("java/lang/Exception"), "create dxgi factory failed.");
		return;
	}
	IDXGIAdapter* p_adapter = nullptr;
	for (UINT i = 0;
		p_dxgi_factory->EnumAdapters(i, &p_adapter) != DXGI_ERROR_NOT_FOUND;
		++i)
	{
		DXGI_ADAPTER_DESC adapter_desc;
		p_adapter->GetDesc(&adapter_desc);
		gpu_name_adapter_map.insert(std::make_pair(adapter_desc.Description, adapter_desc));
	}
	global_cache::init(128);
}

/*
 * Class:     file_engine_dllInterface_gpu_CudaAccelerator
 * Method:    resetAllResultStatus
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_gpu_CudaAccelerator_resetAllResultStatus
(JNIEnv*, jobject)
{
	std::unique_lock ulck(cache_lock);
	set_stop(false);
	// 初始化is_match_done is_output_done为false
	for (auto& [_, cache_ptr] : cache_map)
	{
		cache_ptr->is_match_done = false;
		cache_ptr->is_output_done = 0;
		cache_ptr->matched_number = 0;
	}
	is_results_number_exceed = false;
}

/*
 * Class:     file_engine_dllInterface_gpu_CudaAccelerator
 * Method:    stopCollectResults
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_gpu_CudaAccelerator_stopCollectResults
(JNIEnv*, jobject)
{
	set_stop(true);
}

JNIEXPORT void JNICALL Java_file_engine_dllInterface_gpu_CudaAccelerator_match
(JNIEnv* env, jobject, jobjectArray search_case, jboolean is_ignore_case, jstring search_text,
	jobjectArray keywords, jobjectArray keywords_lower, jbooleanArray is_keyword_path, jint max_results,
	jint result_collect_thread_num, jboolean enable_fuzzy_match, jobject result_consumer)
{
	if (cache_map.empty())
	{
#ifdef DEBUG_OUTPUT
		std::cout << "cache map is empty" << std::endl;
#endif
		return;
	}

	std::call_once(init_thread_pool_flag, [&result_collect_thread_num, &env, &result_consumer]
		{
			// no need to delete
			collect_results_thread_pool_ptr = new BS::thread_pool(result_collect_thread_num);

			const jclass biconsumer_class = env->GetObjectClass(result_consumer);
			if (biconsumer_class == nullptr)
			{
				fprintf(stderr, "Get BiConsumer class failed\n");
				return;
			}
			g_bi_consumer_class = (jclass)env->NewGlobalRef(biconsumer_class);
			env->DeleteLocalRef(biconsumer_class);

			g_bi_consumer_collector = env->GetMethodID(g_bi_consumer_class, "accept",
				"(Ljava/lang/Object;Ljava/lang/Object;)V");
			if (g_bi_consumer_collector == nullptr)
			{
				fprintf(stderr, "Get BiConsumer accept function failed\n");
				return;
			}
		});

#ifdef DEBUG_OUTPUT
	std::cout << "start gpu search" << std::endl;
#endif
	std::unique_lock ulck(cache_lock);
#ifdef DEBUG_OUTPUT
	std::cout << "cache lock acquired" << std::endl;
#endif
	//生成搜索条件 search_case_vec
	std::vector<std::string> search_case_vec;
	if (search_case != nullptr)
	{
		generate_search_case(env, search_case_vec, search_case);
	}
#ifdef DEBUG_OUTPUT
	std::cout << "search case generated, size: " << search_case_vec.size() << std::endl;
#endif
	//生成搜索关键字 keywords_vec keywords_lower_vec is_keyword_path_ptr
	std::vector<std::string> keywords_vec;
	std::vector<std::string> keywords_lower_vec;
	if (keywords == nullptr || keywords_lower == nullptr)
	{
		fprintf(stderr, "%s\n", "keywords cannot be null");
		return;
	}
	const auto keywords_length = env->GetArrayLength(keywords);
	if (keywords_length > MAX_KEYWORDS_NUMBER)
	{
		fprintf(stderr, "%s\n", "too many keywords");
		return;
	}

	if (is_keyword_path == nullptr)
	{
		fprintf(stderr, "%s\n", "is keyword path aray cannot be null");
		return;
	}
	bool is_keyword_path_ptr[MAX_KEYWORDS_NUMBER]{ false };
	jboolean* is_keyword_path_ptr_bool_array = env->GetBooleanArrayElements(is_keyword_path, nullptr);
	if (is_keyword_path_ptr_bool_array == nullptr)
	{
		fprintf(stderr, "%s\n", "failed to get boolean array is_keyword_path");
		return;
	}
	for (jsize i = 0; i < keywords_length; ++i)
	{
		const auto keyword_object = env->GetObjectArrayElement(keywords, i);
		if (keyword_object != nullptr)
		{
			const auto tmp_keywords_str = reinterpret_cast<jstring>(keyword_object);
			const auto keywords_chars = env->GetStringUTFChars(tmp_keywords_str, nullptr);
			keywords_vec.emplace_back(keywords_chars);
			env->ReleaseStringUTFChars(tmp_keywords_str, keywords_chars);
			env->DeleteLocalRef(tmp_keywords_str);
		}
		else
		{
			keywords_vec.emplace_back("");
		}

		const auto keyword_object_lower = env->GetObjectArrayElement(keywords_lower, i);
		if (keyword_object_lower != nullptr)
		{
			const auto tmp_keywords_str = reinterpret_cast<jstring>(keyword_object_lower);
			const auto keywords_chars = env->GetStringUTFChars(tmp_keywords_str, nullptr);
			keywords_lower_vec.emplace_back(keywords_chars);
			env->ReleaseStringUTFChars(tmp_keywords_str, keywords_chars);
			env->DeleteLocalRef(tmp_keywords_str);
		}
		else
		{
			keywords_lower_vec.emplace_back("");
		}

#ifdef DEBUG_OUTPUT
		std::cout << "is keyword path: " << static_cast<bool>(is_keyword_path_ptr_bool_array[i]) << std::endl;
#endif
		is_keyword_path_ptr[i] = is_keyword_path_ptr_bool_array[i];
	}
	env->ReleaseBooleanArrayElements(is_keyword_path, is_keyword_path_ptr_bool_array, JNI_ABORT);
	//复制全字匹配字符串 search_text
	if (search_text == nullptr)
	{
		fprintf(stderr, "search text cannot be null");
		return;
	}
	const auto search_text_chars = env->GetStringUTFChars(search_text, nullptr);
	if (search_text_chars == nullptr)
	{
		fprintf(stderr, "get search text failed");
		return;
	}
	std::atomic_uint result_counter = 0;

	std::vector<std::future<void>> future_vec;

	Concurrency::concurrent_vector<collected_result_data> collected_results_vec;

	for (auto i = 0; i < result_collect_thread_num; ++i)
	{
		future_vec.emplace_back(collect_results_thread_pool_ptr->submit_task([&]
			{
				collect_results(result_counter, max_results, search_case_vec, collected_results_vec);
			}));
	}

	//GPU并行计算
	start_kernel(cache_map, search_case_vec, is_ignore_case, search_text_chars,
		keywords_vec, keywords_lower_vec, is_keyword_path_ptr, enable_fuzzy_match);

	for (auto&& each_future : future_vec)
	{
		each_future.wait();
	}

	if (!collected_results_vec.empty())
	{
		for (auto&& each_record : collected_results_vec)
		{
			const auto record_jstring = env->NewStringUTF(each_record.result.c_str());
			const auto key_jstring = env->NewStringUTF(each_record.key.c_str());

			env->CallVoidMethod(result_consumer, g_bi_consumer_collector, key_jstring, record_jstring);

			env->DeleteLocalRef(record_jstring);
			env->DeleteLocalRef(key_jstring);
		}
	}

	for (auto& [_, cache_val] : cache_map)
	{
		if (cache_val->is_output_done.load() != 2)
		{
			cache_val->is_output_done = 2;
		}
	}

	env->ReleaseStringUTFChars(search_text, search_text_chars);
}

/*
 * Class:     file_engine_dllInterface_gpu_CudaAccelerator
 * Method:    isMatchDone
 * Signature: (Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_file_engine_dllInterface_gpu_CudaAccelerator_isMatchDone
(JNIEnv* env, jobject, jstring key_jstring)
{
	if (is_results_number_exceed.load())
	{
		return true;
	}
	std::shared_lock lck(cache_lock);
	const auto _key = env->GetStringUTFChars(key_jstring, nullptr);
	auto iter = cache_map.find(_key);
	env->ReleaseStringUTFChars(key_jstring, _key);
	if (iter == cache_map.end())
	{
		return false;
	}
	return iter->second->is_output_done.load() == 2;
}

/*
 * Class:     file_engine_dllInterface_gpu_CudaAccelerator
 * Method:    matchedNumber
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_file_engine_dllInterface_gpu_CudaAccelerator_matchedNumber
(JNIEnv* env, jobject, jstring key_jstring)
{
	std::shared_lock lck(cache_lock);
	const auto key = env->GetStringUTFChars(key_jstring, nullptr);
	auto&& matched_number_iter = cache_map.find(key);
	env->ReleaseStringUTFChars(key_jstring, key);
	if (matched_number_iter == cache_map.end())
	{
		return 0;
	}
	auto&& matched_number = matched_number_iter->second;
	return static_cast<jint>(matched_number->matched_number);
}

/*
 * Class:     file_engine_dllInterface_gpu_CudaAccelerator
 * Method:    hasCache
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_file_engine_dllInterface_gpu_CudaAccelerator_hasCache
(JNIEnv*, jobject)
{
	return !cache_map.empty();
}

/*
 * Class:     file_engine_dllInterface_gpu_CudaAccelerator
 * Method:    isCacheExist
 * Signature: (Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_file_engine_dllInterface_gpu_CudaAccelerator_isCacheExist
(JNIEnv* env, jobject, jstring key_jstring)
{
	std::shared_lock lck(cache_lock);
	const auto key = env->GetStringUTFChars(key_jstring, nullptr);
	const bool ret = has_cache(key);
	env->ReleaseStringUTFChars(key_jstring, key);
	return ret;
}

/*
 * Class:     file_engine_dllInterface_gpu_CudaAccelerator
 * Method:    initCache
 * Signature: (Ljava/lang/String;[Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_gpu_CudaAccelerator_initCache
(JNIEnv* env, jobject, jstring key_jstring, jobjectArray records_obj)
{
	std::vector<std::string> records_vec;
	size_t total_bytes = 0;
	const auto records_len = env->GetArrayLength(records_obj);
	for (jsize i = 0; i < records_len; ++i)
	{
		const auto record_from_supplier = env->GetObjectArrayElement(records_obj, i);
		if (record_from_supplier == nullptr)
		{
			continue;
		}
		const auto jstring_val = reinterpret_cast<jstring>(record_from_supplier);
		const auto record = env->GetStringUTFChars(jstring_val, nullptr);
		if (const auto record_len = strlen(record); record_len < MAX_PATH_LENGTH)
		{
			char file_name[MAX_PATH_LENGTH]{ 0 };
			if (!global_cache::get_file_name(record, file_name))
			{
				continue;
			}

			records_vec.emplace_back(record);
			total_bytes += strlen(file_name) + 1; // 每个字符串结尾 '\0'
		}
		env->ReleaseStringUTFChars(jstring_val, record);
		env->DeleteLocalRef(record_from_supplier);
	}
	std::unique_lock ulck(cache_lock);
	const auto key = env->GetStringUTFChars(key_jstring, nullptr);
	create_and_insert_cache(records_vec, total_bytes, key);
	env->ReleaseStringUTFChars(key_jstring, key);
}

/*
 * Class:     file_engine_dllInterface_gpu_CudaAccelerator
 * Method:    addRecordsToCache
 * Signature: (Ljava/lang/String;[Ljava/lang/Object;)V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_gpu_CudaAccelerator_addRecordsToCache
(JNIEnv* env, jobject, jstring key_jstring, jobjectArray records)
{
	std::unique_lock ulck(cache_lock);
	const auto _key = env->GetStringUTFChars(key_jstring, nullptr);
	const std::string key(_key);
	env->ReleaseStringUTFChars(key_jstring, _key);
	const auto records_num = env->GetArrayLength(records);
	std::vector<std::string> records_vec;
	for (int i = 0; i < records_num; ++i)
	{
		const auto record_jstring = reinterpret_cast<jstring>(env->GetObjectArrayElement(records, i));
		const auto record = env->GetStringUTFChars(record_jstring, nullptr);
		records_vec.emplace_back(record);
		env->ReleaseStringUTFChars(record_jstring, record);
		env->DeleteLocalRef(record_jstring);
	}
	add_records_to_cache(key, records_vec);
}

/*
 * Class:     file_engine_dllInterface_gpu_CudaAccelerator
 * Method:    removeRecordsFromCache
 * Signature: (Ljava/lang/String;[Ljava/lang/Object;)V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_gpu_CudaAccelerator_removeRecordsFromCache
(JNIEnv* env, jobject, jstring key_jstring, jobjectArray records)
{
	std::unique_lock ulck(cache_lock);
	const auto _key = env->GetStringUTFChars(key_jstring, nullptr);
	const std::string key(_key);
	env->ReleaseStringUTFChars(key_jstring, _key);
	const auto records_num = env->GetArrayLength(records);
	std::vector<std::string> records_vec;
	for (int i = 0; i < records_num; ++i)
	{
		const auto record_jstring = reinterpret_cast<jstring>(env->GetObjectArrayElement(records, i));
		const auto record = env->GetStringUTFChars(record_jstring, nullptr);
		records_vec.emplace_back(record);
		env->ReleaseStringUTFChars(record_jstring, record);
		env->DeleteLocalRef(record_jstring);
	}
	remove_records_from_cache(key, records_vec);
}

/*
 * Class:     file_engine_dllInterface_gpu_CudaAccelerator
 * Method:    clearCache
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_gpu_CudaAccelerator_clearCache
(JNIEnv* env, jobject, jstring key_jstring)
{
	std::unique_lock ulck(cache_lock);
	const auto _key = env->GetStringUTFChars(key_jstring, nullptr);
	clear_cache(_key);
	env->ReleaseStringUTFChars(key_jstring, _key);
}

/*
 * Class:     file_engine_dllInterface_gpu_CudaAccelerator
 * Method:    isCudaAvailable
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_file_engine_dllInterface_gpu_CudaAccelerator_isCudaAvailable
(JNIEnv*, jobject)
{
	int device_num = 0;
	cudaError_t cudaStatus = cudaGetDeviceCount(&device_num);
	if (cudaStatus != cudaSuccess)
	{
		return FALSE;
	}
	if (device_num > 0)
	{
		void* test_ptr;
		bool ret = FALSE;
		// 测试分配内存
		do
		{
			cudaStatus = cudaMalloc(&test_ptr, 1);
			if (cudaStatus == cudaSuccess)
			{
				ret = TRUE;
			}
		} while (false);
		cudaFree(test_ptr);
		return ret;
	}
	return FALSE;
}

/*
 * Class:     file_engine_dllInterface_gpu_CudaAccelerator
 * Method:    isCacheValid
 * Signature: (Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_file_engine_dllInterface_gpu_CudaAccelerator_isCacheValid
(JNIEnv* env, jobject, jstring key_jstring)
{
	std::shared_lock lck(cache_lock);
	const auto _key = env->GetStringUTFChars(key_jstring, nullptr);
	auto iter = cache_map.find(_key);
	if (iter == cache_map.end())
	{
		return FALSE;
	}
	return iter->second->is_cache_valid;
}

/*
 * Class:     file_engine_dllInterface_gpu_CudaAccelerator
 * Method:    clearAllCache
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_gpu_CudaAccelerator_clearAllCache
(JNIEnv*, jobject)
{
	std::unique_lock ulck(cache_lock);
	clear_all_cache();
}

/*
 * Class:     file_engine_dllInterface_gpu_CudaAccelerator
 * Method:    getGPUMemUsage
 * Signature: ()I
 */
size_t get_device_memory_used();
JNIEXPORT jint JNICALL Java_file_engine_dllInterface_gpu_CudaAccelerator_getGPUMemUsage
(JNIEnv*, jobject)
{
	size_t free;
	size_t total;
	gpuErrchk(cudaMemGetInfo(&free, &total), true, "Get memory info failed");
	auto&& total_mem = total;
	auto&& memory_used = get_device_memory_used();
	if (memory_used == INFINITE)
	{
		return 100;
	}
	return static_cast<jint>(memory_used * 100 / total_mem);
}

template <typename I>
std::string n2hexstr(I w, size_t hex_len = sizeof(I) << 1)
{
	static const char* digits = "0123456789ABCDEF";
	std::string rc(hex_len, '0');
	for (size_t i = 0, j = (hex_len - 1) * 4; i < hex_len; ++i, j -= 4)
		rc[i] = digits[w >> j & 0x0f];
	return rc;
}

std::unordered_map<std::wstring, LONGLONG> query_pdh_val(PDH_STATUS& ret);

size_t get_device_memory_used()
{
	cudaDeviceProp prop;
	gpuErrchk(cudaGetDeviceProperties(&prop, current_using_device), true, "Get device info failed.");
	auto&& device_name = prop.name;
	auto&& device_name_wstr = string2wstring(device_name);
	auto&& dxgi_device = gpu_name_adapter_map.find(device_name_wstr);
	if (dxgi_device == gpu_name_adapter_map.end())
	{
		return INFINITE;
	}
	auto&& adapter_luid = dxgi_device->second.AdapterLuid;
	auto&& luid_str = "0x" + n2hexstr(adapter_luid.HighPart) + "_" + "0x" + n2hexstr(adapter_luid.LowPart);
	auto&& luid_wstr = string2wstring(luid_str);
	PDH_STATUS status;
	auto&& memory_map = query_pdh_val(status);
	for (auto& [gpu_name, memory_used] : memory_map)
	{
		if (gpu_name.find(luid_wstr) != std::wstring::npos)
		{
			return memory_used;
		}
	}
	return INFINITE;
}

std::unordered_map<std::wstring, LONGLONG> query_pdh_val(PDH_STATUS& ret)
{
	PDH_HQUERY query;
	std::unordered_map<std::wstring, LONGLONG> memory_usage_map;
	ret = PdhOpenQuery(nullptr, NULL, &query);
	if (ret != ERROR_SUCCESS)
	{
		return memory_usage_map;
	}
	PDH_HCOUNTER counter;
	ret = PdhAddCounter(query, L"\\GPU Adapter Memory(*)\\Dedicated Usage", NULL, &counter);
	if (ret != ERROR_SUCCESS)
	{
		return memory_usage_map;
	}
	ret = PdhCollectQueryData(query);
	if (ret != ERROR_SUCCESS)
	{
		return memory_usage_map;
	}
	DWORD bufferSize = 0;
	DWORD itemCount = 0;
	PdhGetRawCounterArray(counter, &bufferSize, &itemCount, nullptr);
	auto* lpItemBuffer = reinterpret_cast<PPDH_RAW_COUNTER_ITEM_W>(malloc(bufferSize));
	if (lpItemBuffer == nullptr)
	{
		return memory_usage_map;
	}
	ret = PdhGetRawCounterArray(counter, &bufferSize, &itemCount, lpItemBuffer);
	if (ret != ERROR_SUCCESS)
	{
		free(lpItemBuffer);
		return memory_usage_map;
	}
	for (DWORD i = 0; i < itemCount; ++i)
	{
		auto& [szName, RawValue] = lpItemBuffer[i];
		memory_usage_map.insert(std::make_pair(szName, RawValue.FirstValue));
	}
	free(lpItemBuffer);
	ret = PdhCloseQuery(query);
	return memory_usage_map;
}

/**
 * \brief 将显卡计算的结果保存到hashmap中
 */
void collect_results(std::atomic_uint& result_counter,
	const unsigned max_results,
	const std::vector<std::string>& search_case_vec,
	Concurrency::concurrent_vector<collected_result_data>& collected_results)
{
	bool all_complete;
	const auto stop_func = [&]
		{
			return is_stop() || result_counter.load() > max_results;
		};
	const auto _collect_func = [&](const std::string& _key, const std::string& _matched_record_str,
		unsigned* matched_number)
		{
			++result_counter;
			if (result_counter.load() > max_results)
			{
				is_results_number_exceed = true;
			}
#ifdef DEBUG_OUTPUT
			std::cout << "collecting " << _matched_record_str << std::endl;
#endif
			collected_results.push_back(collected_result_data{ _key, _matched_record_str });
			++*matched_number;
		};
	do
	{
		//尝试退出
		all_complete = true;
		for (const auto& [key, cache_struct] : cache_map)
		{
#ifdef DEBUG_OUTPUT
			std::cout << "try to collect key " << key << std::endl;
#endif
			if (stop_func())
			{
#ifdef DEBUG_OUTPUT
				std::cout << "stop collect results due to stop signal is true" << std::endl;
#endif
				break;
			}
			if (!cache_struct->is_cache_valid)
			{
				continue;
			}
			if (!cache_struct->is_match_done.load())
			{
				//发现仍然有结果未计算完，设置退出标志为false，跳到下一个计算结果
				all_complete = false;
				continue;
			}
			if (int expected = 0;
				!cache_struct->is_output_done.compare_exchange_strong(expected, 1))
			{
				continue;
			}
			unsigned matched_number = 0;
			//复制结果数组到host，dev_output下标对应dev_cache中的下标，若dev_output[i]中的值为1，则对应dev_cache[i]字符串匹配成功
			const auto output_ptr = new char[cache_struct->output_bitmap_size]();
			//将dev_output拷贝到output_ptr
			gpuErrchk(
				cudaMemcpy(output_ptr, cache_struct->dev_output_bitmap, cache_struct->output_bitmap_size,
					cudaMemcpyDeviceToHost),
				true, nullptr);
			for (size_t i = 0; i < cache_struct->output_bitmap_size; ++i)
			{
				if (stop_func())
				{
#ifdef DEBUG_OUTPUT
					std::cout << "stop inner collect results due to stop signal is true" << std::endl;
#endif // DEBUG_OUTPUT

					break;
				}
				//dev_cache[i]字符串匹配成功
				if (static_cast<bool>(output_ptr[i]))
				{
					char matched_record_str_file_name[MAX_PATH_LENGTH]{ 0 };
					char* str_address = nullptr;
					gpuErrchk(cudaMemcpy(&str_address, cache_struct->str_data.dev_str_addr + i, sizeof(size_t),
						cudaMemcpyDeviceToHost), false, nullptr);
					if (str_address == nullptr)
					{
						continue;
					}
					//拷贝GPU中的字符串到host
					const auto str_len = cache_struct->str_data.str_length_array[i];
					gpuErrchk(cudaMemcpy(matched_record_str_file_name, str_address, str_len,
						cudaMemcpyDeviceToHost), false, "collect results failed");
					const auto parent_path_hash = cache_struct->str_data.global_parent_path_index_array[i];
					const auto parent_path = global_cache::copy_to_host_by_hash(parent_path_hash);
					const auto matched_record_str = parent_path + matched_record_str_file_name;

					// 判断文件和文件夹
					if (search_case_vec.empty())
					{
						_collect_func(key, matched_record_str, &matched_number);
					}
					else
					{
						if (std::find(search_case_vec.begin(),
							search_case_vec.end(),
							"f") != search_case_vec.end())
						{
							if (is_dir_or_file(matched_record_str.c_str()) == 1)
							{
								_collect_func(key, matched_record_str, &matched_number);
							}
						}
						else if (std::find(search_case_vec.begin(),
							search_case_vec.end(),
							"d") != search_case_vec.end())
						{
							if (is_dir_or_file(matched_record_str.c_str()) == 0)
							{
								_collect_func(key, matched_record_str, &matched_number);
							}
						}
						else
						{
							_collect_func(key, matched_record_str, &matched_number);
						}
					}
				}
			}
#ifdef DEBUG_OUTPUT
			std::cout << "key " << key << " collected. " << std::endl;
#endif 
			cache_struct->matched_number = matched_number;
			cache_struct->is_output_done = 2;
			delete[] output_ptr;
		}
	} while (!all_complete && !stop_func());
}

/**
 * \brief 检查是文件还是文件夹
 * \param path path
 * \return 如果是文件返回1，文件夹返回0，错误返回-1
 */
int is_dir_or_file(const char* path)
{
	const auto w_path = string2wstring(path);
	const DWORD dwAttrib = GetFileAttributes(w_path.c_str());
	if (dwAttrib != INVALID_FILE_ATTRIBUTES)
	{
		if (dwAttrib & FILE_ATTRIBUTE_DIRECTORY)
		{
			return 0;
		}
		return 1;
	}
	return -1;
}

inline bool is_file_exist(const char* path)
{
	const auto w_path = string2wstring(path);
	return GetFileAttributes(w_path.c_str()) != INVALID_FILE_ATTRIBUTES;
}

std::wstring string2wstring(const std::string& str)
{
	std::wstring result;
	//获取缓冲区大小，并申请空间，缓冲区大小按字符计算
	const int len = MultiByteToWideChar(CP_UTF8, 0, str.c_str(), static_cast<int>(str.size()), nullptr, 0);
	const auto buffer = new TCHAR[len + 1];
	//多字节编码转换成宽字节编码
	MultiByteToWideChar(CP_UTF8, 0, str.c_str(), static_cast<int>(str.size()), buffer, len);
	buffer[len] = '\0';
	//删除缓冲区并返回值
	result.append(buffer);
	delete[] buffer;
	return result;
}

/**
 * \brief 根据java数组search_case构建search_case_vector
 * \param env java运行环境指针
 * \param search_case_vec output
 * \param search_case search_case，有f，d，case，full，分别代表仅匹配文件，仅匹配文件夹，不忽略大小写匹配，全字匹配（CUDA不支持文件和文件夹判断，f和d将被忽略）
 */
void generate_search_case(JNIEnv* env, std::vector<std::string>& search_case_vec, const jobjectArray search_case)
{
	const auto search_case_len = env->GetArrayLength(search_case);
	for (jsize i = 0; i < search_case_len; ++i)
	{
		if (const auto search_case_str = env->GetObjectArrayElement(search_case, i);
			search_case_str != nullptr)
		{
			const auto tmp_search_case_str = reinterpret_cast<jstring>(search_case_str);
			auto search_case_chars = env->GetStringUTFChars(tmp_search_case_str, nullptr);
			search_case_vec.emplace_back(search_case_chars);
			env->ReleaseStringUTFChars(tmp_search_case_str, search_case_chars);
			env->DeleteLocalRef(tmp_search_case_str);
		}
	}
}

/**
 * \brief 检查是否已经存在该缓存
 * \param key key
 * \return true如果已经存在
 */
bool has_cache(const std::string& key)
{
	return cache_map.find(key) != cache_map.end();
}

bool is_record_repeat(const std::string& record, const list_cache* cache)
{
	const auto record_hash = hasher(record);
	return cache->str_data.record_hash.find(record_hash) != cache->str_data.record_hash.end();
}

/**
 * \brief 添加一个record到cache
 * \param key key
 * \param records records
 */
void add_records_to_cache(const std::string& key, const std::vector<std::string>& records)
{
	auto&& cache_iter = cache_map.find(key);
	if (cache_iter != cache_map.end())
	{
		if (const auto& cache = cache_iter->second; cache->is_cache_valid)
		{
			std::vector<std::string> global_cache_to_insert;
			cudaStream_t stream;
			gpuErrchk(cudaStreamCreate(&stream), true, nullptr);
			const auto records_file_name_buffer = new char[records.size() * MAX_PATH_LENGTH]();
			size_t count = 0;
			for (auto&& record_full_path : records)
			{
				++count;
				if (exit_flag.load())
				{
					break;
				}
				const auto record_full_len = record_full_path.length();
				if (record_full_len >= MAX_PATH_LENGTH)
				{
					continue;
				}

				char* file_name = records_file_name_buffer + (count - 1) * MAX_PATH_LENGTH;
				if (!global_cache::get_file_name(record_full_path.c_str(), file_name))
				{
					continue;
				}
				const auto file_name_len = strlen(file_name);

				if (const bool is_repeat = is_record_repeat(record_full_path, cache); !is_repeat)
				{
					if (cache->str_data.str_remain_blank_bytes >= file_name_len + 1)
					{
						const auto index = cache->str_data.record_num.load();
						//计算上一个字符串的内存地址
						char* last_str_addr = nullptr;
						if (index != 0)
						{
							gpuErrchk(cudaMemcpy(&last_str_addr,
								cache->str_data.dev_str_addr + index - 1,
								sizeof(size_t), cudaMemcpyDeviceToHost),
								true, nullptr);
						}
						char* target_address;
						if (last_str_addr != nullptr)
						{
							//字符串尾的下一位即为新字符串起始地址
							target_address = last_str_addr + cache->str_data.str_length_array[index - 1] + 1;
						}
						else
						{
							target_address = cache->str_data.dev_strs;
						}
						//记录到下一空位内存地址target_address
						gpuErrchk(cudaMemsetAsync(target_address,
							0,
							file_name_len + 1,
							stream), true,
							get_cache_info(key, cache).c_str());
						gpuErrchk(cudaMemcpyAsync(target_address,
							file_name,
							file_name_len,
							cudaMemcpyHostToDevice, stream), true,
							get_cache_info(key, cache).c_str());
						//记录内存地址到dev_str_addr
						if (cache->str_data.str_addr_capacity <= index)
						{
#ifdef DEBUG_OUTPUT
							std::cout << "start to enlarge dev_str_addr and str_lenght_array" << std::endl;
#endif
							const auto new_str_addr_capacity = cache->str_data.str_addr_capacity + 10;
							const auto new_str_addr_alloc_size = new_str_addr_capacity * sizeof(size_t);
							size_t* new_dev_str_addr;
							gpuErrchk(cudaMallocAsync(&new_dev_str_addr, new_str_addr_alloc_size, stream), true,
								nullptr);
							gpuErrchk(cudaMemsetAsync(new_dev_str_addr, 0, new_str_addr_alloc_size, stream), true,
								nullptr);
							gpuErrchk(cudaMemcpyAsync(new_dev_str_addr,
								cache->str_data.dev_str_addr,
								cache->str_data.str_addr_capacity * sizeof size_t,
								cudaMemcpyDeviceToDevice,
								stream), true, nullptr);
							gpuErrchk(cudaFreeAsync(cache->str_data.dev_str_addr, stream), true, nullptr);
							cache->str_data.dev_str_addr = new_dev_str_addr;
#ifdef DEBUG_OUTPUT
							std::cout << "copy dev_str_addr complete." << std::endl;
#endif
							auto* new_str_length_array = new unsigned short[new_str_addr_capacity]();
							auto* new_global_cache_array = new size_t[new_str_addr_capacity]();
							for (size_t i = 0; i < cache->str_data.str_addr_capacity; ++i)
							{
								new_str_length_array[i] = cache->str_data.str_length_array[i];
								new_global_cache_array[i] = cache->str_data.global_parent_path_index_array[i];
							}
							delete[] cache->str_data.str_length_array;
							delete[] cache->str_data.global_parent_path_index_array;

							cache->str_data.str_length_array = new_str_length_array;
							cache->str_data.global_parent_path_index_array = new_global_cache_array;

							cache->str_data.str_addr_capacity = new_str_addr_capacity;
#ifdef DEBUG_OUTPUT
							std::cout << "enlarge complete." << std::endl;
#endif
						}
						gpuErrchk(cudaMemcpyAsync(cache->str_data.dev_str_addr + index,
							&target_address,
							sizeof(size_t),
							cudaMemcpyHostToDevice,
							stream),
							true, nullptr);
						//记录字符串长度
						cache->str_data.str_length_array[index] = static_cast<unsigned short>(file_name_len);

						char record_parent_path[MAX_PATH_LENGTH]{ 0 };
						global_cache::get_parent_path(record_full_path.c_str(), record_parent_path);
						const auto parent_path_hash = global_cache::get_str_hash(record_parent_path);
						cache->str_data.global_parent_path_index_array[index] = parent_path_hash;

						global_cache_to_insert.emplace_back(record_parent_path);
#ifdef DEBUG_OUTPUT
						std::cout << "target address: " << std::hex << "0x" << reinterpret_cast<size_t>(target_address) << std::endl;
						std::cout << "successfully add record: " << record_full_path << "  key: " << key << std::endl;
#endif
						++cache->str_data.record_num;
						cache->str_data.str_remain_blank_bytes -= file_name_len + 1;
						cache->str_data.record_hash.insert(hasher(record_full_path));
					}
					else
					{
						// 无空位，有文件丢失，使cache无效
						cache->is_cache_valid = false;
						break;
					}
				}
			}
			delete[] records_file_name_buffer;
			global_cache::insert(global_cache_to_insert);
			gpuErrchk(cudaStreamSynchronize(stream), true, nullptr);
			gpuErrchk(cudaStreamDestroy(stream), true, nullptr);
		}
	}
}

/**
 * \brief 从key对应的record中删除一个记录
 * \param key key
 * \param records records
 */
void remove_records_from_cache(const std::string& key, std::vector<std::string>& records)
{
	auto&& cache_iter = cache_map.find(key);
	if (cache_iter != cache_map.end())
	{
		if (const auto& cache = cache_iter->second; cache->is_cache_valid)
		{
			const auto tmp_records = new char[MAX_PATH_LENGTH * cache->str_data.record_num.load()]();
			cudaStream_t stream;
			gpuErrchk(cudaStreamCreate(&stream), true, nullptr);
			for (size_t i = 0; i < cache->str_data.record_num.load(); ++i)
			{
				char* str_address;
				char* tmp = tmp_records + MAX_PATH_LENGTH * i;
				gpuErrchk(cudaMemcpyAsync(&str_address,
					cache->str_data.dev_str_addr + i,
					sizeof(size_t),
					cudaMemcpyDeviceToHost,
					stream),
					true,
					get_cache_info(key, cache).c_str());
				//拷贝GPU中的字符串到tmp
				gpuErrchk(cudaMemcpyAsync(tmp,
					str_address,
					cache->str_data.str_length_array[i],
					cudaMemcpyDeviceToHost,
					stream),
					true,
					get_cache_info(key, cache).c_str());
			}
			gpuErrchk(cudaStreamSynchronize(stream), true, nullptr);
			gpuErrchk(cudaStreamDestroy(stream), true, nullptr);

			std::vector<std::string> remain_strs;
			std::vector<std::string> global_cache_to_remove;
			size_t new_total_bytes = 0;
			for (size_t i = 0; i < cache->str_data.record_num.load(); ++i)
			{
				if (exit_flag.load())
				{
					break;
				}
				const auto parent_path_hash = cache->str_data.global_parent_path_index_array[i];
				const auto parent_path = global_cache::copy_to_host_by_hash(parent_path_hash);

				char* tmp = tmp_records + MAX_PATH_LENGTH * i;
				const auto full_record_path = parent_path + tmp;
				const auto& record = std::find(records.begin(), records.end(), full_record_path);
				if (record == records.end())
				{
					new_total_bytes += (strlen(tmp) + 1);
					remain_strs.emplace_back(full_record_path);
				}
				else
				{
					global_cache_to_remove.emplace_back(full_record_path);
				}
			}

			gpuErrchk(cudaFree(cache->str_data.dev_strs), false, nullptr);
			gpuErrchk(cudaFree(cache->str_data.dev_str_addr), false, nullptr);
			delete[] cache->str_data.str_length_array;
			delete[] cache->str_data.global_parent_path_index_array;
			delete cache;
			cache_map.unsafe_erase(key);

			delete[] tmp_records;
			global_cache::remove(global_cache_to_remove);
			create_and_insert_cache(remain_strs, new_total_bytes, key);
		}
	}
}

void create_and_insert_cache(const std::vector<std::string>& records_vec, const size_t total_bytes,
	const std::string& key)
{
	const auto record_count = records_vec.size();
	auto cache = new list_cache;
	cache->str_data.record_num = record_count;

	const auto alloc_bytes = total_bytes + CACHE_REMAIN_BLANK_SIZE_IN_BYTES;
	gpuErrchk(cudaMalloc(&cache->str_data.dev_strs, alloc_bytes), true, nullptr);
	gpuErrchk(cudaMemset(cache->str_data.dev_strs, 0, alloc_bytes), true, nullptr);
	cache->str_data.str_total_bytes = alloc_bytes;
	cache->str_data.str_remain_blank_bytes = CACHE_REMAIN_BLANK_SIZE_IN_BYTES;

	const auto str_addr_capacity = record_count + CACHE_REMAIN_BLANK_SIZE_IN_BYTES / MAX_PATH_LENGTH;
	const auto str_addr_alloc_size = str_addr_capacity * sizeof(size_t);
	gpuErrchk(cudaMalloc(&cache->str_data.dev_str_addr, str_addr_alloc_size), true, nullptr);
	gpuErrchk(cudaMemset(cache->str_data.dev_str_addr, 0, str_addr_alloc_size), true, nullptr);
	cache->str_data.str_addr_capacity = str_addr_capacity;

	cache->str_data.str_length_array = new unsigned short[str_addr_capacity]();
	cache->str_data.global_parent_path_index_array = new size_t[str_addr_capacity]();

	cache->is_cache_valid = true;
	cache->is_match_done = false;
	cache->is_output_done = 0;

	cudaStream_t stream;
	gpuErrchk(cudaStreamCreate(&stream), true, nullptr);
	auto target_addr = cache->str_data.dev_strs;
	auto save_str_addr_ptr = cache->str_data.dev_str_addr;
	size_t i = 0;

	char* record_buffer = new char[record_count * MAX_PATH_LENGTH]();
	std::vector<std::string> parent_record_vec;
	for (const std::string& record_full_path : records_vec)
	{
		auto* record_buffer_ptr = record_buffer + i * MAX_PATH_LENGTH;
		bool get_file_name_success = global_cache::get_file_name(record_full_path.c_str(), record_buffer_ptr);
		if (!get_file_name_success)
		{
			continue;
		}

		char record_parent_path[MAX_PATH_LENGTH]{ 0 };
		global_cache::get_parent_path(record_full_path.c_str(), record_parent_path);
		parent_record_vec.emplace_back(record_parent_path);

		const auto record_file_name_length = strlen(record_buffer_ptr);
		gpuErrchk(cudaMemcpyAsync(target_addr, record_buffer_ptr, record_file_name_length, cudaMemcpyHostToDevice, stream), true,
			nullptr);
		const auto str_address = reinterpret_cast<size_t>(target_addr);
		//保存字符串在显存上的地址
		gpuErrchk(cudaMemcpyAsync(save_str_addr_ptr, &str_address, sizeof(size_t), cudaMemcpyHostToDevice, stream),
			true, nullptr);
		cache->str_data.str_length_array[i] = static_cast<unsigned short>(record_file_name_length);
		cache->str_data.record_hash.insert(hasher(record_full_path));

		const auto parent_path_hash = global_cache::get_str_hash(record_parent_path);
		cache->str_data.global_parent_path_index_array[i] = parent_path_hash;

		target_addr += record_file_name_length + 1;
		++save_str_addr_ptr;
		++i;
	}
	gpuErrchk(cudaStreamSynchronize(stream), true, nullptr);
	gpuErrchk(cudaStreamDestroy(stream), true, nullptr);
	delete[] record_buffer;
	global_cache::insert(parent_record_vec);
	cache_map.insert(std::make_pair(key, cache));
}

void clear_cache(const std::string& key)
{
	try
	{
		const auto cache = cache_map.at(key);
		cache->is_cache_valid = false;
		gpuErrchk(cudaFree(cache->str_data.dev_strs), false, get_cache_info(key, cache).c_str());
		gpuErrchk(cudaFree(cache->str_data.dev_str_addr), false, get_cache_info(key, cache).c_str());
		delete[] cache->str_data.str_length_array;
		delete[] cache->str_data.global_parent_path_index_array;
		delete cache;
		cache_map.unsafe_erase(key);
	}
	catch (std::out_of_range&)
	{
	}
	catch (std::exception& e)
	{
		fprintf(stderr, "clear cache failed: %s\n", e.what());
	}
}

void clear_all_cache()
{
	std::vector<std::string> all_keys_vec;
	for (auto& [key, _] : cache_map)
	{
		all_keys_vec.emplace_back(key);
	}
	for (auto& key : all_keys_vec)
	{
		clear_cache(key);
	}
}

void release_all()
{
	clear_all_cache();
	free_cuda_search_memory();
	free_stop_signal();
	global_cache::clear();
}
