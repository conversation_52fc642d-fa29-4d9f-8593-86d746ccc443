﻿/* DO NOT EDIT THIS FILE - it is machine generated */
#include "jni.h"
/* Header for class file_engine_dllInterface_IsLocalDisk */

#ifndef _Included_file_engine_dllInterface_IsLocalDisk
#define _Included_file_engine_dllInterface_IsLocalDisk
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     file_engine_dllInterface_IsLocalDisk
 * Method:    isLocalDisk
 * Signature: (Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_file_engine_dllInterface_IsLocalDisk_isLocalDisk
  (JNIEnv *, jobject, jstring);

/*
 * Class:     file_engine_dllInterface_IsLocalDisk
 * Method:    isDiskNTFS
 * Signature: (Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_file_engine_dllInterface_IsLocalDisk_isDiskNTFS
  (JNIEnv *, jobject, jstring);

#ifdef __cplusplus
}
#endif
#endif
