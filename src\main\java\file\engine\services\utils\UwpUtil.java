package file.engine.services.utils;

import lombok.SneakyThrows;

public class UwpUtil {
    @SneakyThrows
    public static void openUWP(String appUserModelId) {
        if (appUserModelId == null || appUserModelId.isEmpty()) {
            return;
        }
        String shellNameTemplate = "shell:AppsFolder\\%s";

        String[] strings = {
                "explorer.exe", String.format(shellNameTemplate, appUserModelId)
        };
        Runtime.getRuntime().exec(strings);
    }
}
