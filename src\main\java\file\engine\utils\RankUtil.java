package file.engine.utils;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import file.engine.entity.SearchResult;
import file.engine.entity.UwpResult;
import file.engine.utils.file.FileUtil;

import java.util.Comparator;

/**
 * 排序工具类
 * <p>
 * 该类负责计算搜索结果的排序分数，用于确定搜索结果的显示顺序。
 * 分数越大的结果排在越前面。
 * <p>
 * 主要功能：
 * 1. 计算文件搜索结果的排序分数
 * 2. 计算UWP应用搜索结果的排序分数
 * 3. 基于连续匹配算法评估匹配质量
 * 4. 缓存计算结果以提高性能
 * <p>
 * 排序算法考虑的因素：
 * - 优先级结果权重（最重要，大幅加分）
 * - 模糊匹配权重（准确性低，适度减分）
 * - 文件名长度权重（长文件名减分）
 * - 连续匹配权重（考虑连续性、位置、密度，匹配好加分）
 * - 有意义概率权重（有意义的文件名加分）
 *
 * <AUTHOR> Engine Team
 */
public class RankUtil {

    /**
     * 排序分数缓存
     * <p>
     * 使用Caffeine缓存来存储已计算的排序分数，避免重复计算。
     * - 软引用：在内存不足时可以被回收
     * - 最大容量：1000个条目
     * - 缓存键格式：文件路径:关键词1,关键词2,...
     */
    private static final Cache<String, Double> rankCache = Caffeine.newBuilder()
            .softValues()
            .maximumSize(1000)
            .build();


    /**
     * 计算SearchResult的排序分数
     * <p>
     * 综合多个因素计算文件搜索结果的排序分数，分数越大排名越靠前。
     * <p>
     * 分数计算公式：
     * 基础分数 = 1000（基础分，确保大部分情况下为正数）
     * + 优先级结果：+1000（最重要的加分项）
     * - 模糊匹配：-100（模糊匹配的惩罚）
     * - 文件名长度：-长度*1（长文件名的惩罚）
     * + 连续匹配分数：+分数*30（连续匹配的奖励）
     * + 有意义概率：+概率*100（有意义文件名的奖励）
     * - 路径哈希：-哈希*0.000001（确保排序稳定性的微调）
     * <p>
     * 示例：
     * - 优先级文件"test.txt"：基础1000+优先级1000=2000+，其他因素微调
     * - 普通文件"verylongfilename.txt"：基础1000-长度惩罚19=981+，其他因素调整
     * - 连续匹配好的文件：连续匹配奖励可能+300以上
     *
     * @param searchResult 搜索结果对象
     * @param keywords     搜索关键词数组
     * @return 排序分数（越大越靠前）
     */
    private static double calculateSearchResultRank(SearchResult searchResult, String[] keywords) {
        // 构建缓存键：文件路径 + 关键词组合
        String cacheKey = searchResult.getPath() + ":" + String.join(",", keywords);
        Double cachedRank = rankCache.getIfPresent(cacheKey);
        if (cachedRank != null) {
            return cachedRank;
        }

        String fileName = FileUtil.getFileName(searchResult.getPath());
        String word = FileUtil.removeFileNameSuffix(fileName);

        double rank = 1000; // 基础分数，确保大部分情况下为正数

        // 优先级结果权重 (最重要的因素)
        // 被标记为优先级的结果会获得巨大的分数优势
        if (searchResult.isPrioritizeResult()) {
            rank += 1000;
        }

        // 模糊匹配权重
        // 模糊匹配的结果准确性较低，给予惩罚
        if (searchResult.isFuzzyMatched()) {
            rank -= 100;
        }

        // 连续匹配权重（核心算法）
        // 计算关键词与文件名的匹配质量，匹配越好分数越高
        double consecutiveMatchScore = getConsecutiveMatchScore(fileName, keywords);
        rank += consecutiveMatchScore * 30; // 乘以30放大连续匹配的影响

        // 有意义概率权重
        // 文件名越像有意义的单词，相关性可能越高
        double meaningfulProbability = calculateMeaningfulProbability(word);
        rank += meaningfulProbability * 100;

        // 路径哈希作为次要排序条件
        // 确保相同分数的结果有稳定的排序顺序
        var pathHashCode = Math.abs(searchResult.getPath().hashCode());
        rank -= pathHashCode * 0.000000000000000000001; // 极小的权重，只用于打破平局

        // 检查并处理异常值，确保比较器契约不被违反
        if (Double.isNaN(rank) || Double.isInfinite(rank)) {
            rank = 0; // 异常情况下给予最低优先级
        }

        // 缓存计算结果以提高性能
        rankCache.put(cacheKey, rank);

        return rank;
    }

    /**
     * 计算UwpResult的排序分数
     * <p>
     * 为UWP应用搜索结果计算排序分数，算法与文件搜索类似但简化了一些因素。
     * UWP应用没有优先级和模糊匹配的概念，主要基于应用名称的匹配质量。
     * <p>
     * 分数计算公式：
     * 基础分数 = 1000（基础分，确保大部分情况下为正数）
     * - 显示名称长度：-长度*1（长应用名的惩罚）
     * + 连续匹配分数：+分数*30（连续匹配的奖励）
     * + 有意义概率：+概率*100（有意义应用名的奖励）
     * - 名称哈希：-哈希*0.000001（确保排序稳定性）
     * <p>
     * 示例：
     * - 短应用名"Word"匹配关键词"word"：基础1000-长度惩罚4+连续匹配奖励120=1116+
     * - 长应用名"Microsoft Office Word"：基础1000-长度惩罚23+匹配奖励=977+
     *
     * @param uwpResult UWP搜索结果对象
     * @param keywords  搜索关键词数组
     * @return 排序分数（越大越靠前）
     */
    private static double calculateUwpResultRank(UwpResult uwpResult, String[] keywords) {
        String displayName = uwpResult.getDisplayName();

        // 构建UWP专用的缓存键
        String cacheKey = "uwp:" + displayName + ":" + String.join(",", keywords);
        Double cachedRank = rankCache.getIfPresent(cacheKey);
        if (cachedRank != null) {
            return cachedRank;
        }

        double rank = 1000; // 基础分数，确保大部分情况下为正数

        // 连续匹配权重（核心算法）
        // 计算关键词与应用名称的匹配质量
        double consecutiveMatchScore = getConsecutiveMatchScore(displayName, keywords);
        rank += consecutiveMatchScore * 30;

        // 有意义概率权重
        // 应用名称越像有意义的单词组合，相关性可能越高
        double meaningfulProbability = calculateMeaningfulProbability(displayName);
        rank += meaningfulProbability * 100;

        // 显示名称哈希作为次要排序条件
        // 确保相同分数的应用有稳定的排序顺序
        rank -= Math.abs(displayName.hashCode()) * 0.000000000000000000001;

        // 检查并处理异常值，确保比较器契约不被违反
        if (Double.isNaN(rank) || Double.isInfinite(rank)) {
            rank = 0; // 异常情况下给予最低优先级
        }

        // 缓存计算结果
        rankCache.put(cacheKey, rank);

        return rank;
    }

    /**
     * 获取连续匹配权重分数（支持多个关键词）
     * <p>
     * 该方法计算文件名与多个关键词的匹配质量，考虑以下因素：
     * 1. 匹配的连续性（连续匹配得分更高）
     * 2. 匹配的位置（靠前的匹配得分更高）
     * 3. 匹配的密度（紧密匹配得分更高）
     * 4. 匹配段的数量（段数越少得分越高）
     *
     * @param fileName 文件名
     * @param keywords 关键词数组
     * @return 连续匹配权重分数（越高越好）
     */
    public static double getConsecutiveMatchScore(String fileName, String[] keywords) {
        if (fileName == null || fileName.isEmpty() || keywords == null || keywords.length == 0) {
            return 0;
        }
        double totalScore = 0;
        for (String keyword : keywords) {
            // 处理以'?'开头的关键词（移除前缀）
            if (keyword.charAt(0) == '?') {
                keyword = keyword.substring(1);
            }
            totalScore += getConsecutiveMatchScore(fileName, keyword);
        }
        return totalScore;
    }

    /**
     * 获取连续匹配的权重分数（考虑连续性和位置）
     * <p>
     * 算法说明：
     * 1. 尝试从文件名的每个位置开始匹配关键词
     * 2. 对于每个完整匹配，计算综合分数
     * 3. 返回所有可能匹配中的最高分数
     * <p>
     * 示例：
     * - 关键词 "geekexe"，文件名 "geek.exe"
     * 匹配：g(0)e(1)e(2)k(3).e(5)x(6)e(7) -> 2个连续段，密度高
     * - 关键词 "geekexe"，文件名 "geek64.exe"
     * 匹配：g(0)e(1)e(2)k(3)6.4.e(7)x(8)e(9) -> 2个连续段，密度较低
     *
     * @param fileName 文件名
     * @param keyword  关键词
     * @return 连续匹配权重分数（越高越好）
     */
    private static double getConsecutiveMatchScore(String fileName, String keyword) {
        if (fileName == null || fileName.isEmpty() || keyword == null || keyword.isEmpty()) {
            return 0;
        }

        // 转换为小写进行不区分大小写的匹配
        String lowerFileName = fileName.toLowerCase();
        String lowerKeyword = keyword.toLowerCase();

        double bestScore = 0;

        // 尝试从文件名的每个位置开始匹配关键词
        // 这样可以找到最佳的匹配起始位置
        for (int startPos = 0; startPos < lowerFileName.length(); startPos++) {
            MatchResult matchResult = findMatchFromPosition(lowerFileName, lowerKeyword, startPos);

            // 如果完全匹配了关键词的所有字符，计算分数
            if (matchResult.completeMatch()) {
                double currentScore = calculateMatchScore(matchResult, startPos, lowerFileName.length());
                bestScore = Math.max(bestScore, currentScore);
            }
        }

        return bestScore;
    }

    /**
     * 从指定位置开始查找匹配
     * <p>
     * 该方法从文件名的指定位置开始，按顺序匹配关键词的每个字符。
     * 统计匹配的各种特征用于后续的分数计算。
     * <p>
     * 匹配统计说明：
     * - totalMatches: 总匹配字符数
     * - maxConsecutive: 最长连续匹配长度（如"geek"中的4个连续字符）
     * - consecutiveSegments: 连续段数量（如"geek.exe"有2段："geek"和"exe"）
     * - matchSpan: 匹配跨度（从第一个匹配字符到最后一个匹配字符的距离）
     *
     * @param fileName 文件名（已转为小写）
     * @param keyword  关键词（已转为小写）
     * @param startPos 开始匹配的位置
     * @return 匹配结果统计信息
     */
    private static MatchResult findMatchFromPosition(String fileName, String keyword, int startPos) {
        int keywordIndex = 0;           // 当前匹配到关键词的第几个字符
        int totalMatches = 0;           // 总匹配字符数
        int currentConsecutive = 0;     // 当前连续匹配长度
        int maxConsecutive = 0;         // 最长连续匹配长度
        int lastMatchPos = -1;          // 上一个匹配字符的位置
        int consecutiveSegments = 0;    // 连续段数量
        boolean inConsecutiveSegment = false;  // 是否正在一个连续段中
        int firstMatchPos = -1;         // 第一个匹配字符的位置
        int lastMatchPosEnd = -1;       // 最后一个匹配字符的位置

        // 从指定位置开始遍历文件名，寻找关键词的每个字符
        for (int i = startPos; i < fileName.length() && keywordIndex < keyword.length(); i++) {
            if (fileName.charAt(i) == keyword.charAt(keywordIndex)) {
                totalMatches++;
                keywordIndex++;

                // 记录匹配范围的边界位置
                if (firstMatchPos == -1) {
                    firstMatchPos = i;
                }
                lastMatchPosEnd = i;

                // 判断是否与前一个匹配字符连续
                if (lastMatchPos == -1 || i == lastMatchPos + 1) {
                    // 连续匹配：当前字符紧跟在上一个匹配字符后面
                    currentConsecutive++;
                    maxConsecutive = Math.max(maxConsecutive, currentConsecutive);

                    // 如果这是一个新连续段的开始，增加段数计数
                    if (!inConsecutiveSegment) {
                        consecutiveSegments++;
                        inConsecutiveSegment = true;
                    }
                } else {
                    // 非连续匹配：当前字符与上一个匹配字符之间有间隔
                    // 开始一个新的连续段
                    currentConsecutive = 1;
                    consecutiveSegments++;
                    inConsecutiveSegment = true;
                }
                lastMatchPos = i;
            }
        }

        // 计算匹配跨度：从第一个匹配字符到最后一个匹配字符的总长度
        // 跨度越小表示匹配越紧密
        int matchSpan = (firstMatchPos != -1 && lastMatchPosEnd != -1) ?
                (lastMatchPosEnd - firstMatchPos + 1) : 0;

        return new MatchResult(keywordIndex == keyword.length(), totalMatches, maxConsecutive,
                consecutiveSegments, matchSpan, firstMatchPos);
    }

    /**
     * 计算匹配分数
     * <p>
     * 综合考虑多个因素计算最终的匹配分数：
     * <p>
     * 1. 位置权重 (positionWeight)：
     * - 匹配开始位置越靠前，分数越高
     * - 公式：1.0 - startPos / fileNameLength
     * - 范围：[0, 1]，越接近1越好
     * <p>
     * 2. 连续性比例 (continuityRatio)：
     * - 最长连续匹配段占总匹配字符的比例
     * - 公式：maxConsecutive / totalMatches
     * - 范围：[0, 1]，越接近1表示匹配越连续
     * <p>
     * 3. 段数惩罚 (segmentPenalty)：
     * - 连续段数越多，表示匹配越分散，给予惩罚
     * - 公式：1.0 / consecutiveSegments（最小保持0.1）
     * - 1段=1.0（无惩罚），2段=0.5，3段=0.33，以此类推
     * <p>
     * 4. 密度奖励 (densityBonus)：
     * - 匹配密度 = 匹配字符数 / 匹配跨度
     * - 密度越高表示匹配越紧密，给予奖励
     * - 公式：1.0 + density * 0.5
     * <p>
     * 最终分数 = 匹配字符数 × 位置权重 × (连续性奖励 × 段数惩罚 × 密度奖励)
     *
     * @param matchResult    匹配结果统计
     * @param startPos       匹配开始位置
     * @param fileNameLength 文件名总长度
     * @return 匹配分数（越高越好）
     */
    private static double calculateMatchScore(MatchResult matchResult, int startPos, int fileNameLength) {
        if (matchResult.totalMatches == 0) {
            return 0;
        }

        // 1. 位置权重：匹配开始位置越靠前，权重越高
        // 例如：文件名长度10，从位置0开始=1.0，从位置5开始=0.5
        double positionWeight = 1.0 - (double) startPos / fileNameLength;

        // 2. 连续性比例：最长连续段占总匹配字符的比例
        // 例如：匹配"geek"(4个连续) + "exe"(3个连续)，总7个字符，比例=4/7≈0.57
        double continuityRatio = (double) matchResult.maxConsecutive / matchResult.totalMatches;

        // 3. 段数惩罚：连续段越多表示匹配越分散，给予惩罚
        // 例如：1段=1.0(无惩罚)，2段=0.5，3段=0.33，最低保持0.1
        double segmentPenalty = Math.max(0.1, 1.0 / Math.max(1, matchResult.consecutiveSegments));

        // 4. 密度奖励：匹配字符数与匹配跨度的比例，密度越高奖励越多
        double densityBonus = 1.0;
        if (matchResult.matchSpan > 0) {
            double density = (double) matchResult.totalMatches / matchResult.matchSpan;
            // 例如：7个字符跨度8个位置，密度=7/8=0.875，奖励=1.0+0.875*0.5=1.4375
            densityBonus = 1.0 + density * 0.5; // 密度奖励系数0.5
        }

        // 5. 连续性奖励：基于连续性比例给予奖励
        double continuityBonus = 1.0 + continuityRatio * 2.0; // 连续性奖励系数2.0

        // 6. 计算最终奖励系数
        double finalBonus = continuityBonus * segmentPenalty * densityBonus;

        // 7. 最终分数 = 基础分数(匹配字符数) × 位置权重 × 综合奖励系数
        double finalScore = matchResult.totalMatches * positionWeight * finalBonus;

        // 检查并处理异常值
        if (Double.isNaN(finalScore) || Double.isInfinite(finalScore)) {
            return 0; // 异常情况下返回0分
        }

        return finalScore;
    }

    /**
     * 匹配结果数据类
     * <p>
     * 封装单次匹配尝试的所有统计信息，用于后续的分数计算。
     *
     * @param completeMatch       是否完全匹配了关键词的所有字符
     * @param totalMatches        总匹配字符数（应该等于关键词长度，如果完全匹配的话）
     * @param maxConsecutive      最长连续匹配长度（如"geek"连续匹配4个字符）
     * @param consecutiveSegments 连续段数量（如"geek.exe"有2个连续段）
     * @param matchSpan           匹配跨度（从第一个匹配字符到最后一个匹配字符的距离+1）
     * @param firstMatchPos       第一个匹配字符在文件名中的位置
     */
    private record MatchResult(boolean completeMatch, int totalMatches, int maxConsecutive,
                               int consecutiveSegments, int matchSpan, int firstMatchPos) {
    }

    /**
     * 计算有意义概率（按单词分别计算）
     * <p>
     * 该方法评估文本中单词的有意义程度，用于判断文件名或应用名的质量。
     * 有意义的单词（如"document", "photo"）比随机字符串（如"xkcd123"）更有价值。
     * <p>
     * 算法流程：
     * 1. 将文本按空格分割成单词
     * 2. 对每个有效单词计算有意义概率（使用GibberishDetectorUtil）
     * 3. 返回所有单词的平均有意义概率
     * <p>
     * 概率范围：[0.0, 1.0]
     * - 0.0：完全无意义的随机字符串
     * - 1.0：完全有意义的真实单词
     * <p>
     * 示例：
     * - "document backup"：两个有意义单词，概率接近1.0
     * - "xkcd123 temp"：一个随机串+一个单词，概率约0.5
     * - "asdfjkl"：随机字符串，概率接近0.0
     *
     * @param text 要分析的文本（文件名或应用名）
     * @return 平均有意义概率，范围[0.0, 1.0]
     */
    private static double calculateMeaningfulProbability(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }

        // 使用正则表达式按空格分割文本为单词
        var pattern = RegexUtil.getPattern("\\s+", 0);
        String[] words = pattern.split(text);
        if (words.length == 0) {
            return 0;
        }

        double totalProbability = 0;
        int validWordCount = 0;

        // 遍历每个单词，计算其有意义概率
        for (String word : words) {
            // 过滤掉空字符串和只包含特殊字符的单词
            if (word != null && !word.isEmpty()) {
                // 使用GibberishDetectorUtil判断单词的有意义程度
                totalProbability += GibberishDetectorUtil.getMeaningfulProbability(word);
                validWordCount++;
            }
        }

        // 返回所有有效单词的平均有意义概率
        // 如果没有有效单词则返回0
        return validWordCount > 0 ? totalProbability / validWordCount : 0;
    }

    /**
     * 创建安全的SearchResult比较器
     * <p>
     * 该比较器确保不会违反比较器契约，即使在计算过程中出现异常值。
     * 使用稳定的排序逻辑，避免NaN和无穷大值导致的比较异常。
     * 排序规则：分数越大的结果排在越前面。
     *
     * @param keywords 搜索关键词数组
     * @return 安全的比较器实例（分数大的在前）
     */
    public static Comparator<SearchResult> createSafeComparator(String[] keywords) {
        return (result1, result2) -> {
            try {
                double rank1 = calculateSearchResultRank(result1, keywords);
                double rank2 = calculateSearchResultRank(result2, keywords);

                // 使用Double.compare确保正确处理特殊值
                // 注意：这里反转比较顺序，让分数大的排在前面
                int comparison = Double.compare(rank2, rank1);

                // 如果分数相同，使用路径作为次要排序条件确保稳定性
                if (comparison == 0) {
                    return result1.getPath().compareTo(result2.getPath());
                }

                return comparison;
            } catch (Exception e) {
                // 异常情况下，使用路径排序作为后备方案
                return result1.getPath().compareTo(result2.getPath());
            }
        };
    }

    /**
     * 创建安全的UwpResult比较器
     * <p>
     * 排序规则：分数越大的结果排在越前面。
     *
     * @param keywords 搜索关键词数组
     * @return 安全的比较器实例（分数大的在前）
     */
    public static Comparator<UwpResult> createSafeUwpComparator(String[] keywords) {
        return (result1, result2) -> {
            try {
                double rank1 = calculateUwpResultRank(result1, keywords);
                double rank2 = calculateUwpResultRank(result2, keywords);

                // 使用Double.compare确保正确处理特殊值
                // 注意：这里反转比较顺序，让分数大的排在前面
                int comparison = Double.compare(rank2, rank1);

                // 如果分数相同，使用显示名称作为次要排序条件确保稳定性
                if (comparison == 0) {
                    return result1.getDisplayName().compareTo(result2.getDisplayName());
                }

                return comparison;
            } catch (Exception e) {
                // 异常情况下，使用显示名称排序作为后备方案
                return result1.getDisplayName().compareTo(result2.getDisplayName());
            }
        };
    }
}
