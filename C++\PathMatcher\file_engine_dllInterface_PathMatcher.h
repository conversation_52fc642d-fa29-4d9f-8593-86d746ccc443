﻿/* DO NOT EDIT THIS FILE - it is machine generated */
#include "jni.h"
/* Header for class file_engine_dllInterface_PathMatcher */

#ifndef _Included_file_engine_dllInterface_PathMatcher
#define _Included_file_engine_dllInterface_PathMatcher
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     file_engine_dllInterface_PathMatcher
 * Method:    prepareSearchInfo
 * Signature: ([Ljava/lang/String;ZLjava/lang/String;[Ljava/lang/String;[Ljava/lang/String;[ZZ)J
 */
JNIEXPORT jlong JNICALL Java_file_engine_dllInterface_PathMatcher_prepareSearchInfo
  (JNIEnv *, jobject, jobjectArray, jboolean, jstring, jobjectArray, jobjectArray, jbooleanArray, jboolean);

/*
 * Class:     file_engine_dllInterface_PathMatcher
 * Method:    releaseSearchInfo
 * Signature: (J)V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_PathMatcher_releaseSearchInfo
  (JNIEnv *, jobject, jlong);

/*
 * Class:     file_engine_dllInterface_PathMatcher
 * Method:    match
 * Signature: (Ljava/lang/String;Ljava/lang/String;JIJILjava/util/function/Consumer;)V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_PathMatcher_match
  (JNIEnv *, jobject, jstring, jstring, jlong, jint, jlong, jint, jobject);

/*
 * Class:     file_engine_dllInterface_PathMatcher
 * Method:    openConnections
 * Signature: ([Ljava/lang/String;)J
 */
JNIEXPORT jlong JNICALL Java_file_engine_dllInterface_PathMatcher_openConnections
  (JNIEnv *, jobject, jobjectArray);

/*
 * Class:     file_engine_dllInterface_PathMatcher
 * Method:    closeConnections
 * Signature: (J)V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_PathMatcher_closeConnections
  (JNIEnv *, jobject, jlong);

/*
 * Class:     file_engine_dllInterface_PathMatcher
 * Method:    iterateUwpApps
 * Signature: (Ljava/util/function/Consumer;)V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_PathMatcher_iterateUwpApps
  (JNIEnv *, jobject, jobject);

/*
 * Class:     file_engine_dllInterface_PathMatcher
 * Method:    refreshUwpApps
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_PathMatcher_refreshUwpApps
  (JNIEnv *, jobject);

/*
 * Class:     file_engine_dllInterface_PathMatcher
 * Method:    cleanupUwpApps
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_file_engine_dllInterface_PathMatcher_cleanupUwpApps
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
