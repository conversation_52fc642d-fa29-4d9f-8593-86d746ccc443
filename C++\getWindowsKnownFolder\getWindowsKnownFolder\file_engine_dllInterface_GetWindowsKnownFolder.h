﻿/* DO NOT EDIT THIS FILE - it is machine generated */
#include "jni.h"
/* Header for class file_engine_dllInterface_GetWindowsKnownFolder */

#ifndef _Included_file_engine_dllInterface_GetWindowsKnownFolder
#define _Included_file_engine_dllInterface_GetWindowsKnownFolder
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     file_engine_dllInterface_GetWindowsKnownFolder
 * Method:    getKnownFolder
 * Signature: (Ljava/lang/String;)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_file_engine_dllInterface_GetWindowsKnownFolder_getKnownFolder
  (JNIEnv *, jobject, jstring);

#ifdef __cplusplus
}
#endif
#endif
