package file.engine.utils;

import file.engine.configs.Constants;
import file.engine.entity.GeneratedCertificate;
import org.bouncycastle.asn1.x500.X500Name;
import org.bouncycastle.asn1.x500.style.RFC4519Style;
import org.bouncycastle.asn1.x509.*;
import org.bouncycastle.cert.X509v3CertificateBuilder;
import org.bouncycastle.cert.jcajce.JcaX509CertificateConverter;
import org.bouncycastle.cert.jcajce.JcaX509ExtensionUtils;
import org.bouncycastle.cert.jcajce.JcaX509v3CertificateBuilder;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.operator.ContentSigner;
import org.bouncycastle.operator.OperatorCreationException;
import org.bouncycastle.operator.jcajce.JcaContentSignerBuilder;

import java.io.*;
import java.math.BigInteger;
import java.security.*;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.Date;
import java.util.UUID;

public class CertificateUtil {

    public static X509Certificate loadCertificate(InputStream is) throws Exception {
        // 创建证书工厂
        CertificateFactory certificateFactory = CertificateFactory.getInstance("X.509", BouncyCastleProvider.PROVIDER_NAME);

        // 生成证书对象
        return (X509Certificate) certificateFactory.generateCertificate(is);
    }

    public static PrivateKey loadPrivateKey(InputStream is) throws Exception {
        // 读取私钥文件
        byte[] keyBytes;
        try (var reader = new BufferedInputStream(is)) {
            keyBytes = reader.readAllBytes();
        }

        String privateKeyContent = new String(keyBytes);
        privateKeyContent = privateKeyContent.replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s", "")
                .trim();
        byte[] decodedKey = java.util.Base64.getDecoder().decode(privateKeyContent);

        // 使用 Bouncy Castle 的 KeyFactory 来生成私钥对象
        KeyFactory keyFactory = KeyFactory.getInstance("RSA", BouncyCastleProvider.PROVIDER_NAME);
        return keyFactory.generatePrivate(new java.security.spec.PKCS8EncodedKeySpec(decodedKey));
    }

    public static GeneratedCertificate generateCertificate(X509Certificate caCert, PrivateKey caPrivateKey)
            throws NoSuchAlgorithmException, OperatorCreationException, CertificateException, KeyStoreException, IOException {
        String provider = BouncyCastleProvider.PROVIDER_NAME;
        var gen = KeyPairGenerator.getInstance("RSA");
        gen.initialize(2048);
        var keyPair = gen.generateKeyPair();
        String password = new BigInteger(256, new SecureRandom()).toString(32);

        // Set the certificate start and end dates
        Date notBefore = new Date(System.currentTimeMillis() - 1000L * 60 * 60 * 24);
        Date notAfter = new Date(System.currentTimeMillis() + (1000L * 60 * 60 * 24 * 365 * 10));

        // Create the certificate builder
        X500Name issuer = new X500Name(RFC4519Style.INSTANCE, caCert.getSubjectX500Principal().getName());
        X500Name subject = new X500Name(Constants.CERTIFICATE_DN);
        BigInteger serialNumber = BigInteger.valueOf(System.currentTimeMillis());

        X509v3CertificateBuilder certificateBuilder = new JcaX509v3CertificateBuilder(
                issuer,          // Issuer (from CA)
                serialNumber,    // Serial Number
                notBefore,       // Valid From
                notAfter,        // Valid Until
                subject,         // Subject
                keyPair.getPublic()  // Public Key
        );

        // Initialize X509ExtensionUtils
        JcaX509ExtensionUtils extensionUtils = new JcaX509ExtensionUtils();

        // Add extensions (e.g., BasicConstraints, KeyUsage)
        certificateBuilder.addExtension(Extension.basicConstraints, true, new BasicConstraints(false));
        certificateBuilder.addExtension(Extension.keyUsage, true, new KeyUsage(KeyUsage.digitalSignature | KeyUsage.keyEncipherment));

        // Authority Key Identifier
        certificateBuilder.addExtension(Extension.authorityKeyIdentifier, false,
                extensionUtils.createAuthorityKeyIdentifier(caCert));

        GeneralNames subjectAltNames = new GeneralNames(
                new GeneralName(GeneralName.dNSName, "localhost")
        );
        certificateBuilder.addExtension(Extension.subjectAlternativeName, false, subjectAltNames);

        // Subject Key Identifier
        certificateBuilder.addExtension(Extension.subjectKeyIdentifier, false,
                extensionUtils.createSubjectKeyIdentifier(keyPair.getPublic()));

        // Build the content signer for signing the certificate with CA's private key
        ContentSigner contentSigner = new JcaContentSignerBuilder("SHA256WithRSAEncryption")
                .setProvider(provider)
                .build(caPrivateKey);

        // Build the certificate
        X509Certificate cert = new JcaX509CertificateConverter()
                .setProvider(provider)
                .getCertificate(certificateBuilder.build(contentSigner));

        // Create the KeyStore and store the generated certificate
        KeyStore keyStore = KeyStore.getInstance("PKCS12");
        keyStore.load(null, null);
        keyStore.setKeyEntry(Constants.CERTIFICATE_ALIAS, keyPair.getPrivate(), password.toCharArray(), new java.security.cert.Certificate[]{cert, caCert});

        String tempPath = System.getProperty("java.io.tmpdir");
        File tempFile = File.createTempFile(UUID.randomUUID().toString(), ".p12", new File(tempPath));
        keyStore.store(new FileOutputStream(tempFile), password.toCharArray());

        return new GeneratedCertificate(password, tempFile);
    }
}
