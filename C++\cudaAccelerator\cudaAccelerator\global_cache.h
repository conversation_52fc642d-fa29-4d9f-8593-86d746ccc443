#pragma once
#include "framework.h"
#include <string>
#include <vector>
#include <unordered_map>

namespace global_cache
{
	using global_cache_data = struct global_cache_struct
	{
		size_t total_bytes = 0;
		char* dev_cache = nullptr;
		std::vector<size_t> dev_str_start_address;
		std::vector<unsigned short> dev_str_len_vec;
		size_t blank_start_address = 0;
		std::unordered_map<size_t, size_t> unique_hash_index_map;
	};

	void insert(const std::vector<std::string>& str_vec);

	void remove(const std::vector<std::string>& str_vec);

	void init(const size_t init_bytes);

	size_t get_str_hash(const std::string& str);

	size_t get_address_by_hash(const size_t hash);

	size_t size();

	std::string copy_to_host_by_hash(const size_t hash);

	bool get_file_name(const char* path, char* output);

	bool get_parent_path(const char* path, char* output);

	void clear();
}