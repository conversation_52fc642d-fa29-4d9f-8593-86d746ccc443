package file.engine.utils.llm;

import file.engine.entity.Args;
import io.github.ollama4j.models.generate.OllamaStreamHandler;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

public interface LLMInterface {
    void init();

    String newSession();

    void removeSession(String sessionId);

    String chat(String sessionId, String message);

    void chat(String sessionId, String message, OllamaStreamHandler streamHandler);

    String chatWithTools(String sessionId, String message, boolean[] toolsCallDone);

    String chatWithImage(String sessionId, String message, List<File> images);

    void registerApi(String apiName,
                     String apiFunctionName,
                     String apiDescription,
                     String apiVersion,
                     List<Args> args,
                     Function<Map<String, Object>, String> func);
}
