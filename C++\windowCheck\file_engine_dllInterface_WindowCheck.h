/* DO NOT EDIT THIS FILE - it is machine generated */
#include "jni.h"
/* Header for class file_engine_dllInterface_WindowCheck */

#ifndef _Included_file_engine_dllInterface_WindowCheck
#define _Included_file_engine_dllInterface_WindowCheck
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     file_engine_dllInterface_WindowCheck
 * Method:    isForegroundFullscreen
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_file_engine_dllInterface_WindowCheck_isForegroundFullscreen
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
