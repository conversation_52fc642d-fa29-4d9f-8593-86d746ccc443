﻿#include "key.h"
#include <Windows.h>

std::string encrypt::getkey()
{
	// 1. 加载 DLL
	HMODULE hDll = LoadLibraryA("encryption.dll");
	if (hDll == nullptr)
	{
		fprintf(stderr, "Failed to load DLL. Error code: %lu\n", GetLastError());
		return "";
	}

	// 2. 获取 getkey 函数地址
	GetKeyFunc getKey = (GetKeyFunc)GetProcAddress(hDll, "getkey");
	if (getKey == nullptr)
	{
		fprintf(stderr, "Failed to get getkey function.Error code : % lu\n", GetLastError());
		FreeLibrary(hDll);
		return "";
	}

	// 3. 调用 getkey 函数
	char* result = getKey();

	std::string hashkey(result == nullptr ? "" : result);
	// 4. 卸载 DLL
	FreeLibrary(hDll);
	return hashkey;
}