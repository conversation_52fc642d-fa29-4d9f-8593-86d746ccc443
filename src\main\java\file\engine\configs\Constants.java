package file.engine.configs;

import lombok.extern.slf4j.Slf4j;

/**
 * 项目使用到的常量
 */
@Slf4j
public class Constants {
    // 数据库中最大的分表ID  list[0-40]
    public static final int MAX_TABLE_NUM = 40;

    // 关闭数据库连接超时时间
    public static final int CLOSE_DATABASE_TIMEOUT_MILLS = 60 * 1000;

    public static final int MAX_SEARCH_TEXT_LENGTH = 300;

    public static final String DATABASE_CREATE_TIME_FILE = "user/databaseCreateTime.dat";

    public static final String DATABASE_INTEGRITY_CHECK_FILE = "user/databaseIntegrityCheck.dat";

    public static final String CONFIG_FILE = "user/settings.json";

    public static final int MAX_TASK_EXIST_TIME = 5 * 60 * 1000;

    public static final int THREAD_POOL_AWAIT_TIMEOUT = 5;

    public static final int ICON_SIZE = 256;

    public static final String CERTIFICATE_DN = "CN=cn, O=o, L=L, ST=il, C=c";

    public static final String CERTIFICATE_ALIAS = "SELF_GENERATED";

    public static final String LUCENE_DATA_BASE_DIR = "data/lucene/";

    public static final String LUCENE_SCANNED_DATABASE_RECORD_FILE = "user/luceneScanned.dat";

    public static final String DATABASE_RESULTS_COUNT_CACHE_FILE = "user/databaseResultsCount.dat";

    public static final int DATABASE_STRUCTURE_VERSION = 1;

    public static final int MAX_SINGLE_FILE_SIZE_LUCENE = 2 * 1024 * 1024;

    // 需要和GPU加速dll中的define宏同时修改
    public static final boolean IS_COMPLEX_GPU_KERNEL_FUNCTION_ENABLED = true;

    public static final double CACHE_INSERTABLE_MAX_OCCUPATION_PERCENTAGE = 0.75;

    public static final int CACHE_SQL_UNION_BATCH_SIZE = 25;

    // 需要和PathMatcher中的SQLITE_CONNECTION_NUMBER同步
    public static final int SQLITE_CONNECTION_OPEN_NUMBER = 4;

    private Constants() {
        throw new RuntimeException("not allowed");
    }

    public static class Enums {

        public enum PathMatchType {
            FULL_MATCHED, FUZZY_MATCHED, NOT_MATCHED;
        }

        /**
         * 数据库运行状态
         * NORMAL：正常
         * _TEMP：正在搜索中，已经切换到临时数据库
         * VACUUM：正在整理数据库
         * MANUAL_UPDATE：正在搜索中，未切换到临时数据库
         */
        public enum DatabaseStatus {
            NORMAL, _TEMP, VACUUM, MANUAL_UPDATE
        }

        public enum LLM {
            OLLAMA, NONE
        }

        public enum CONTENT_READER_TYPE {
            TEXT, EXCEL, WORD, PDF, PICTURE
        }
    }
}
