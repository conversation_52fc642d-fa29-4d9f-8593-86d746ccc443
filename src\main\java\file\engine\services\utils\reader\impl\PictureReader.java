package file.engine.services.utils.reader.impl;

import file.engine.configs.AllConfigs;
import file.engine.entity.AdvancedConfigEntity;
import file.engine.services.utils.reader.ContentReader;
import file.engine.utils.llm.LLMFactory;
import file.engine.utils.llm.LLMInterface;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.tess4j.ITessAPI;
import net.sourceforge.tess4j.Tesseract;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class PictureReader implements ContentReader {
    private static final Tesseract tesseract = new Tesseract();
    private static volatile LLMInterface llm;
    private static final String DATA_PATH = "trained";

    static {
        loadTesseractTrainData();
    }

    private static void loadTesseractTrainData() {
        tesseract.setDatapath(DATA_PATH);
        tesseract.setOcrEngineMode(ITessAPI.TessOcrEngineMode.OEM_TESSERACT_LSTM_COMBINED);

        // 动态检测trained文件夹下的模型文件
        String languages = detectAvailableLanguages();
        if (languages.isEmpty()) {
            // 如果没有检测到模型文件，使用默认语言
            log.warn("未在trained文件夹中检测到任何.traineddata文件，使用默认语言设置");
            tesseract.setLanguage("eng");
        } else {
            tesseract.setLanguage(languages);
            log.info("检测到Tesseract语言模型: {}", languages);
        }
    }

    /**
     * 检测trained文件夹下可用的语言模型
     *
     * @return 用"+"连接的语言代码字符串
     */
    private static String detectAvailableLanguages() {
        try {
            Path trainedPath = Paths.get(DATA_PATH);
            if (!Files.exists(trainedPath) || !Files.isDirectory(trainedPath)) {
                log.warn("trained文件夹不存在或不是目录");
                return "";
            }
            var fileStream = Files.list(trainedPath);

            try (fileStream) {
                // 扫描.traineddata文件并提取语言代码
                var fileSuffix = ".traineddata";
                List<String> languages = fileStream
                        .filter(Files::isRegularFile)
                        .map(Path::getFileName)
                        .map(Path::toString)
                        .filter(fileName -> fileName.endsWith(fileSuffix))
                        .map(fileName -> fileName.substring(0, fileName.lastIndexOf(fileSuffix)))
                        .collect(Collectors.toList());

                if (languages.isEmpty()) {
                    return "";
                }
                // 用"+"连接所有语言代码
                return String.join("+", languages);
            }
        } catch (Exception e) {
            log.error("检测Tesseract语言模型时出错: {}", e.getMessage(), e);
            return "";
        }
    }

    @Override
    public String readContent(String fileAbsPath) {
        try {
            File f = new File(fileAbsPath);
            AllConfigs allConfigs = AllConfigs.getInstance();
            AdvancedConfigEntity advancedConfigEntity = allConfigs.getConfigEntity().getAdvancedConfigEntity();
            if (advancedConfigEntity.isReadPictureByLLM()) {
                if (llm == null) {
                    synchronized (this) {
                        if (llm == null) {
                            var llmOpt = LLMFactory.createLLM(allConfigs.getConfigEntity().getLlm());
                            llm = llmOpt.orElse(null);
                        }
                    }
                }
                if (llm != null) {
                    var llmSessionId = llm.newSession();
                    try {
                        return llm.chatWithImage(llmSessionId, "Describe this image. Output some keywords and split them by ','", List.of(f));
                    } finally {
                        llm.removeSession(llmSessionId);
                    }
                } else {
                    return tesseract.doOCR(f);
                }
            } else {
                return tesseract.doOCR(f);
            }
        } catch (Exception e) {
            log.error("error {}", e.getMessage(), e);
            return "";
        }
    }
}
