package file.engine.services;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import file.engine.annotation.EventListener;
import file.engine.annotation.EventRegister;
import file.engine.configs.AllConfigs;
import file.engine.configs.Constants;
import file.engine.dllInterface.*;
import file.engine.dllInterface.gpu.GPUAccelerator;
import file.engine.entity.AdvancedConfigEntity;
import file.engine.entity.SearchInfoEntity;
import file.engine.entity.SearchResult;
import file.engine.entity.UwpResult;
import file.engine.event.handler.Event;
import file.engine.event.handler.EventManagement;
import file.engine.event.handler.impl.BootSystemEvent;
import file.engine.event.handler.impl.configs.SetConfigsEvent;
import file.engine.event.handler.impl.database.*;
import file.engine.event.handler.impl.database.gpu.GPUAddRecordEvent;
import file.engine.event.handler.impl.database.gpu.GPUClearCacheEvent;
import file.engine.event.handler.impl.database.gpu.GPURemoveRecordEvent;
import file.engine.event.handler.impl.monitor.disk.StartMonitorDiskEvent;
import file.engine.event.handler.impl.stop.CloseEvent;
import file.engine.services.utils.AdminUtil;
import file.engine.services.utils.PathMatchUtil;
import file.engine.services.utils.StringUtf8SumUtil;
import file.engine.services.utils.SymSpellUtil;
import file.engine.services.utils.connection.SQLiteUtil;
import file.engine.utils.HeapMemUtil;
import file.engine.utils.ProcessUtil;
import file.engine.utils.RegexUtil;
import file.engine.utils.ThreadPoolUtil;
import file.engine.utils.file.FileUtil;
import file.engine.utils.gson.GsonUtil;
import file.engine.utils.system.properties.IsDebug;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.wltea.analyzer.cfg.DefaultConfig;
import org.wltea.analyzer.core.IKSegmenter;
import org.wltea.analyzer.core.Lexeme;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.lang.ref.SoftReference;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.attribute.FileTime;
import java.sql.*;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.Period;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class DatabaseService {
    private static boolean isEnableGPUAccelerate = false;
    // 搜索任务队列
    // 预搜索任务map，当发送PrepareSearchEvent后，将会创建预搜索任务，并放入该map中。
    // 发送StartSearchEvent后将会先寻找预搜索任务，成功找到则直接添加进入searchTasksQueue中，不重新创建搜索任务。
    private static final ConcurrentHashMap<SearchInfo, SearchTask> prepareTasksMap = new ConcurrentHashMap<>();
    private final ConcurrentLinkedQueue<SQLWithTaskId> sqlCommandQueue = new ConcurrentLinkedQueue<>();
    //保存每个key所对应的结果数量，数量为0的则直接跳过搜索，不执行SQL查找数据库
    private final ConcurrentHashMap<String, DatabaseRecordInfo> databaseResultsCount = new ConcurrentHashMap<>();
    private final AtomicReference<Constants.Enums.DatabaseStatus> status = new AtomicReference<>(Constants.Enums.DatabaseStatus.NORMAL);
    // 保存从0-40数据库的表，使用频率和名字对应，使经常使用的表最快被搜索到
    private final Set<TableNameWeightInfo> tableSet = ConcurrentHashMap.newKeySet();
    private final AtomicBoolean isDatabaseUpdated = new AtomicBoolean(false);
    private final AtomicBoolean isCheckUnavailableDiskThreadNotExist = new AtomicBoolean(false);
    private ConcurrentLinkedQueue<SuffixPriorityPair> priorityMap = new ConcurrentLinkedQueue<>();
    //tableCache 数据表缓存，在初始化时将会放入所有的key和一个空的cache，后续需要缓存直接放入空的cache中，不再创建新的cache实例
    private final ConcurrentHashMap<String, Cache> tableCache = new ConcurrentHashMap<>();
    // 对数据库cache表的缓存，保存常用的应用
    private final ConcurrentSkipListMap<String, Integer> databaseCacheMap = new ConcurrentSkipListMap<>();
    private final AtomicInteger searchThreadCount = new AtomicInteger(0);

    private static final int MAX_SQL_NUM = 5000;

    private static volatile DatabaseService INSTANCE = null;

    private DatabaseService() {
    }

    public static DatabaseService getInstance() {
        if (INSTANCE == null) {
            synchronized (DatabaseService.class) {
                if (INSTANCE == null) {
                    INSTANCE = new DatabaseService();
                }
            }
        }
        return INSTANCE;
    }

    private void prepareResultContainer(SearchTask searchTask) {
        //准备每个priority对应的容器
        priorityMap.forEach(suffixPriorityPair -> searchTask.tempResults.put(suffixPriorityPair.suffix(), new ConcurrentLinkedQueue<>()));
    }

    private void prepareSearchTasks(SearchTask searchTask) {
        //每个priority用一个线程，每一个后缀名对应一个优先级
        //按照优先级排列，key是sql和表名的对应，value是容器
        var nonFormattedSql = getNonFormattedSqlFromTableQueue(searchTask.searchInfo);
        //添加搜索任务到队列
        addSearchTasks(nonFormattedSql, searchTask);
    }

    private void invalidateAllCache() {
        GPUClearCacheEvent gpuClearCacheEvent = new GPUClearCacheEvent();
        EventManagement eventManagement = EventManagement.getInstance();
        eventManagement.putEvent(gpuClearCacheEvent);
        eventManagement.waitForEvent(gpuClearCacheEvent, 60_000);
        tableCache.values().forEach(Cache::clearCache);
    }

    /**
     * 通过表名获得表的权重信息
     *
     * @param tableName 表名
     * @return 权重信息
     */
    private TableNameWeightInfo getInfoByName(String tableName) {
        for (TableNameWeightInfo each : tableSet) {
            if (each.tableName.equals(tableName)) {
                return each;
            }
        }
        return null;
    }

    /**
     * 更新权重信息
     *
     * @param tableName 表名
     * @param weight    权重
     */
    private void updateTableWeight(String tableName, long weight) {
        TableNameWeightInfo origin = getInfoByName(tableName);
        if (origin == null) {
            return;
        }
        origin.weight.addAndGet(weight);
        String format = String.format("UPDATE weight SET TABLE_WEIGHT=%d WHERE TABLE_NAME='%s'", origin.weight.get(), tableName);
        addToCommandQueue(new SQLWithTaskId(format, SqlTaskIds.UPDATE_WEIGHT, "weight"));
        if (IsDebug.isDebug) {
            log.info("已更新" + tableName + "权重, 之前为" + origin + "***增加了" + weight);
        }
    }

    /**
     * 开始监控磁盘文件变化
     */
    private static synchronized void startMonitorDisks() {
        var threadPoolUtil = ThreadPoolUtil.getInstance();
        var eventManagement = EventManagement.getInstance();
        var allConfigs = AllConfigs.getInstance();
        var databaseService = getInstance();
        if (AdminUtil.isAdmin()) {
            String disks = allConfigs.getAvailableDisks();
            String[] splitDisks = RegexUtil.comma.split(disks);
            for (String root : splitDisks) {
                FileMonitor.INSTANCE.stop_monitor(root);
                while (!FileMonitor.INSTANCE.is_monitor_stopped(root)) {
                    try {
                        TimeUnit.MILLISECONDS.sleep(100);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                    FileMonitor.INSTANCE.stop_monitor(root);
                }
                threadPoolUtil.executeTask(() -> FileMonitor.INSTANCE.monitor(root), false);
            }
            var isCheckUnavailableDiskThreadNotExist = databaseService.isCheckUnavailableDiskThreadNotExist;
            if (isCheckUnavailableDiskThreadNotExist.compareAndSet(false, true)) {
                threadPoolUtil.executeTask(() -> {
                    Set<String> unAvailableDiskSet = allConfigs.getUnAvailableDiskSet();
                    while (eventManagement.notMainExit()) {
                        if (!unAvailableDiskSet.isEmpty()) {
                            for (String unAvailableDisk : unAvailableDiskSet) {
                                if (Files.exists(Path.of(unAvailableDisk)) &&
                                    IsLocalDisk.INSTANCE.isLocalDisk(unAvailableDisk) &&
                                    IsLocalDisk.INSTANCE.isDiskNTFS(unAvailableDisk)) {
                                    FileMonitor.INSTANCE.stop_monitor(unAvailableDisk);
                                    while (!FileMonitor.INSTANCE.is_monitor_stopped(unAvailableDisk)) {
                                        try {
                                            TimeUnit.MILLISECONDS.sleep(100);
                                        } catch (InterruptedException e) {
                                            throw new RuntimeException(e);
                                        }
                                        FileMonitor.INSTANCE.stop_monitor(unAvailableDisk);
                                    }
                                    threadPoolUtil.executeTask(() -> FileMonitor.INSTANCE.monitor(unAvailableDisk), false);
                                    unAvailableDiskSet.remove(unAvailableDisk);
                                }
                            }
                        }
                        try {
                            TimeUnit.SECONDS.sleep(1);
                        } catch (InterruptedException e) {
                            throw new RuntimeException(e);
                        }
                    }
                    boolean expect;
                    do {
                        expect = isCheckUnavailableDiskThreadNotExist.get();
                    } while (!isCheckUnavailableDiskThreadNotExist.compareAndSet(expect, false));
                }, false);
            }
        } else {
            log.warn("Not administrator, file monitoring function is turned off");
        }
    }

    private void searchFolder(String folder, SearchTask searchTask) {
        if (FileUtil.isFileNotExist(folder)) {
            return;
        }
        File path = new File(folder);
        if (searchTask.shouldStopSearch()) {
            return;
        }
        File[] files = path.listFiles();
        if (null == files || files.length == 0) {
            return;
        }
        var remainFiles = new ArrayDeque<>(List.of(files));
        ArrayList<File> dirsToSearch = new ArrayList<>();
        do {
            var eachFile = remainFiles.poll();
            if (eachFile == null) {
                continue;
            }
            if (eachFile.isDirectory()) {
                dirsToSearch.add(eachFile);
                File[] subFiles = eachFile.listFiles();
                if (subFiles != null) {
                    List<File> subFileList = List.of(subFiles);
                    remainFiles.addAll(subFileList);
                    dirsToSearch.addAll(subFileList);
                }
            } else {
                var pathToCheck = eachFile.getAbsolutePath();
                checkIsMatchedAndAddToList(pathToCheck, searchTask);
            }
        } while (!remainFiles.isEmpty() && !searchTask.shouldStopSearch());
        for (var eachDir : dirsToSearch) {
            var pathToCheck = eachDir.getAbsolutePath();
            checkIsMatchedAndAddToList(pathToCheck, searchTask);
        }
    }

    /**
     * 返回满足数据在minRecordNum-maxRecordNum之间的表可以被缓存的表
     *
     * @param disks                硬盘盘符
     * @param tableQueueByPriority 后缀优先级表，从高到低优先级逐渐降低
     * @param isStopCreateCache    是否停止
     * @param minRecordNum         最小数据量
     * @param maxRecordNum         最大数据量
     * @return key为[盘符, 表名, 优先级]，例如 [C,list10,9]，value为实际数据量所占的字节数
     */
    private LinkedHashMap<String, Integer> scanDatabaseAndSelectCacheTable(String[] disks,
                                                                           ConcurrentLinkedQueue<String> tableQueueByPriority,
                                                                           Supplier<Boolean> isStopCreateCache,
                                                                           @SuppressWarnings("SameParameterValue") int minRecordNum,
                                                                           int maxRecordNum) {
        if (minRecordNum > maxRecordNum) {
            throw new RuntimeException("minRecordNum > maxRecordNum");
        }

        // 第一次启动时尝试从缓存文件加载，避免每次都扫描数据库
        if (databaseResultsCount.isEmpty()) {
            loadDatabaseResultsCountCache();
        }

        // 如果仍然为空，则未从文件中加载，扫描数据库
        if (databaseResultsCount.isEmpty()) {
            scanDatabaseWithAggregatedQuery(disks, tableQueueByPriority, isStopCreateCache);
        }

        // 保存数据库统计信息到json
        saveDatabaseResultsCountCache();

        //检查哪些表符合缓存条件，通过表权重依次向下排序
        LinkedHashMap<String, Integer> tableNeedCache = new LinkedHashMap<>();
        for (String diskPath : disks) {
            String disk = String.valueOf(diskPath.charAt(0));
            for (String tableName : tableQueueByPriority) {
                for (SuffixPriorityPair suffixPriorityPair : priorityMap) {
                    if (isStopCreateCache.get()) {
                        return tableNeedCache;
                    }
                    String key = disk + "," + tableName + "," + suffixPriorityPair.priority;
                    DatabaseRecordInfo databaseRecordInfo = databaseResultsCount.get(key);

                    if (databaseRecordInfo != null) {
                        // 使用缓存的数据判断是否可以缓存
                        final int recordCount = databaseRecordInfo.recordCount().get();
                        boolean canBeCached = recordCount > minRecordNum && recordCount <= maxRecordNum;
                        if (canBeCached) {
                            tableNeedCache.put(key, databaseRecordInfo.totalBytes().get());
                        }
                    }
                }
            }
        }

        return tableNeedCache;
    }

    /**
     * 使用聚合SQL查询扫描数据库，大幅提升性能
     * 每个磁盘每个优先级执行一条SQL查询，避免SQLite的UNION数量限制
     */
    private void scanDatabaseWithAggregatedQuery(String[] disks,
                                                 ConcurrentLinkedQueue<String> tableQueueByPriority,
                                                 Supplier<Boolean> isStopCreateCache) {
        // 将priorityMap提取到外层循环，避免SQL过于复杂
        for (SuffixPriorityPair suffixPriorityPair : priorityMap) {
            if (isStopCreateCache.get()) {
                return;
            }

            for (String diskPath : disks) {
                if (isStopCreateCache.get()) {
                    return;
                }

                String disk = String.valueOf(diskPath.charAt(0));

                // 构建单个优先级的聚合SQL查询
                StringBuilder sqlBuilder = new StringBuilder();
                boolean firstQuery = true;

                for (String tableName : tableQueueByPriority) {
                    if (!firstQuery) {
                        sqlBuilder.append(" UNION ALL ");
                    }
                    firstQuery = false;

                    sqlBuilder.append(String.format(
                            "SELECT '%s' as table_name, %d as priority, " +
                            "COUNT(*) as record_count, " +
                            "COALESCE(SUM(LENGTH(folder.PATH || NAME)), 0) as total_bytes " +
                            "FROM %s INNER JOIN folder ON folder.ID = %s.FOLDER_ID " +
                            "WHERE PRIORITY = %d",
                            tableName, suffixPriorityPair.priority,
                            tableName, tableName, suffixPriorityPair.priority
                    ));
                }

                if (sqlBuilder.isEmpty()) {
                    continue;
                }

                // 执行聚合查询
                try (var connection = SQLiteUtil.getDbConnection(disk);
                     Statement stmt = connection.createStatement();
                     ResultSet resultSet = stmt.executeQuery(sqlBuilder.toString())) {

                    while (resultSet.next()) {
                        if (isStopCreateCache.get()) {
                            return;
                        }

                        String tableName = resultSet.getString("table_name");
                        int priority = resultSet.getInt("priority");
                        int recordCount = resultSet.getInt("record_count");
                        int totalBytes = resultSet.getInt("total_bytes");

                        String key = disk + "," + tableName + "," + priority;
                        DatabaseRecordInfo databaseRecordInfo = new DatabaseRecordInfo(
                                new AtomicInteger(totalBytes),
                                new AtomicInteger(recordCount)
                        );
                        databaseResultsCount.put(key, databaseRecordInfo);
                    }

                } catch (SQLException e) {
                    log.error("扫描磁盘 {} 优先级 {} 时出错: {}", disk, suffixPriorityPair.priority, e.getMessage(), e);
                }
            }
        }

        if (IsDebug.isDebug) {
            log.info("数据库聚合扫描完成，共处理 {} 个磁盘，{} 个优先级", disks.length, priorityMap.size());
        }
    }

    private void testSearchThread() {
        ThreadPoolUtil.getInstance().executeTask(() -> {
            long start = System.currentTimeMillis();
            while (EventManagement.getInstance().notMainExit()) {
                if (System.currentTimeMillis() - start > 60 * 1000) {
                    start = System.currentTimeMillis();
                    String keywordsTemp = getRandomString(2) + ";" +
                                          getRandomString(2) + ";" +
                                          getRandomString(2);
                    String[] warmupKeywords = RegexUtil.semicolon.split(keywordsTemp);
                    EventManagement.getInstance().putEvent(new StartSearchEvent(() -> warmupKeywords[0],
                            () -> null,
                            () -> warmupKeywords));
                }
                try {
                    TimeUnit.SECONDS.sleep(1);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        });
    }

    @SuppressWarnings("SameParameterValue")
    private static String getRandomString(int length) {
        String str = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(str.length());
            sb.append(str.charAt(number));
        }
        return sb.toString();
    }

    /**
     * 扫描数据库并添加缓存
     */
    private void saveTableCacheThread() {
        ThreadPoolUtil.getInstance().executeTask(() -> {
            EventManagement eventManagement = EventManagement.getInstance();
            final int checkTimeInterval = 10 * 60 * 1000; // 10 min
            final int startUpLatency = 10 * 1000; // 10s
            var startCheckInfo = new Object() {
                long startCheckTimeMills = System.currentTimeMillis() - checkTimeInterval + startUpLatency;
            };
            final Supplier<Boolean> isStopCreateCache =
                    () -> !eventManagement.notMainExit() ||
                          status.get() == Constants.Enums.DatabaseStatus.MANUAL_UPDATE ||
                          status.get() == Constants.Enums.DatabaseStatus.VACUUM;
            final Supplier<Boolean> isStartSaveCache =
                    () -> (System.currentTimeMillis() - startCheckInfo.startCheckTimeMills > checkTimeInterval &&
                           status.get() == Constants.Enums.DatabaseStatus.NORMAL &&
                           !WindowCheck.INSTANCE.isForegroundFullscreen()) ||
                          (isDatabaseUpdated.get());
            final int createGPUCacheThreshold = 50;
            final int freeGPUCacheThreshold = 70;
            while (eventManagement.notMainExit()) {
                if (isStartSaveCache.get()) {
                    if (isDatabaseUpdated.get()) {
                        isDatabaseUpdated.set(false);
                    }
                    startCheckInfo.startCheckTimeMills = System.currentTimeMillis();

                    createMemoryCache(isStopCreateCache);
                    if (isEnableGPUAccelerate) {
                        final int gpuMemUsage = GPUAccelerator.INSTANCE.getGPUMemUsage();
                        if (gpuMemUsage < createGPUCacheThreshold) {
                            createGpuCache(isStopCreateCache, createGPUCacheThreshold);
                        }
                    }
                } else {
                    if (isEnableGPUAccelerate) {
                        final int gpuMemUsage = GPUAccelerator.INSTANCE.getGPUMemUsage();
                        if (gpuMemUsage >= freeGPUCacheThreshold) {
                            // 防止显存占用超过70%后仍然扫描数据库
                            startCheckInfo.startCheckTimeMills = System.currentTimeMillis();
                            if (GPUAccelerator.INSTANCE.hasCache()) {
                                log.info("由于显存占用过多，清除GPU缓存");
                                GPUClearCacheEvent gpuClearCacheEvent = new GPUClearCacheEvent();
                                eventManagement.putEvent(gpuClearCacheEvent);
                                eventManagement.waitForEvent(gpuClearCacheEvent);
                            }
                        }
                    }
                }
                try {
                    TimeUnit.SECONDS.sleep(1);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }, false);
    }

    private void createMemoryCache(Supplier<Boolean> isStopCreateCache) {
        log.info("添加缓存");
        AdvancedConfigEntity advancedConfigEntity = AllConfigs.getInstance().getConfigEntity().getAdvancedConfigEntity();
        String availableDisks = AllConfigs.getInstance().getAvailableDisks();
        ConcurrentLinkedQueue<String> tableQueueByPriority = initTableQueueByPriority();
        String[] disks = RegexUtil.comma.split(availableDisks);
        LinkedHashMap<String, Integer> tableNeedCache = scanDatabaseAndSelectCacheTable(disks,
                tableQueueByPriority,
                isStopCreateCache,
                advancedConfigEntity.getMinCacheBlockNumber(),
                advancedConfigEntity.getMaxCacheBlockNumber());
        saveTableCache(isStopCreateCache, tableNeedCache);
        log.info("添加完成");
    }

    @SuppressWarnings("SameParameterValue")
    private void createGpuCache(Supplier<Boolean> isStopCreateCache, int createGpuCacheThreshold) {
        log.info("添加gpu缓存");
        AdvancedConfigEntity advancedConfigEntity = AllConfigs.getInstance().getConfigEntity().getAdvancedConfigEntity();
        String availableDisks = AllConfigs.getInstance().getAvailableDisks();
        ConcurrentLinkedQueue<String> tableQueueByPriority = initTableQueueByPriority();
        String[] disks = RegexUtil.comma.split(availableDisks);
        LinkedHashMap<String, Integer> tableNeedCache = scanDatabaseAndSelectCacheTable(disks,
                tableQueueByPriority,
                isStopCreateCache,
                advancedConfigEntity.getMinGpuCacheBlockNumber(),
                advancedConfigEntity.getMaxGpuCacheBlockNumber());
        saveTableCacheForGPU(isStopCreateCache, tableNeedCache, createGpuCacheThreshold);
        log.info("添加完成");
    }

    /**
     * 缓存数据表
     *
     * @param isStopCreateCache 是否停止
     * @param tableNeedCache    需要缓存的表
     */
    private void saveTableCache(Supplier<Boolean> isStopCreateCache, LinkedHashMap<String, Integer> tableNeedCache) {
        // 按磁盘分组需要缓存的表
        Map<String, List<Map.Entry<String, Cache>>> cachesByDisk = new HashMap<>();

        for (Map.Entry<String, Cache> entry : tableCache.entrySet()) {
            String key = entry.getKey();
            Cache cache = entry.getValue();

            if (tableNeedCache.containsKey(key)) {
                //当前表可以被缓存
                if (HeapMemUtil.getInstance().getHeapUsage() < Constants.CACHE_INSERTABLE_MAX_OCCUPATION_PERCENTAGE &&
                    !cache.isCacheValid()) {
                    String[] info = RegexUtil.comma.split(key);
                    String disk = info[0];
                    cachesByDisk.computeIfAbsent(disk, k -> new ArrayList<>()).add(entry);
                }
            } else {
                if (cache.isCached.get()) {
                    cache.clearCache();
                }
            }
        }

        // 批量处理每个磁盘的缓存
        for (Map.Entry<String, List<Map.Entry<String, Cache>>> diskEntry : cachesByDisk.entrySet()) {
            String disk = diskEntry.getKey();
            List<Map.Entry<String, Cache>> diskCaches = diskEntry.getValue();

            // 批处理数据
            int diskCacheSize = diskCaches.size();
            for (int i = 0; i < diskCacheSize; i += Constants.CACHE_SQL_UNION_BATCH_SIZE) {
                if (isStopCreateCache.get()) {
                    return;
                }

                int endIndex = Math.min(i + Constants.CACHE_SQL_UNION_BATCH_SIZE, diskCacheSize);
                List<Map.Entry<String, Cache>> batch = diskCaches.subList(i, endIndex);

                processCacheBatch(disk, batch, isStopCreateCache);
            }
        }
    }

    /**
     * 批量处理缓存
     *
     * @param disk              磁盘标识
     * @param batch             批量处理的缓存条目
     * @param isStopCreateCache 是否停止创建缓存
     */
    private void processCacheBatch(String disk, List<Map.Entry<String, Cache>> batch, Supplier<Boolean> isStopCreateCache) {
        if (batch.isEmpty()) {
            return;
        }

        try (var connection = SQLiteUtil.getDbConnection(disk);
             Statement stmt = connection.createStatement()) {

            // 构建批量查询SQL
            StringBuilder sqlBuilder = new StringBuilder();
            Map<String, Cache> keyToCacheMap = new HashMap<>();

            for (int i = 0; i < batch.size(); i++) {
                Map.Entry<String, Cache> entry = batch.get(i);
                String key = entry.getKey();
                Cache cache = entry.getValue();

                String[] info = RegexUtil.comma.split(key);
                String tableName = info[1];
                String priority = info[2];

                if (i > 0) {
                    sqlBuilder.append(" UNION ALL ");
                }

                sqlBuilder.append(String.format(
                        "SELECT '%s' as CACHE_KEY, folder.PATH || NAME as PATH FROM %s " +
                        "INNER JOIN folder ON folder.ID = %s.FOLDER_ID " +
                        "WHERE PRIORITY=%s",
                        key, tableName, tableName, priority
                ));

                keyToCacheMap.put(key, cache);
            }

            // 执行批量查询
            try (ResultSet resultSet = stmt.executeQuery(sqlBuilder.toString())) {
                Map<String, List<String>> pathsByKey = new HashMap<>();

                while (resultSet.next()) {
                    if (isStopCreateCache.get()) {
                        return;
                    }

                    String cacheKey = resultSet.getString("CACHE_KEY");
                    String path = resultSet.getString("PATH");

                    pathsByKey.computeIfAbsent(cacheKey, k -> new ArrayList<>()).add(path);
                }

                // 批量保存到各个cache
                for (Map.Entry<String, List<String>> pathEntry : pathsByKey.entrySet()) {
                    String cacheKey = pathEntry.getKey();
                    List<String> paths = pathEntry.getValue();
                    Cache cache = keyToCacheMap.get(cacheKey);

                    if (cache != null) {
                        cache.savePathCacheBatch(paths);
                        cache.isCached.set(true);
                        cache.isFileLost.set(false);
                    }
                }
            }

        } catch (SQLException e) {
            log.error("error: {}", e.getMessage(), e);
        }
    }

    /**
     * 缓存数据表到显存中
     *
     * @param isStopCreateCache 是否停止
     * @param tableNeedCache    需要缓存的表
     */
    private void saveTableCacheForGPU(Supplier<Boolean> isStopCreateCache,
                                      LinkedHashMap<String, Integer> tableNeedCache,
                                      int createGpuCacheThreshold) {
        var allConfigs = AllConfigs.getInstance();

        // 按磁盘分组需要缓存的表
        Map<String, List<String>> gpuCachesByDisk = new HashMap<>();

        for (String diskPath : RegexUtil.comma.split(allConfigs.getAvailableDisks())) {
            for (int i = 0; i <= Constants.MAX_TABLE_NUM; i++) {
                for (var suffixPriorityPair : priorityMap) {
                    var key = diskPath.charAt(0) + "," + "list" + i + "," + suffixPriorityPair.priority;
                    if (tableNeedCache.containsKey(key)) {
                        //超过128M字节或已存在缓存
                        var memoryCache = tableCache.get(key);
                        if (GPUAccelerator.INSTANCE.isCacheExist(key) ||
                            tableNeedCache.get(key) > 128 * 1024 * 1024 ||
                            (memoryCache != null && memoryCache.isCacheValid())) {
                            continue;
                        }
                        String disk = String.valueOf(diskPath.charAt(0));
                        gpuCachesByDisk.computeIfAbsent(disk, k -> new ArrayList<>()).add(key);
                    }
                }
            }
        }

        // 批量处理每个磁盘的GPU缓存
        for (Map.Entry<String, List<String>> diskEntry : gpuCachesByDisk.entrySet()) {
            String disk = diskEntry.getKey();
            List<String> diskGpuCaches = diskEntry.getValue();

            // 批处理数据
            int diskCacheSize = diskGpuCaches.size();
            for (int i = 0; i < diskCacheSize; i += Constants.CACHE_SQL_UNION_BATCH_SIZE) {
                if (isStopCreateCache.get()) {
                    return;
                }

                int endIndex = Math.min(i + Constants.CACHE_SQL_UNION_BATCH_SIZE, diskCacheSize);
                List<String> batch = diskGpuCaches.subList(i, endIndex);

                if (processGpuCacheBatch(disk, batch, isStopCreateCache, createGpuCacheThreshold)) {
                    // 如果GPU内存使用率超过阈值，停止处理
                    return;
                }
            }
        }
    }

    /**
     * 批量处理GPU缓存
     *
     * @param disk                    磁盘标识
     * @param batch                   批量处理的缓存key
     * @param isStopCreateCache       是否停止创建缓存
     * @param createGpuCacheThreshold GPU缓存阈值
     * @return 是否因为GPU内存使用率过高而停止
     */
    private boolean processGpuCacheBatch(String disk, List<String> batch,
                                         Supplier<Boolean> isStopCreateCache,
                                         int createGpuCacheThreshold) {
        if (batch.isEmpty()) {
            return false;
        }

        try (var connection = SQLiteUtil.getDbConnection(disk);
             Statement stmt = connection.createStatement()) {

            // 构建批量查询SQL
            StringBuilder sqlBuilder = new StringBuilder();

            for (int i = 0; i < batch.size(); i++) {
                String key = batch.get(i);
                String[] info = RegexUtil.comma.split(key);
                String tableName = info[1];
                String priority = info[2];

                if (i > 0) {
                    sqlBuilder.append(" UNION ALL ");
                }

                sqlBuilder.append(String.format(
                        "SELECT '%s' as GPU_CACHE_KEY, folder.PATH || NAME as PATH FROM %s " +
                        "INNER JOIN folder ON folder.ID = %s.FOLDER_ID " +
                        "WHERE PRIORITY=%s",
                        key, tableName, tableName, priority
                ));
            }

            // 执行批量查询
            try (ResultSet resultSet = stmt.executeQuery(sqlBuilder.toString())) {
                Map<String, List<String>> pathsByKey = new HashMap<>();

                while (resultSet.next()) {
                    if (isStopCreateCache.get()) {
                        return false;
                    }

                    String gpuCacheKey = resultSet.getString("GPU_CACHE_KEY");
                    String path = resultSet.getString("PATH");

                    pathsByKey.computeIfAbsent(gpuCacheKey, k -> new ArrayList<>()).add(path);
                }

                // 批量初始化GPU缓存
                for (Map.Entry<String, List<String>> pathEntry : pathsByKey.entrySet()) {
                    String cacheKey = pathEntry.getKey();
                    List<String> paths = pathEntry.getValue();

                    if (!paths.isEmpty()) {
                        GPUAccelerator.INSTANCE.initCache(cacheKey, paths.toArray(new String[0]));

                        if (isStopCreateCache.get()) {
                            return false;
                        }

                        var usage = GPUAccelerator.INSTANCE.getGPUMemUsage();
                        if (usage > createGpuCacheThreshold) {
                            return true; // 返回true表示因为GPU内存使用率过高而停止
                        }
                    }
                }
            }

        } catch (SQLException e) {
            log.error("error: {}", e.getMessage(), e);
        }

        return false;
    }

    private static long getFileLastModifiedTime(String path) {
        long modifyDate = 0;
        try {
            FileTime lastModifiedTime = Files.getLastModifiedTime(Path.of(path));
            Instant timeStampStart = Instant.parse("1970-01-01T00:00:00Z");
            Instant fileTimeStart = Instant.parse("1601-01-01T00:00:00Z");
            Duration between = Duration.between(fileTimeStart, timeStampStart);
            modifyDate = lastModifiedTime.toMillis() + between.toMillis();
            modifyDate *= 10_000;
        } catch (IOException e) {
            log.error("error {}", e.getMessage(), e);
        }
        return modifyDate;
    }

    private void addRestartMonitorThread() {
        ThreadPoolUtil.getInstance().executeTask(() -> {
            var eventManagement = EventManagement.getInstance();
            var startTime = System.currentTimeMillis();
            while (eventManagement.notMainExit()) {
                if (System.currentTimeMillis() - startTime > AllConfigs.getInstance().
                        getConfigEntity().
                        getAdvancedConfigEntity().
                        getRestartMonitorDiskThreadTimeoutInMills()) {
                    startTime = System.currentTimeMillis();
                    eventManagement.putEvent(new StartMonitorDiskEvent());
                }
                try {
                    TimeUnit.MILLISECONDS.sleep(10);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        });
    }

    private void syncFileChangesThread() {
        ThreadPoolUtil.getInstance().executeTask(() -> {
            try {
                addFileChangesRecords();
            } catch (Exception e) {
                log.error("error: {}", e.getMessage(), e);
            }
        }, false);
    }

    private void addFileChangesRecords() {
        var eventManagement = EventManagement.getInstance();
        String tempPath = System.getProperty("java.io.tmpdir");
        while (eventManagement.notMainExit()) {
            String addFilePath = FileMonitor.INSTANCE.pop_add_file();
            String deleteFilePath = FileMonitor.INSTANCE.pop_del_file();
            if (addFilePath != null && !addFilePath.contains(tempPath)) {
                File addFile = new File(addFilePath);
                var dirQueue = new ArrayDeque<File>();
                if (addFile.isDirectory()) {
                    dirQueue.add(addFile);
                }
                do {
                    if (addFile.getParentFile() != null) {
                        try {
                            addFileToDatabase(addFile.getAbsolutePath());
                        } catch (Exception e) {
                            log.error("error: {}", e.getMessage(), e);
                        }
                    }
                } while ((addFile = addFile.getParentFile()) != null);
                ThreadPoolUtil.getInstance().executeTask(() -> {
                    // start in 1 min
                    long beginTime = System.currentTimeMillis() + 60_000;
                    while (eventManagement.notMainExit() && System.currentTimeMillis() < beginTime) {
                        try {
                            TimeUnit.SECONDS.sleep(1);
                        } catch (InterruptedException e) {
                            throw new RuntimeException(e);
                        }
                    }
                    // start
                    File remain;
                    while ((remain = dirQueue.poll()) != null && eventManagement.notMainExit()) {
                        try {
                            addFileToDatabase(remain.getAbsolutePath());
                        } catch (Exception e) {
                            log.error("error: {}", e.getMessage(), e);
                        }
                        File[] subFiles = remain.listFiles();
                        if (subFiles == null) {
                            continue;
                        }
                        for (File eachFile : subFiles) {
                            try {
                                addFileToDatabase(eachFile.getAbsolutePath());
                            } catch (Exception e) {
                                log.error("error: {}", e.getMessage(), e);
                            }
                            if (eachFile.isDirectory()) {
                                dirQueue.add(eachFile);
                            }
                        }
                    }
                });
            }
            if (deleteFilePath != null && !deleteFilePath.contains(tempPath)) {
                try {
                    removeFileFromDatabase(deleteFilePath);
                } catch (Exception e) {
                    log.error("error: {}", e.getMessage(), e);
                }
            }
            try {
                TimeUnit.MILLISECONDS.sleep(1);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public Map<String, ConcurrentLinkedQueue<SearchResult>> getFrequentlyUsedCachesMerged(int num,
                                                                                          boolean onlyFile,
                                                                                          SearchInfoEntity searchInfoEntity) {
        SearchInfo searchInfo;
        if (searchInfoEntity != null) {
            searchInfo = prepareSearchKeywords(searchInfoEntity.getSearchText(),
                    searchInfoEntity.getSearchCase(),
                    searchInfoEntity.getKeywords(),
                    searchInfoEntity.getMaxResultNum());
        } else {
            searchInfo = null;
        }

        boolean enableFuzzyMatch = AllConfigs.getInstance().getConfigEntity().isEnableFuzzyMatch();
        List<String> cacheResult = getFrequentlyUsedCaches(num, onlyFile);
        if (searchInfo != null) {
            cacheResult = cacheResult.stream()
                    .filter(cache -> PathMatchUtil.check(FileUtil.getFileName(cache),
                            FileUtil.getParentPath(cache),
                            searchInfo.searchCase(),
                            searchInfo.isIgnoreCase(),
                            searchInfo.searchText(),
                            searchInfo.keywords(),
                            searchInfo.keywordsLowerCase(),
                            searchInfo.isKeywordPath(),
                            enableFuzzyMatch) != Constants.Enums.PathMatchType.NOT_MATCHED)
                    .toList();
        }

        Map<String, ConcurrentLinkedQueue<SearchResult>> cacheContainer = new LinkedHashMap<>();
        priorityMap.forEach(suffixPriorityPair -> cacheContainer.put(suffixPriorityPair.suffix(), new ConcurrentLinkedQueue<>()));
        cacheResult.stream()
                .map(each -> SearchResult.buildFromPath(true, each, false, false).orElse(null))
                .filter(Objects::nonNull)
                .forEach(each -> addSearchResultToContainer(cacheContainer, each));
        return cacheContainer;
    }

    public List<String> getFrequentlyUsedCaches(int num, boolean onlyFile) {
        Stream<String> stringStream = databaseCacheMap.entrySet()
                .stream()
                .sorted((o1, o2) -> o2.getValue() - o1.getValue())
                .map(Map.Entry::getKey);
        if (onlyFile) {
            stringStream = stringStream.filter(FileUtil::isFile);
        }
        if (num == -1) {
            return stringStream.toList();
        } else {
            return stringStream.limit(num).toList();
        }
    }

    public Set<String> getCache() {
        return new LinkedHashSet<>(databaseCacheMap.keySet());
    }

    /**
     * 将缓存中的文件保存到cacheSet中
     */
    private void prepareDatabaseCache() {
        try (var connection = SQLiteUtil.getDbConnection("cache");
             Statement statement = connection.createStatement();
             ResultSet resultSet = statement.executeQuery("select cache.PATH, COUNT from cache left join statistics s on cache.PATH = s.PATH;")) {
            while (resultSet.next()) {
                String eachLine = resultSet.getString("PATH");
                Object countObj = resultSet.getObject("COUNT");
                int count = 1;
                if (countObj != null) {
                    count = Integer.parseInt(String.valueOf(countObj));
                }
                if (count > Integer.MAX_VALUE - 100_000) {
                    addToCommandQueue(new SQLWithTaskId("update statistics set COUNT = COUNT/2;", SqlTaskIds.UPDATE_STATISTICS, "cache"));
                } else if (count <= 0) {
                    count = 1;
                    addToCommandQueue(new SQLWithTaskId("update statistics set COUNT = 1 where PATH='" + eachLine + "';", SqlTaskIds.UPDATE_STATISTICS, "cache"));
                }
                databaseCacheMap.put(eachLine, count);
            }
        } catch (Exception e) {
            log.error("error: {}", e.getMessage(), e);
        }
    }

    /**
     * 从缓存中搜索结果并将匹配的放入listResults
     */
    private void searchCache(SearchTask searchTask) {
        var eventManagement = EventManagement.getInstance();
        HashSet<String> dirs = new HashSet<>();
        for (var each : getFrequentlyUsedCaches(-1, false)) {
            if (FileUtil.isFileNotExist(each)) {
                eventManagement.putEvent(new DeleteFromCacheEvent(each));
            } else {
                if (FileUtil.isDir(each)) {
                    dirs.add(each);
                } else {
                    checkIsMatchedAndAddToList(each, searchTask);
                }
            }
            if (searchTask.shouldStopSearch()) {
                return;
            }
        }
        for (String each : dirs) {
            if (FileUtil.isFileNotExist(each)) {
                eventManagement.putEvent(new DeleteFromCacheEvent(each));
            } else {
                checkIsMatchedAndAddToList(each, searchTask);
            }
            if (searchTask.shouldStopSearch()) {
                return;
            }
        }
    }

    private void checkIsMatchedAndAddToList(String path,
                                            SearchTask searchTask) {
        String fileName = FileUtil.getFileName(path);
        String parentPath = FileUtil.getParentPath(path);
        checkIsMatchedAndAddToList(true, fileName, parentPath, searchTask);
    }

    private void checkIsMatchedAndAddToUwpList(@NonNull UwpResult uwpResult,
                                               SearchTask searchTask) {
        if (searchTask.shouldStopSearch()) {
            return;
        }
        boolean enableFuzzyMatch = AllConfigs.getInstance().getConfigEntity().isEnableFuzzyMatch();
        Constants.Enums.PathMatchType matchType = PathMatchUtil.check(uwpResult.getDisplayName(),
                "",
                searchTask.searchInfo.searchCase,
                searchTask.searchInfo.isIgnoreCase,
                searchTask.searchInfo.searchText,
                searchTask.searchInfo.keywords,
                searchTask.searchInfo.keywordsLowerCase,
                searchTask.searchInfo.isKeywordPath,
                enableFuzzyMatch);
        if (matchType == Constants.Enums.PathMatchType.FULL_MATCHED ||
            matchType == Constants.Enums.PathMatchType.FUZZY_MATCHED) {
            //字符串匹配通过
            searchTask.resultCounter.getAndIncrement();
            uwpResult.setHighlightName(searchTask.searchInfo);
            if (!searchTask.uwpResults.contains(uwpResult)) {
                searchTask.uwpResults.add(uwpResult);
            }
        }
    }

    /**
     * 检查文件路径是否匹配然后加入到列表
     */
    private boolean checkIsMatchedAndAddToList(boolean prioritizeResult,
                                               String fileName,
                                               String parentPath,
                                               SearchTask searchTask) {
        if (searchTask.shouldStopSearch()) {
            return false;
        }
        boolean enableFuzzyMatch = AllConfigs.getInstance().getConfigEntity().isEnableFuzzyMatch();
        Constants.Enums.PathMatchType matchType = PathMatchUtil.check(fileName,
                parentPath,
                searchTask.searchInfo.searchCase,
                searchTask.searchInfo.isIgnoreCase,
                searchTask.searchInfo.searchText,
                searchTask.searchInfo.keywords,
                searchTask.searchInfo.keywordsLowerCase,
                searchTask.searchInfo.isKeywordPath,
                enableFuzzyMatch);
        boolean fuzzyMatched = matchType == Constants.Enums.PathMatchType.FUZZY_MATCHED;
        if (matchType == Constants.Enums.PathMatchType.FULL_MATCHED ||
            matchType == Constants.Enums.PathMatchType.FUZZY_MATCHED) {
            //字符串匹配通过
            String path = parentPath + File.separator + fileName;
            if (FileUtil.isFileNotExist(path)) {
                removeFileFromDatabase(path);
            } else if (searchTask.tempResultsSet.add(path)) {
                searchTask.resultCounter.getAndIncrement();
                SearchResult.buildFromPath(prioritizeResult, path, false, fuzzyMatched)
                        .ifPresent(
                                searchResult -> addSearchResultToContainer(searchTask.tempResults, searchResult)
                        );
            }
            return true;
        }
        return false;
    }

    /**
     * 提取表名中的数字
     *
     * @param tableName 表名，如 "list10"
     * @return 数字，如 10
     */
    private int extractTableNumber(String tableName) {
        try {
            return Integer.parseInt(tableName.substring(4)); // 去掉"list"前缀
        } catch (NumberFormatException | StringIndexOutOfBoundsException e) {
            return 0; // 如果解析失败，返回0
        }
    }

    /**
     * 根据表名数字计算权重调整因子
     * 数字越小（文件名越短）得分越高，10-30区间适当提升权重
     * 使用乘法因子而不是加法，避免数量级不匹配问题
     *
     * @param tableNumber 表名数字
     * @return 权重调整因子（乘法因子）
     */
    private double getTableNumberBonus(int tableNumber) {
        if (tableNumber <= 10) {
            // 0-10: 文件名很短
            return 1.2;
        } else if (tableNumber <= 20) {
            // 10-20: 较短文件名
            return 1.5;
        } else if (tableNumber <= 30) {
            // 20-30: 中等长度文件名
            return 1.3;
        } else if (tableNumber <= 35) {
            // 30-35: 较长文件名
            return 1.0;
        } else {
            // 35-40: 最长文件名（可能包含乱码），降低权重（0.8倍）
            return 0.8;
        }
    }

    /**
     * 根据优先级将表排序放入tableQueue
     */
    private ConcurrentLinkedQueue<String> initTableQueueByPriority() {
        ConcurrentLinkedQueue<String> tableQueue = new ConcurrentLinkedQueue<>();
        ArrayList<TableNameWeightInfo> tmpCommandList = new ArrayList<>(tableSet);
        //将tableSet通过权重和表名数字综合排序
        tmpCommandList.sort((o1, o2) -> {
            // 提取表名中的数字
            int num1 = extractTableNumber(o1.tableName);
            int num2 = extractTableNumber(o2.tableName);

            // list40永远排在最后，不管权重多高
            if (num1 == Constants.MAX_TABLE_NUM && num2 != Constants.MAX_TABLE_NUM) {
                return 1; // o1是list40，排在后面
            }
            if (num1 != Constants.MAX_TABLE_NUM && num2 == Constants.MAX_TABLE_NUM) {
                return -1; // o2是list40，排在后面
            }

            // 非list40的表按综合得分排序
            long weight1 = o1.weight.get();
            long weight2 = o2.weight.get();

            // 计算综合得分：权重 * 表名数字的调整因子
            // 使用乘法因子避免数量级不匹配问题
            double score1 = weight1 * getTableNumberBonus(num1);
            double score2 = weight2 * getTableNumberBonus(num2);

            return Double.compare(score2, score1); // 降序排列
        });
        for (TableNameWeightInfo each : tmpCommandList) {
            if (IsDebug.isDebug) {
                log.info("已添加表" + each.tableName + "----权重" + each.weight.get());
            }
            tableQueue.add(each.tableName);
        }
        return tableQueue;
    }

    /**
     * 初始化所有表名和权重信息，不要移动到构造函数中，否则会造成死锁
     * 在该任务前可能会有设置搜索框颜色等各种任务，这些任务被设置为异步，若在构造函数未执行完成时，会造成无法构造实例
     */
    private void initTableMap() {
        boolean isNeedSubtract = false;
        HashMap<String, Integer> weights = queryAllWeights();
        if (!weights.isEmpty()) {
            for (int i = 0; i <= Constants.MAX_TABLE_NUM; i++) {
                Integer weight = weights.get("list" + i);
                if (weight == null) {
                    weight = 0;
                }
                if (weight > 100_000_000) {
                    isNeedSubtract = true;
                }
                tableSet.add(new TableNameWeightInfo("list" + i, weight));
            }
        } else {
            for (int i = 0; i <= Constants.MAX_TABLE_NUM; i++) {
                tableSet.add(new TableNameWeightInfo("list" + i, 0));
            }
        }
        if (isNeedSubtract) {
            tableSet.forEach(tableNameWeightInfo -> tableNameWeightInfo.weight.set(tableNameWeightInfo.weight.get() / 2));
        }
    }

    /**
     * 根据上面分配的位信息，从第二位开始，与taskStatus做与运算，并向右偏移，若结果为1，则表示该任务完成
     */
    private void waitForTasks(SearchTask searchTask, CountDownLatch countDownLatch) {
        long startTime = System.currentTimeMillis();
        try {
            if (!countDownLatch.await(60, TimeUnit.SECONDS)) {
                var eventManagement = EventManagement.getInstance();
                final long startWaiting = System.currentTimeMillis();
                var allConfigs = AllConfigs.getInstance();
                while (searchThreadCount.get() != 0 &&
                       eventManagement.notMainExit() &&
                       System.currentTimeMillis() - startWaiting < allConfigs
                               .getConfigEntity()
                               .getAdvancedConfigEntity()
                               .getWaitForSearchTasksTimeoutInMills()) {
                    TimeUnit.MILLISECONDS.sleep(1);
                }
            }
        } catch (Exception e) {
            log.error("error: {}", e.getMessage(), e);
        } finally {
            long endTime = System.currentTimeMillis();
            searchTask.taskExecutionDuration = endTime - startTime;
            searchDone(searchTask);
        }
    }

    private void searchDone(SearchTask searchTask) {
        EventManagement eventManagement = EventManagement.getInstance();
        if (isEnableGPUAccelerate && eventManagement.notMainExit()) {
            GPUAccelerator.INSTANCE.stopCollectResults();
        }
        searchTask.searchDoneFlag = true;
        synchronized (PathMatcher.class) {
            PathMatcher.INSTANCE.closeConnections(searchTask.connectionId);
        }
        PathMatcher.INSTANCE.releaseSearchInfo(searchTask.preparedSearchInfoId);
    }

    /**
     * 创建搜索任务
     * nonFormattedSql将会生成从list0-40，根据priority从高到低排序的SQL语句
     * 生成任务顺序会根据list的权重和priority来生成
     *
     * @param nonFormattedSql 未格式化搜索字段的SQL
     */
    private void addSearchTasks(ArrayList<List<SearchTableAndPriority>> nonFormattedSql, SearchTask searchTask) {
        AllConfigs allConfigs = AllConfigs.getInstance();
        String availableDisks = allConfigs.getAvailableDisks();
        String[] disks = RegexUtil.comma.split(availableDisks);

        synchronized (PathMatcher.class) {
            ArrayList<String> dbPathArray = new ArrayList<>();
            for (String eachDisk : disks) {
                var eachDiskKey = String.valueOf(eachDisk.charAt(0));
                dbPathArray.add(SQLiteUtil.getDbAbsolutePath(eachDiskKey));
            }
            var connectionId = PathMatcher.INSTANCE.openConnections(dbPathArray.toArray(new String[0]));
            if (connectionId == 0) {
                throw new RuntimeException("Failed to establish database connections");
            }
            searchTask.setConnectionId(connectionId);
        }

        for (String eachDisk : disks) {
            var taskQueue = new ConcurrentLinkedQueue<Runnable>();
            searchTask.taskMap.put(eachDisk, taskQueue);
            for (var commandsList : nonFormattedSql) {
                //向任务队列tasks添加任务
                //每一个任务负责查询一个list表和100个priority生成的SQL
                addTaskForDatabase0(eachDisk, taskQueue, commandsList, searchTask.preparedSearchInfoId, searchTask);
            }
        }
    }

    private void addTaskForDatabase0(String diskChar,
                                     ConcurrentLinkedQueue<Runnable> tasks,
                                     List<SearchTableAndPriority> sqlToExecute,
                                     long preparedSearchInfoId,
                                     SearchTask searchTask) {
        boolean contentSearch = searchTask.searchInfo.searchCase != null &&
                                List.of(searchTask.searchInfo.searchCase).contains(PathMatchUtil.SearchCase.CONTENT);
        if (contentSearch) {
            return;
        }
        tasks.add(() -> {
            String diskStr = String.valueOf(diskChar.charAt(0));
            ArrayList<String> unionSqlList = new ArrayList<>();
            for (var sqlAndTableName : sqlToExecute) {
                String tableName = sqlAndTableName.table();
                Integer priority = sqlAndTableName.priority();
                String key = diskStr + "," + tableName + "," + priority;
                long matchedNum = 0L;

                Cache cache = tableCache.get(key);
                if (cache != null && cache.isCacheValid()) {
                    if (IsDebug.isDebug) {
                        log.info("从缓存中读取 {}", key);
                    }
                    matchedNum = cache.parallelSearch(s -> {
                        String parentPath = Cache.parentPathCacheMap.get(s.parentPathId());
                        if (parentPath == null) {
                            log.warn("未找到id为{}的父文件夹路径，存在数据丢失", s.parentPathId());
                            return false;
                        }
                        return checkIsMatchedAndAddToList(false, s.fileName(), parentPath, searchTask);
                    });
                } else {
                    int recordsNum = 1;
                    if (databaseResultsCount.containsKey(key)) {
                        recordsNum = databaseResultsCount.get(key).recordCount().get();
                    }
                    if (recordsNum != 0) {
                        String formattedSql = String.format(sqlAndTableName.toString(), "folder.PATH AS PARENT_PATH, NAME, FILE_SIZE, MODIFY_DATE");
                        unionSqlList.add(formattedSql);
                    }
                }
                final long weight = Math.min(matchedNum, 5);
                if (weight != 0L) {
                    //更新表的权重，每次搜索将会按照各个表的权重排序
                    updateTableWeight(tableName, weight);
                }
            }
            if (!unionSqlList.isEmpty()) {
                // 将unionSqlList均等分为diskConnectionNumber份
                var partitionedSqlLists = partitionSqlList(unionSqlList, Constants.SQLITE_CONNECTION_OPEN_NUMBER);
                var futures = new ArrayList<CompletableFuture<Map<String, Integer>>>();

                // 为每个分区启动一个线程
                var connectionId = searchTask.connectionId;
                for (int i = 0; i < partitionedSqlLists.size(); i++) {
                    var sqlPartition = partitionedSqlLists.get(i);
                    final int threadId = i;

                    if (!sqlPartition.isEmpty()) {
                        var future = CompletableFuture.supplyAsync(() -> {
                            String join = String.join(" UNION ALL ", sqlPartition);
                            return fallbackToSearchDatabase(searchTask, diskStr, join, preparedSearchInfoId, connectionId, threadId);
                        }, ThreadPoolUtil.getInstance().getCachedThreadPool());
                        futures.add(future);
                    }
                }

                // 等待所有线程完成并合并结果
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                        .thenAccept(v -> {
                            var combinedResults = new HashMap<String, Integer>();
                            for (var future : futures) {
                                try {
                                    var result = future.get();
                                    result.forEach((key, value) -> combinedResults.merge(key, value, Integer::sum));
                                } catch (Exception e) {
                                    log.error("获取搜索结果时出错: {}", e.getMessage(), e);
                                }
                            }
                            for (var eachTable : combinedResults.entrySet()) {
                                updateTableWeight(eachTable.getKey(), Math.min(eachTable.getValue(), 5));
                            }
                        }).join();
            }
        });
    }

    private Map<String, Integer> fallbackToSearchDatabase(SearchTask searchTask,
                                                          String diskStr,
                                                          String querySql,
                                                          long preparedSearchInfoId,
                                                          long connectionId,
                                                          int threadId) {
        if (searchTask.shouldStopSearch()) {
            return Collections.emptyMap();
        }
        var resultMap = new HashMap<String, Integer>();
        // C++实现
        PathMatcher.INSTANCE.match(querySql,
                SQLiteUtil.getDbAbsolutePath(diskStr),
                preparedSearchInfoId,
                searchTask.searchInfo.maxResultNum,
                connectionId,
                threadId,
                searchResult -> {
                    if (searchTask.shouldStopSearch()) {
                        return;
                    }
                    //字符串匹配通过
                    var fileName = FileUtil.getFileName(searchResult.getPath());
                    var fileNameSum = StringUtf8SumUtil.getStringSum(fileName);
                    int asciiGroup = fileNameSum / 100;
                    asciiGroup = Math.min(asciiGroup, Constants.MAX_TABLE_NUM);
                    String tableName = "list" + asciiGroup;
                    resultMap.merge(tableName, 1, Integer::sum);

                    if (searchTask.tempResultsSet.add(searchResult.getPath())) {
                        searchTask.resultCounter.getAndIncrement();
                        addSearchResultToContainer(searchTask.tempResults, searchResult);
                    }
                }
        );
        return resultMap;
    }

    /**
     * 将SQL列表均等分割为指定数量的分区
     *
     * @param sqlList        待分割的SQL列表
     * @param partitionCount 分区数量
     * @return 分区后的SQL列表
     */
    private List<List<String>> partitionSqlList(List<String> sqlList, int partitionCount) {
        if (sqlList.isEmpty() || partitionCount <= 1) {
            return List.of(sqlList);
        }

        var partitions = new ArrayList<List<String>>();
        int totalSize = sqlList.size();
        int baseSize = totalSize / partitionCount;
        int remainder = totalSize % partitionCount;

        int currentIndex = 0;
        for (int i = 0; i < partitionCount; i++) {
            int partitionSize = baseSize + (i < remainder ? 1 : 0);
            if (currentIndex >= totalSize) {
                break;
            }

            int endIndex = Math.min(currentIndex + partitionSize, totalSize);
            var partition = new ArrayList<>(sqlList.subList(currentIndex, endIndex));
            if (!partition.isEmpty()) {
                partitions.add(partition);
            }
            currentIndex = endIndex;
        }

        return partitions;
    }

    /**
     * 将匹配的结果添加到tempResult容器中
     *
     * @param searchResultContainer 搜索结果存放容器
     * @param searchResult          待添加的搜索结果
     */
    private void addSearchResultToContainer(Map<String, ConcurrentLinkedQueue<SearchResult>> searchResultContainer,
                                            SearchResult searchResult) {
        String suffixByPath = getSuffixByPath(searchResult.getPath());
        ConcurrentLinkedQueue<SearchResult> container = searchResultContainer.get(suffixByPath);
        if (container != null) {
            container.add(searchResult);
        } else {
            if (FileUtil.isDir(searchResult.getPath())) {
                container = searchResultContainer.get("dirPriority");
            } else {
                container = searchResultContainer.get("defaultPriority");
            }
            if (container != null) {
                container.add(searchResult);
            }
        }
        ThreadPoolUtil.INSTANCE.executeTask(() -> addUpdateFileModifyDateAndFileSizeSql(searchResult.getPath()));
    }

    private void addUpdateFileModifyDateAndFileSizeSql(String path) {
        try {
            if (path == null || path.isBlank() || FileUtil.isFileNotExist(path)) {
                return;
            }

            long modifyDate = getFileLastModifiedTime(path);
            long fileSize = Files.size(Path.of(path));

            String fileName = FileUtil.getFileName(path);
            int stringSum = StringUtf8SumUtil.getStringSum(fileName);
            int listGroup = stringSum / 100;
            listGroup = Math.min(listGroup, Constants.MAX_TABLE_NUM);

            String parentPath = FileUtil.getParentPath(path) + File.separator;

            String fileNameWithDoubleQuotes = escapeSingleQuotes(fileName);
            String parentPathWithDoubleQuotes = escapeSingleQuotes(parentPath);

            String formattedSql = String.format("""
                    update
                    \tlist%d
                    set
                    \tFILE_SIZE = %d,
                    \tMODIFY_DATE = %d
                    where
                    \tNAME = '%s'
                    \tand (
                    \tselect
                    \t\t`PATH`
                    \tfrom
                    \t\tfolder
                    \twhere
                    \t\tID = list%d.FOLDER_ID) = '%s'
                    """, listGroup, fileSize, modifyDate, fileNameWithDoubleQuotes, listGroup, parentPathWithDoubleQuotes);
            if (IsDebug.isDebug) {
                log.info("更新文件大小和修改日期, sql: {}", formattedSql);
            }
            addToCommandQueue(new SQLWithTaskId(formattedSql, SqlTaskIds.UPDATE_FILE_INFO, String.valueOf(path.charAt(0))));
        } catch (Exception e) {
            log.error("error {}", e.getMessage(), e);
        }
    }

    /**
     * 生成未格式化的sql
     * 每一个priority加上list0-list40会生成41条SQL作为key，value是搜索的表，即SELECT* FROM [list?]中的[list?];
     *
     * @return set
     */
    private ArrayList<List<SearchTableAndPriority>> getNonFormattedSqlFromTableQueue(SearchInfo searchInfo) {
        ArrayList<List<SearchTableAndPriority>> sqlColumnList = new ArrayList<>();
        if (priorityMap.isEmpty()) {
            return sqlColumnList;
        }
        ConcurrentLinkedQueue<String> tableQueue = initTableQueueByPriority();

        // 找到最长的非?开头且isKeywordPath为false的keyword
        String longestValidKeyword = findLongestValidKeyword(searchInfo);
        String otherCondition = null;
        if (longestValidKeyword != null && !longestValidKeyword.isEmpty()) {
            // 生成长度限制条件：NAME中的英文字母总长度大于等于最长的keywords的长度
            var keywordsSum = StringUtf8SumUtil.getStringSum(longestValidKeyword);
            otherCondition = "ASCII>=" + keywordsSum;
        }

        // 有d代表只需要搜索文件夹，文件夹的priority为-1
        if ((searchInfo.searchCase != null && Arrays.asList(searchInfo.searchCase).contains(PathMatchUtil.SearchCase.DIRECTORY))) {
            ArrayList<SearchTableAndPriority> searchTableAndPriorities = new ArrayList<>();
            for (String each : tableQueue) {
                searchTableAndPriorities.add(new SearchTableAndPriority(each, -1, otherCondition));
            }
            sqlColumnList.add(searchTableAndPriorities);
        } else {
            // 检查keywords中是否包含后缀
            Integer priorityToSearch = null;
            if (searchInfo.keywords() != null && searchInfo.keywords().length > 1) {
                for (String keyword : searchInfo.keywords()) {
                    if (keyword == null) continue;
                    // 移除可能的点号前缀
                    String suffix = keyword.startsWith(".") ? keyword.substring(1) : keyword;
                    // 检查是否在priorityMap中存在这个后缀
                    List<SuffixPriorityPair> filtered = priorityMap.stream().filter(pair -> pair.suffix().equalsIgnoreCase(suffix)).toList();
                    if (!filtered.isEmpty()) {
                        priorityToSearch = filtered.getFirst().priority();
                        break;
                    }
                }
            }

            // 如果找到了后缀关键词，只搜索对应的后缀
            if (priorityToSearch != null) {
                ArrayList<SearchTableAndPriority> searchTableAndPriorities = new ArrayList<>();
                for (String each : tableQueue) {
                    searchTableAndPriorities.add(new SearchTableAndPriority(each, priorityToSearch, otherCondition));
                }
                sqlColumnList.add(searchTableAndPriorities);
            } else {
                // 如果没找到后缀关键词，搜索所有后缀
                for (String each : tableQueue) {
                    ArrayList<SearchTableAndPriority> searchTableAndPriorities = new ArrayList<>();
                    for (SuffixPriorityPair i : priorityMap) {
                        searchTableAndPriorities.add(new SearchTableAndPriority(each, i.priority(), otherCondition));
                    }
                    sqlColumnList.add(searchTableAndPriorities);
                }
            }
        }
        return sqlColumnList;
    }

    /**
     * 找到最长的非?开头且isKeywordPath为false的keyword
     *
     * @param searchInfo 搜索信息
     * @return 最长的有效keyword，如果没有找到返回null
     */
    private String findLongestValidKeyword(SearchInfo searchInfo) {
        if (searchInfo.keywords() == null || searchInfo.isKeywordPath() == null) {
            return null;
        }

        String longestKeyword = null;
        int maxLength = 0;

        for (int i = 0; i < searchInfo.keywords().length; i++) {
            String keyword = searchInfo.keywords()[i];
            if (keyword != null &&
                !keyword.startsWith("?") &&
                i < searchInfo.isKeywordPath().length &&
                !searchInfo.isKeywordPath()[i] &&
                keyword.length() > maxLength) {
                longestKeyword = keyword;
                maxLength = keyword.length();
            }
        }

        return longestKeyword;
    }

    private void startSearchInThreadPool(SearchTask searchTask) {
        ThreadPoolUtil.getInstance().executeTask(() -> startSearch(searchTask), false);
    }

    /**
     * 添加sql语句，并开始搜索
     */
    private void startSearch(SearchTask searchTask) {
        var eventManagement = EventManagement.getInstance();
        var threadPoolUtil = ThreadPoolUtil.getInstance();
        Consumer<ConcurrentLinkedQueue<Runnable>> taskHandler = (taskQueue) -> {
            while (!taskQueue.isEmpty() && eventManagement.notMainExit()) {
                var runnable = taskQueue.poll();
                if (runnable == null) {
                    continue;
                }
                try {
                    runnable.run();
                } catch (Exception e) {
                    log.error("error: {}", e.getMessage(), e);
                }
            }
        };

        int searchThreadNumber = searchTask.taskMap.size();
        var countDownLatch = new CountDownLatch(searchThreadNumber);

        for (var eachQueue : searchTask.taskMap.values()) {
            threadPoolUtil.executeTask(() -> {
                // 每个磁盘一个线程，避免出现SQLite多线程占用一个连接
                try {
                    searchThreadCount.getAndIncrement();
                    taskHandler.accept(eachQueue);
                } finally {
                    countDownLatch.countDown();
                    searchThreadCount.getAndDecrement();
                }
            }, false);
        }

        waitForTasks(searchTask, countDownLatch);
    }

    private static String escapeSingleQuotes(String path) {
        StringBuilder escaped = new StringBuilder();
        for (char c : path.toCharArray()) {
            if (c == '\'') {
                escaped.append("''"); // 将 ' 替换为 ''
            } else {
                escaped.append(c);
            }
        }
        return escaped.toString();
    }

    /**
     * 生成删除记录sql
     *
     * @param asciiSum ascii
     * @param path     文件路径
     */
    private void addDeleteSqlCommandByAscii(int asciiSum, String path) {
        String command;
        int asciiGroup = asciiSum / 100;
        asciiGroup = Math.min(asciiGroup, Constants.MAX_TABLE_NUM);
        var parentPathWithDoubleQuotes = escapeSingleQuotes(FileUtil.getParentPath(path) + "\\");
        var fileNameWithDoubleQuotes = escapeSingleQuotes(FileUtil.getFileName(path));
        command = String.format("""
                delete from %s where NAME = '%s' and FOLDER_ID = (
                    select ID from folder where PATH = '%s'
                )
                """, "list" + asciiGroup, fileNameWithDoubleQuotes, parentPathWithDoubleQuotes);
        String disk = String.valueOf(path.charAt(0));
        if (isCommandNotRepeat(command)) {
            SQLWithTaskId sqlWithTaskId = new SQLWithTaskId(command, SqlTaskIds.DELETE_FROM_LIST, disk);
            sqlWithTaskId.key = disk + "," + "list" + asciiGroup + "," + getPriorityByPath(path);
            addToCommandQueue(sqlWithTaskId);
        }
        if (FileUtil.isDir(path)) {
            var pathWithDoubleQuotes = escapeSingleQuotes(path);
            String deleteFolderCommand = String.format("delete from folder where PATH = '%s'", pathWithDoubleQuotes);
            if (isCommandNotRepeat(deleteFolderCommand)) {
                SQLWithTaskId sqlWithTaskId = new SQLWithTaskId(deleteFolderCommand, SqlTaskIds.DELETE_FROM_LIST, disk);
                addToCommandQueue(sqlWithTaskId);
            }
        }
    }

    /**
     * 生成添加记录sql
     *
     * @param asciiSum ascii
     * @param path     文件路径
     * @param priority 优先级
     */
    private void addInsertSqlCommandByAscii(int asciiSum, String path, int priority) {
        int asciiGroup = asciiSum / 100;
//        long modifyDate = getFileLastModifiedTime(path);
        long modifyDate = 0L;
//        long fileSize = Files.size(pathVar);
        long fileSize = 0L;
        asciiGroup = Math.min(asciiGroup, Constants.MAX_TABLE_NUM);
        // 获取FOLDER_ID并插入
        String disk = String.valueOf(path.charAt(0));
        // 和executeAllCommands同步
        synchronized (this) {
            Connection conn = null;
            try {
                conn = SQLiteUtil.getDbConnection(disk);
                conn.setAutoCommit(false);
                try (PreparedStatement folderStmt = conn.prepareStatement(
                        "insert into folder(PATH) values(?) on CONFLICT(PATH) do update set PATH=excluded.PATH RETURNING ID;"
                )) {
                    folderStmt.setString(1, FileUtil.getParentPath(path) + "\\");
                    boolean execute = folderStmt.execute();
                    if (execute) {
                        try (ResultSet resultSet = folderStmt.getResultSet()) {
                            if (resultSet.next()) {
                                long folderId = resultSet.getLong("ID");
                                String tableName = "list" + asciiGroup + "(\"ASCII\", \"NAME\", \"FILE_SIZE\", \"MODIFY_DATE\", \"PRIORITY\", \"FOLDER_ID\")";
                                String command = String.format("INSERT OR REPLACE INTO %s VALUES(%d, '%s', %d, %d, %d, %d)",
                                        tableName, asciiSum, escapeSingleQuotes(FileUtil.getFileName(path)), fileSize, modifyDate, priority, folderId);
                                if (isCommandNotRepeat(command)) {
                                    SQLWithTaskId sqlWithTaskId = new SQLWithTaskId(command, SqlTaskIds.INSERT_TO_LIST, disk);
                                    sqlWithTaskId.key = disk + "," + "list" + asciiGroup + "," + getPriorityByPath(path);
                                    addToCommandQueue(sqlWithTaskId);
                                }
                            }
                        }
                    }
                } catch (SQLException e) {
                    log.error("error {}", e.getMessage(), e);
                }
            } catch (SQLException e) {
                log.error("error {}", e.getMessage(), e);
            } finally {
                if (conn != null) {
                    try {
                        conn.commit();
                    } catch (SQLException e) {
                        log.error(e.getMessage(), e);
                    }
                    try {
                        conn.close();
                    } catch (SQLException e) {
                        log.error(e.getMessage(), e);
                    }
                }
            }
        }
    }

    /**
     * 检查要删除的文件是否还未添加
     * 防止文件刚添加就被删除
     *
     * @param path 待删除文件路径
     * @return true如果待删除文件已经在任务队列中
     */
    private boolean isRemoveFileInCommandQueue(String path, SQLWithTaskId[] sqlWithTaskId) {
        for (SQLWithTaskId each : sqlCommandQueue) {
            if (each.taskId == SqlTaskIds.INSERT_TO_LIST && each.sql.contains(path)) {
                sqlWithTaskId[0] = each;
                return true;
            }
        }
        return false;
    }

    /**
     * 从数据库中删除记录
     *
     * @param path 文件路径
     */
    private void removeFileFromDatabase(String path) {
        if (path == null || path.isEmpty()) {
            return;
        }
        LuceneIndexService.removeFromIndex(path);
        int asciiSum = StringUtf8SumUtil.getStringSum(FileUtil.getFileName(path));
        SQLWithTaskId[] sqlWithTaskId = new SQLWithTaskId[1];
        if (isRemoveFileInCommandQueue(path, sqlWithTaskId)) {
            sqlCommandQueue.remove(sqlWithTaskId[0]);
        } else {
            addDeleteSqlCommandByAscii(asciiSum, path);
            int priorityBySuffix = getPriorityByPath(path);
            int asciiGroup = asciiSum / 100;
            asciiGroup = Math.min(asciiGroup, Constants.MAX_TABLE_NUM);
            String tableName = "list" + asciiGroup;
            String key = path.charAt(0) + "," + tableName + "," + priorityBySuffix;
            if (isEnableGPUAccelerate) {
                EventManagement.getInstance().putEvent(new GPURemoveRecordEvent(key, path));
            }
            Cache cache = tableCache.get(key);
            if (cache != null && cache.isCached.get()) {
                cache.removePathFromCache(path);
            }
        }
    }

    public HashMap<String, Integer> getPriorityMap() {
        HashMap<String, Integer> map = new HashMap<>();
        getPriority().forEach(p -> map.put(p.suffix, p.priority));
        return map;
    }

    /**
     * 初始化优先级表
     */
    private ConcurrentLinkedQueue<SuffixPriorityPair> getPriority() {
        ConcurrentLinkedQueue<SuffixPriorityPair> priorityQueue = new ConcurrentLinkedQueue<>();
        try (var connection = SQLiteUtil.getDbConnection("cache");
             Statement stmt = connection.createStatement();
             ResultSet resultSet = stmt.executeQuery("SELECT * FROM priority order by PRIORITY desc;")) {
            while (resultSet.next()) {
                String suffix = resultSet.getString("SUFFIX");
                String priority = resultSet.getString("PRIORITY");
                try {
                    priorityQueue.add(new SuffixPriorityPair(suffix, Integer.parseInt(priority)));
                } catch (Exception e) {
                    log.error("error: {}", e.getMessage(), e);
                    priorityQueue.add(new SuffixPriorityPair(suffix, 0));
                }
            }
            if (priorityQueue.stream().noneMatch(suffixPriorityPair -> "defaultPriority".equals(suffixPriorityPair.suffix))) {
                priorityQueue.add(new SuffixPriorityPair("defaultPriority", 0));
            }
            if (priorityQueue.stream().noneMatch(suffixPriorityPair -> "dirPriority".equals(suffixPriorityPair.suffix))) {
                priorityQueue.add(new SuffixPriorityPair("dirPriority", -1));
            }
        } catch (SQLException e) {
            log.error("error: {}", e.getMessage(), e);
        }
        return priorityQueue;
    }

    /**
     * 根据文件后缀获取优先级信息
     *
     * @param path 文件
     * @return 优先级
     */
    private int getPriorityByPath(String path) {
        if (FileUtil.isDir(path)) {
            return priorityMap.stream()
                    .filter(each -> each.suffix.equals("dirPriority"))
                    .findFirst()
                    .map(SuffixPriorityPair::priority)
                    .orElse(0);
        }
        String suffix = path.substring(path.lastIndexOf('.') + 1).toLowerCase();
        List<SuffixPriorityPair> result = priorityMap.stream().filter(each -> each.suffix.equals(suffix)).toList();
        if (result.isEmpty()) {
            return priorityMap.stream()
                    .filter(each -> each.suffix.equals("defaultPriority"))
                    .findFirst()
                    .map(SuffixPriorityPair::priority)
                    .orElse(0);
        } else {
            return result.getFirst().priority();
        }
    }

    /**
     * 获取文件后缀
     *
     * @param path 文件路径
     * @return 后缀名
     */
    private String getSuffixByPath(String path) {
        return path.substring(path.lastIndexOf('.') + 1).toLowerCase();
    }

    private void addFileToDatabase(String path) {
        if (path == null || path.isEmpty() || FileUtil.isFileNotExist(path)) {
            return;
        }
        if (status.get() != Constants.Enums.DatabaseStatus.NORMAL) {
            return;
        }
        LuceneIndexService.addToIndex(path);
        int asciiSum = StringUtf8SumUtil.getStringSum(FileUtil.getFileName(path));
        int priorityBySuffix = getPriorityByPath(path);
        try {
            addInsertSqlCommandByAscii(asciiSum, path, priorityBySuffix);
            int asciiGroup = asciiSum / 100;
            asciiGroup = Math.min(asciiGroup, Constants.MAX_TABLE_NUM);
            String tableName = "list" + asciiGroup;
            String key = path.charAt(0) + "," + tableName + "," + priorityBySuffix;
            if (isEnableGPUAccelerate) {
                EventManagement.getInstance().putEvent(new GPUAddRecordEvent(key, path));
            }
            Cache cache = tableCache.get(key);
            if (cache != null && cache.isCacheValid()) {
                if (HeapMemUtil.getInstance().getHeapUsage() < Constants.CACHE_INSERTABLE_MAX_OCCUPATION_PERCENTAGE) {
                    cache.savePathCache(path);
                } else {
                    cache.isFileLost.set(true);
                    cache.clearCache();
                }
            }
        } catch (Exception e) {
            log.error("error: {}", e.getMessage(), e);
        }
    }

    private void addFileToCache(String path) {
        String command = "INSERT OR IGNORE INTO cache(PATH) VALUES('" + path + "');";
        String statisticsCommand = "INSERT INTO statistics(PATH) VALUES('" + path + "') ON CONFLICT DO UPDATE SET COUNT = COUNT + 1;";
        if (isCommandNotRepeat(command)) {
            addToCommandQueue(new SQLWithTaskId(command, SqlTaskIds.INSERT_TO_CACHE, "cache"));
            addToCommandQueue(new SQLWithTaskId(statisticsCommand, SqlTaskIds.INSERT_TO_STATISTICS, "cache"));
            if (IsDebug.isDebug) {
                log.info("添加" + path + "到缓存");
            }
        }
    }

    private void removeFileFromCache(String path) {
        String command = "DELETE from cache where PATH=" + "'" + path + "';";
        String statisticsCommand = "DELETE from statistics where PATH=" + "'" + path + "';";
        if (isCommandNotRepeat(command)) {
            addToCommandQueue(new SQLWithTaskId(command, SqlTaskIds.DELETE_FROM_CACHE, "cache"));
            addToCommandQueue(new SQLWithTaskId(statisticsCommand, SqlTaskIds.DELETE_FROM_STATISTICS, "cache"));
            if (IsDebug.isDebug) {
                log.info("删除" + path + "到缓存");
            }
        }
    }

    /**
     * 执行sql
     */
    @SuppressWarnings("SqlNoDataSourceInspection")
    private void executeAllCommands() {
        synchronized (this) {
            if (!sqlCommandQueue.isEmpty()) {
                LinkedHashSet<SQLWithTaskId> tempCommandSet = new LinkedHashSet<>(sqlCommandQueue);
                HashMap<String, Boolean> dbIntegrityMap = new HashMap<>();
                Map<String, List<SQLWithTaskId>> collectSqlMap = tempCommandSet.stream()
                        .collect(Collectors.groupingBy(SQLWithTaskId::getDiskStr));
                collectSqlMap.forEach((diskKey, sqlList) -> {
                    Connection diskConnection = null;
                    try {
                        diskConnection = SQLiteUtil.getDbConnection(diskKey);
                        diskConnection.setAutoCommit(false);
                        try (Statement stmt = diskConnection.createStatement()) {
                            for (var sqlWithTaskId : sqlList) {
                                if (IsDebug.isDebug) {
                                    log.info("----------------------------------------------");
                                    log.info("执行SQL命令--" + sqlWithTaskId.sql);
                                    log.info("----------------------------------------------");
                                }
                                try {
                                    if (!stmt.execute(sqlWithTaskId.sql)) {
                                        int updateCount = stmt.getUpdateCount();
                                        if (sqlWithTaskId.key != null && updateCount != -1 && updateCount != 0) {
                                            if (databaseResultsCount.containsKey(sqlWithTaskId.key)) {
                                                var recordsNumber = databaseResultsCount.get(sqlWithTaskId.key);
                                                updateCount = sqlWithTaskId.taskId == SqlTaskIds.INSERT_TO_LIST ?
                                                        updateCount : -updateCount;
                                                recordsNumber.recordCount().addAndGet(updateCount);
                                            }
                                        }
                                    }
                                } catch (Exception e) {
                                    log.error("error {}", e.getMessage(), e);
                                    log.error("error sql: {}", sqlWithTaskId.sql);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("error {}", e.getMessage(), e);
                        dbIntegrityMap.put(diskKey, false);
                    } finally {
                        if (diskConnection != null) {
                            try {
                                diskConnection.commit();
                            } catch (SQLException e) {
                                log.error("error {}", e.getMessage(), e);
                            }
                            try {
                                diskConnection.close();
                            } catch (SQLException e) {
                                log.error("error {}", e.getMessage(), e);
                            }
                        }
                    }
                });

                sqlCommandQueue.removeAll(tempCommandSet);
                if (!dbIntegrityMap.isEmpty()) {
                    Gson gson = GsonUtil.INSTANCE.getGson();
                    if (FileUtil.isFileExist(Constants.DATABASE_INTEGRITY_CHECK_FILE)) {
                        try (var reader = new BufferedReader(new InputStreamReader(new FileInputStream(Constants.DATABASE_INTEGRITY_CHECK_FILE), StandardCharsets.UTF_8))) {
                            Map<String, Boolean> map = gson.fromJson(reader, Map.class);
                            map.forEach(dbIntegrityMap::putIfAbsent);
                        } catch (IOException ex) {
                            throw new RuntimeException(ex);
                        }
                    }
                    try (var writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(Constants.DATABASE_INTEGRITY_CHECK_FILE), StandardCharsets.UTF_8))) {
                        writer.write(gson.toJson(dbIntegrityMap));
                    } catch (IOException ex) {
                        throw new RuntimeException(ex);
                    }
                }
            }
        }
    }

    /**
     * 添加任务到任务列表
     *
     * @param sql 任务
     */
    private void addToCommandQueue(SQLWithTaskId sql) {
        if (sqlCommandQueue.size() < MAX_SQL_NUM) {
            if (getStatus() == Constants.Enums.DatabaseStatus.MANUAL_UPDATE) {
                return;
            }
            sqlCommandQueue.add(sql);
        } else {
            if (IsDebug.isDebug) {
                log.warn("添加sql语句" + sql + "失败，已达到最大上限");
            }
        }
    }

    /**
     * 检查任务是否重复
     *
     * @param sql 任务
     * @return boolean
     */
    private boolean isCommandNotRepeat(String sql) {
        for (SQLWithTaskId each : sqlCommandQueue) {
            if (each.sql.equals(sql)) {
                return false;
            }
        }
        return true;
    }

    public Constants.Enums.DatabaseStatus getRawStatus() {
        return status.get();
    }

    /**
     * 获取数据库状态
     *
     * @return 数据库状态
     */
    public Constants.Enums.DatabaseStatus getStatus() {
        var currentStatus = status.get();
        return switch (currentStatus) {
            case NORMAL, _TEMP -> Constants.Enums.DatabaseStatus.NORMAL;
            default -> currentStatus;
        };
    }

    private boolean casSetStatus(Constants.Enums.DatabaseStatus expect, Constants.Enums.DatabaseStatus newVal) {
        final long start = System.currentTimeMillis();
        final long timeout = 1000;
        try {
            while (!status.compareAndSet(expect, newVal) && System.currentTimeMillis() - start < timeout) {
                TimeUnit.MILLISECONDS.sleep(1);
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return status.get() == newVal;
    }

    /**
     * 创建索引
     */
    private void createAllIndex() {
        sqlCommandQueue.add(new SQLWithTaskId("CREATE INDEX IF NOT EXISTS cache_index ON cache(PATH);", SqlTaskIds.CREATE_INDEX, "cache"));
        for (String each : RegexUtil.comma.split(AllConfigs.getInstance().getAvailableDisks())) {
            String diskStr = String.valueOf(each.charAt(0));
            for (int i = 0; i <= Constants.MAX_TABLE_NUM; ++i) {
                String createIndex = "CREATE INDEX IF NOT EXISTS list" + i + "_index ON list" + i + "(PRIORITY, ASCII);";
                sqlCommandQueue.add(new SQLWithTaskId(createIndex, SqlTaskIds.CREATE_INDEX, diskStr));
                String createIndexPath = "CREATE INDEX IF NOT EXISTS list" + i + "_index_path ON list" + i + "(FOLDER_ID);";
                sqlCommandQueue.add(new SQLWithTaskId(createIndexPath, SqlTaskIds.CREATE_INDEX, diskStr));
            }
            String createFolderIndex = "CREATE INDEX IF NOT EXISTS folder_index ON folder(ID);";
            sqlCommandQueue.add(new SQLWithTaskId(createFolderIndex, SqlTaskIds.CREATE_INDEX, diskStr));
        }
    }

    /**
     * 调用C程序搜索并等待执行完毕
     *
     * @param paths      磁盘信息
     * @param ignorePath 忽略文件夹
     * @throws IOException exception
     */
    private Process searchByUSN(String paths, String ignorePath) throws IOException {
        File usnSearcher = new File("fileSearcherUSN.exe");
        String absPath = usnSearcher.getAbsolutePath();
        String start = absPath.substring(0, 2);
        String end = "\"" + absPath.substring(2) + "\"";
        File database = new File("data");
        try (BufferedWriter buffW = new BufferedWriter(new OutputStreamWriter(new FileOutputStream("MFTSearchInfo.dat"), StandardCharsets.UTF_8))) {
            buffW.write(paths);
            buffW.newLine();
            buffW.write(database.getAbsolutePath());
            buffW.newLine();
            buffW.write(ignorePath);
            buffW.flush();
        }
        return Runtime.getRuntime().exec(new String[]{"cmd.exe", "/c", start + end});
    }

    private void resetStartTimeCount() {
        File startTimeCount = new File("user/startTimeCount.dat");
        try (var writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(startTimeCount), StandardCharsets.UTF_8))) {
            writer.write(String.valueOf(1));
        } catch (IOException e) {
            log.error("error: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查索引数据库大小
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    private void checkAndDeleteDb(boolean isDropPrevious) {
        HashMap<String, String> databaseCreateTimeMap = new HashMap<>();
        String[] disks = RegexUtil.comma.split(AllConfigs.getInstance().getAvailableDisks());
        LocalDate now = LocalDate.now();
        for (String disk : disks) {
            databaseCreateTimeMap.put(disk, now.toString());
        }

        //从文件中读取每个数据库的创建时间
        Gson gson = GsonUtil.INSTANCE.getGson();
        if (FileUtil.isFileExist(Constants.DATABASE_CREATE_TIME_FILE)) {
            try (var reader = new BufferedReader(new InputStreamReader(new FileInputStream(Constants.DATABASE_CREATE_TIME_FILE), StandardCharsets.UTF_8))) {
                Map map = gson.fromJson(reader, Map.class);
                if (map != null) {
                    //从文件中读取每个数据库的创建时间
                    map.forEach((key, value) -> databaseCreateTimeMap.put((String) key, (String) value));
                }
            } catch (Exception e) {
                log.error("error: {}", e.getMessage(), e);
            }
        }

        String dbVersionKey = "dbVersion";
        String currentDbVersion = String.valueOf(Constants.DATABASE_STRUCTURE_VERSION);
        String dbVersion = databaseCreateTimeMap.get(dbVersionKey);
        if (!Objects.equals(dbVersion, currentDbVersion)) {
            isDropPrevious = true;
        }
        databaseCreateTimeMap.put(dbVersionKey, currentDbVersion);

        final long maxDatabaseSize = 8L * 1024 * 1024 * 100;
        for (String eachDisk : disks) {
            String name = eachDisk.charAt(0) + ".db";
            try {
                Path diskDatabaseFile = Path.of("data/" + name);
                long length = Files.size(diskDatabaseFile);
                if (length > maxDatabaseSize ||
                    Period.between(LocalDate.parse(databaseCreateTimeMap.get(eachDisk)), now).getDays() > 5 ||
                    isDropPrevious) {
                    if (IsDebug.isDebug) {
                        log.info("当前文件" + name + "已删除");
                    }
                    //更新创建时间
                    databaseCreateTimeMap.put(eachDisk, now.toString());
                    Files.delete(diskDatabaseFile);
                }
            } catch (IOException e) {
                log.error("error: {}", e.getMessage(), e);
            }
        }
        String toJson = gson.toJson(databaseCreateTimeMap);
        try (var writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(Constants.DATABASE_CREATE_TIME_FILE), StandardCharsets.UTF_8))) {
            writer.write(toJson);
        } catch (IOException e) {
            log.error("error: {}", e.getMessage(), e);
        }
    }

    @SneakyThrows
    private void executeAllSQLAndWait(@SuppressWarnings("SameParameterValue") int timeoutMills) {// 等待剩余的sql全部执行完成
        final long time = System.currentTimeMillis();
        // 将在队列中的sql全部执行并等待搜索线程全部完成
        log.info("等待所有sql执行完成，并且退出搜索");
        while (searchThreadCount.get() != 0 || !sqlCommandQueue.isEmpty()) {
            executeAllCommands();
            TimeUnit.MILLISECONDS.sleep(10);
            if (System.currentTimeMillis() - time > timeoutMills) {
                log.info("等待超时");
                break;
            }
        }
    }

    private void stopAllSearch() {
        prepareTasksMap.values().forEach(SearchTask::stopSearch);
    }

    /**
     * 等待fileSearcherUSN进程并切换回数据库
     *
     * @param searchByUsn fileSearcherUSN进程
     */
    private void waitForSearchAndSwitchDatabase(Process searchByUsn) {
        // 搜索完成并写入数据库后，重新建立数据库连接
        try {
            ProcessUtil.waitForProcess("fileSearcherUSN.exe", 1000);
            readSearchUsnOutput(searchByUsn);
        } catch (Exception e) {
            log.error("error: {}", e.getMessage(), e);
        }
        stopAllSearch();
        if (!casSetStatus(this.status.get(), Constants.Enums.DatabaseStatus.MANUAL_UPDATE)) {
            log.warn("修改status为MANUAL_UPDATE失败");
        }
        log.info("等待搜索任务全部结束");
        try {
            final long startWaitingTime = System.currentTimeMillis();
            //等待所有搜索线程结束，最多等待1分钟
            while (searchThreadCount.get() != 0 && System.currentTimeMillis() - startWaitingTime < 60 * 1000) {
                TimeUnit.MILLISECONDS.sleep(20);
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        log.info("全部搜索任务等待完成");
        SQLiteUtil.closeAll();
        invalidateAllCache();
        log.info("切换到主数据库");
        SQLiteUtil.initAllConnections();
        createAllIndex();
        ThreadPoolUtil.getInstance().executeTask(this::executeAllCommands);
        waitForCommandSet(SqlTaskIds.CREATE_INDEX);
        // 搜索完成，更新isDatabaseUpdated标志
        isDatabaseUpdated.set(true);
        //重新初始化priority
        priorityMap = getPriority();
        this.status.set(Constants.Enums.DatabaseStatus.NORMAL);
        startMonitorDisks();
        ThreadPoolUtil.INSTANCE.executeTask(() -> {
            try {
                LuceneIndexService.writeLuceneScannedFile(null);
                TimeUnit.SECONDS.sleep(10);
            } catch (InterruptedException | IOException e) {
                throw new RuntimeException(e);
            }
            LuceneIndexService.createIndex();
        });
    }

    private static void readSearchUsnOutput(Process searchByUsn) {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(searchByUsn.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                log.info("fileSearcherUSN: " + line);
            }
        } catch (IOException e) {
            log.error("error: {}", e.getMessage(), e);
        }
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(searchByUsn.getErrorStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                log.info("fileSearcherUSN Error: " + line);
            }
        } catch (IOException e) {
            log.error("error: {}", e.getMessage(), e);
        }
    }

    private static synchronized void stopMonitorDisks(boolean isDeleteUsn) {
        String availableDisks = AllConfigs.getInstance().getAvailableDisks();
        String[] disks = RegexUtil.comma.split(availableDisks);
        for (String disk : disks) {
            if (isDeleteUsn) {
                FileMonitor.INSTANCE.delete_usn_on_exit(disk);
            }
            FileMonitor.INSTANCE.stop_monitor(disk);
        }
    }

    /**
     * 关闭数据库连接并更新数据库
     *
     * @param ignorePath     忽略文件夹
     * @param isDropPrevious 是否删除之前的记录
     */
    private boolean updateLists(String ignorePath, boolean isDropPrevious) throws IOException, InterruptedException {
        if (getStatus() == Constants.Enums.DatabaseStatus.MANUAL_UPDATE || ProcessUtil.isProcessExist("fileSearcherUSN.exe")) {
            throw new RuntimeException("already searching");
        }
        stopMonitorDisks(false);

        if (!casSetStatus(status.get(), Constants.Enums.DatabaseStatus.MANUAL_UPDATE)) {
            throw new RuntimeException("databaseService status设置MANUAL UPDATE状态失败");
        }
        // 停止搜索
        stopAllSearch();
        executeAllSQLAndWait(3000);

        String availableDisks = AllConfigs.getInstance().getAvailableDisks();
        if (!isDropPrevious) {
            //执行VACUUM命令
            for (String eachDisk : RegexUtil.comma.split(availableDisks)) {
                try (var connection = SQLiteUtil.getDbConnection(String.valueOf(eachDisk.charAt(0)));
                     Statement stmt = connection.createStatement()) {
                    stmt.execute("VACUUM;");
                } catch (Exception ex) {
                    log.error("error: {}", ex.getMessage(), ex);
                }
            }
        }

        SQLiteUtil.closeAll();
        // 复制数据库到tmp
        SQLiteUtil.copyDatabases("data", "tmp");
        SQLiteUtil.initAllConnections("tmp");
        if (IsDebug.isDebug) {
            log.info("成功切换到临时数据库");
        }
        if (!casSetStatus(status.get(), Constants.Enums.DatabaseStatus._TEMP)) {
            //恢复data目录的数据库
            SQLiteUtil.closeAll();
            SQLiteUtil.initAllConnections();
            casSetStatus(status.get(), Constants.Enums.DatabaseStatus.NORMAL);
            throw new RuntimeException("databaseService status设置TEMP状态失败");
        }
        // 检查数据库文件大小，过大则删除
        checkAndDeleteDb(isDropPrevious);
        resetStartTimeCount();

        // 初始化数据库表
        for (String eachDisk : RegexUtil.comma.split(availableDisks)) {
            try (var conn = SQLiteUtil.openNewDbConnection("data" + File.separator + eachDisk.charAt(0) + ".db");
                 var stmt = conn.createStatement()) {
                stmt.execute("BEGIN;");
                SQLiteUtil.initTables(stmt);
                SQLiteUtil.initFolderTable(stmt);
                stmt.execute("COMMIT;");
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        }

        Process searchByUSN = null;
        try {
            // 创建搜索进程并等待
            searchByUSN = searchByUSN(availableDisks, ignorePath.toLowerCase());
        } catch (Exception e) {
            log.error("error: {}", e.getMessage(), e);
            return false;
        } finally {
            try {
                waitForSearchAndSwitchDatabase(searchByUSN);
            } catch (Exception e) {
                log.error("error {}", e.getMessage(), e);
            }
        }
        return true;
    }

    /**
     * 等待sql任务执行
     *
     * @param taskId 任务id
     */
    @SneakyThrows
    private void waitForCommandSet(@SuppressWarnings("SameParameterValue") SqlTaskIds taskId) {
        EventManagement eventManagement = EventManagement.getInstance();
        long tmpStartTime = System.currentTimeMillis();
        while (eventManagement.notMainExit()) {
            //等待
            if (System.currentTimeMillis() - tmpStartTime > 60 * 1000) {
                log.warn("等待SQL语句任务" + taskId + "处理超时");
                break;
            }
            //判断commandSet中是否还有taskId存在
            if (!isTaskExistInCommandSet(taskId)) {
                break;
            }
            TimeUnit.MILLISECONDS.sleep(10);
        }
    }

    private boolean isTaskExistInCommandSet(SqlTaskIds taskId) {
        for (SQLWithTaskId tasks : sqlCommandQueue) {
            if (tasks.taskId == taskId) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取list0-40所有表的权重，使用越频繁权重越高
     *
     * @return map
     */
    private HashMap<String, Integer> queryAllWeights() {
        HashMap<String, Integer> stringIntegerHashMap = new HashMap<>();
        try (var connection = SQLiteUtil.getDbConnection("weight");
             Statement pStmt = connection.createStatement();
             ResultSet resultSet = pStmt.executeQuery("SELECT TABLE_NAME, TABLE_WEIGHT FROM weight;")) {
            while (resultSet.next()) {
                String tableName = resultSet.getString("TABLE_NAME");
                int weight = resultSet.getInt("TABLE_WEIGHT");
                stringIntegerHashMap.put(tableName, weight);
            }
        } catch (SQLException e) {
            log.error("error: {}", e.getMessage(), e);
        }
        return stringIntegerHashMap;
    }

    private void checkTimeAndSendExecuteSqlSignalThread() {
        ThreadPoolUtil.getInstance().executeTask(() -> {
            // 时间检测线程
            AllConfigs allConfigs = AllConfigs.getInstance();
            EventManagement eventManagement = EventManagement.getInstance();
            long checkTime = System.currentTimeMillis();
            while (eventManagement.notMainExit()) {
                final long updateTimeLimit = allConfigs.getConfigEntity().getUpdateTimeLimit() * 1000L;
                boolean isTooManySQLsToExecute =
                        getStatus() == Constants.Enums.DatabaseStatus.NORMAL && sqlCommandQueue.size() > 100;
                if (isTooManySQLsToExecute || System.currentTimeMillis() - checkTime >= updateTimeLimit) {
                    checkTime = System.currentTimeMillis();
                    executeAllCommands();
                }
                try {
                    TimeUnit.SECONDS.sleep(1);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        });
    }

    /**
     * 根据输入生成SearchInfo
     *
     * @param searchTextSupplier 全字匹配关键字
     * @param searchCaseSupplier 搜索过滤类型
     * @param keywordsSupplier   搜索关键字
     * @return SearchInfo
     */
    private static SearchInfo prepareSearchKeywords(Supplier<String> searchTextSupplier,
                                                    Supplier<String[]> searchCaseSupplier,
                                                    Supplier<String[]> keywordsSupplier,
                                                    int maxResultNumber) {
        var searchText = searchTextSupplier.get();
        var searchCase = searchCaseSupplier.get();
        var isIgnoreCase = searchCase == null ||
                           Arrays.stream(searchCase).noneMatch(s -> s.equals(PathMatchUtil.SearchCase.CASE));
        String[] _keywords = keywordsSupplier.get();
        var keywords = new ArrayList<String>();
        var keywordsLowerCase = new ArrayList<String>();
        var isKeywordPath = new ArrayList<Boolean>();
        // 对keywords进行处理
        for (String keyword : _keywords) {
            String eachKeyword = keyword;
            // 当keywords为空，初始化为默认值
            if (eachKeyword == null || eachKeyword.isEmpty()) {
                isKeywordPath.add(false);
                keywords.add("");
                keywordsLowerCase.add("");
                continue;
            }
            final boolean isPath = eachKeyword.startsWith("/") || eachKeyword.startsWith(File.separator);
            if (isPath) {
                // 当关键字为"test;/C:/test"时，分割出来为["test", "/C:/test"]，所以需要去掉 /C:/test 前面的 "/"
                eachKeyword = eachKeyword.substring(1);
                // 将 / 替换为 \ ，以便模糊匹配文件夹路径
                Matcher matcher = RegexUtil.getPattern("/", 0).matcher(eachKeyword);
                eachKeyword = matcher.replaceAll(Matcher.quoteReplacement(File.separator));
                isKeywordPath.add(true);
                keywords.add(eachKeyword);
                keywordsLowerCase.add(eachKeyword.toLowerCase());
            } else {
                // 处理非路径关键词，包括拼写纠错和分词
                processNonPathKeyword(eachKeyword, keywords, keywordsLowerCase, isKeywordPath);
            }
        }
        int size = isKeywordPath.size();
        boolean[] isKeywordsPathArr = new boolean[size];
        for (int i = 0; i < size; i++) {
            isKeywordsPathArr[i] = isKeywordPath.get(i);
        }
        return new SearchInfo(searchCase,
                isIgnoreCase,
                searchText,
                keywords.toArray(new String[0]),
                keywordsLowerCase.toArray(new String[0]),
                isKeywordsPathArr,
                maxResultNumber);
    }

    /**
     * 处理非路径关键词，包括拼写纠错和分词
     *
     * @param eachKeyword       原始关键词
     * @param keywords          关键词列表
     * @param keywordsLowerCase 小写关键词列表
     * @param isKeywordPath     是否为路径关键词的标记列表
     */
    private static void processNonPathKeyword(String eachKeyword,
                                              ArrayList<String> keywords,
                                              ArrayList<String> keywordsLowerCase,
                                              ArrayList<Boolean> isKeywordPath) {
        // 对关键词进行拼写纠错
        String correctedKeyword = SymSpellUtil.INSTANCE.lookup(eachKeyword);

        // 是否启用内容索引（分词）
        boolean enableContentIndex = AllConfigs.getInstance().getConfigEntity().getAdvancedConfigEntity().isEnableContentIndex();

        if (enableContentIndex) {
            // 使用 IK 分词器进行分词
            processKeywordWithSegmentation(eachKeyword, correctedKeyword, keywords, keywordsLowerCase, isKeywordPath);
        } else {
            // 不使用分词，直接处理整个关键词
            processSingleKeyword(eachKeyword, correctedKeyword, keywords, keywordsLowerCase, isKeywordPath);
        }
    }

    /**
     * 使用分词器处理关键词
     */
    private static void processKeywordWithSegmentation(String originalKeyword,
                                                       String correctedKeyword,
                                                       ArrayList<String> keywords,
                                                       ArrayList<String> keywordsLowerCase,
                                                       ArrayList<Boolean> isKeywordPath) {
        // 对原始关键词进行分词
        segmentAndAddKeywords(originalKeyword, keywords, keywordsLowerCase, isKeywordPath);

        // 如果纠错后的词与原词不同，也对纠错后的词进行分词
        if (!originalKeyword.equals(correctedKeyword)) {
            segmentAndAddKeywords(correctedKeyword, keywords, keywordsLowerCase, isKeywordPath);
        }
    }

    /**
     * 对关键词进行分词并添加到列表中
     */
    private static void segmentAndAddKeywords(String keyword,
                                              ArrayList<String> keywords,
                                              ArrayList<String> keywordsLowerCase,
                                              ArrayList<Boolean> isKeywordPath) {
        try {
            IKSegmenter ikSegmenter = new IKSegmenter(new StringReader(keyword), DefaultConfig.getInstance());
            Lexeme lexeme;
            while ((lexeme = ikSegmenter.next()) != null) {
                String lexemeText = lexeme.getLexemeText();

                // 对分词后的每个词也进行拼写纠错
                String correctedLexeme = SymSpellUtil.INSTANCE.lookup(lexemeText);

                // 如果纠错后与原词不同，添加两个 optional keyword
                if (!lexemeText.equals(correctedLexeme)) {
                    // 添加原词作为 optional keyword
                    isKeywordPath.add(false);
                    keywords.add("?" + lexemeText);
                    keywordsLowerCase.add("?" + lexemeText.toLowerCase());

                    // 添加纠错后的词作为 optional keyword
                    isKeywordPath.add(false);
                    keywords.add("?" + correctedLexeme);
                    keywordsLowerCase.add("?" + correctedLexeme.toLowerCase());
                } else {
                    // 纠错后与原词相同，直接添加
                    isKeywordPath.add(false);
                    keywords.add(lexemeText);
                    keywordsLowerCase.add(lexemeText.toLowerCase());
                }
            }
        } catch (IOException e) {
            // 分词失败，直接添加原关键词
            isKeywordPath.add(false);
            keywords.add(keyword);
            keywordsLowerCase.add(keyword.toLowerCase());
        }
    }

    /**
     * 处理单个关键词（不分词）
     */
    private static void processSingleKeyword(String originalKeyword,
                                             String correctedKeyword,
                                             ArrayList<String> keywords,
                                             ArrayList<String> keywordsLowerCase,
                                             ArrayList<Boolean> isKeywordPath) {
        // 如果纠错后与原词不同，添加两个 optional keyword
        if (!originalKeyword.equals(correctedKeyword)) {
            // 添加原词作为 optional keyword
            isKeywordPath.add(false);
            keywords.add("?" + originalKeyword);
            keywordsLowerCase.add("?" + originalKeyword.toLowerCase());

            // 添加纠错后的词作为 optional keyword
            isKeywordPath.add(false);
            keywords.add("?" + correctedKeyword);
            keywordsLowerCase.add("?" + correctedKeyword.toLowerCase());
        } else {
            // 纠错后与原词相同，直接添加
            isKeywordPath.add(false);
            keywords.add(originalKeyword);
            keywordsLowerCase.add(originalKeyword.toLowerCase());
        }
    }

    @EventRegister(registerClass = FlushFileChangesEvent.class)
    private static void flushFileChangesToDatabase(Event event) {
        getInstance().executeAllCommands();
    }

    @EventRegister(registerClass = CheckDatabaseEmptyEvent.class)
    private static void checkDatabaseEmpty(Event event) {
        boolean databaseDamaged = SQLiteUtil.isDatabaseDamaged();
        event.setReturnValue(databaseDamaged);
    }

    @EventRegister(registerClass = InitializeDatabaseEvent.class)
    private static void initAllDatabases(Event event) {
        SQLiteUtil.initAllConnections();
    }

    @EventRegister(registerClass = StartMonitorDiskEvent.class)
    private static void startMonitorDiskEvent(Event event) {
        startMonitorDisks();
    }

    @EventListener(listenClass = SetConfigsEvent.class)
    private static void setGpuDevice(Event event) {
        isEnableGPUAccelerate = AllConfigs.getInstance().getConfigEntity().isEnableGpuAccelerate();
        if (isEnableGPUAccelerate) {
            synchronized (DatabaseService.class) {
                var device = AllConfigs.getInstance().getConfigEntity().getGpuDevice();
                if (!GPUAccelerator.INSTANCE.setDevice(device)) {
                    log.warn("gpu设备" + device + "无效");
                }
            }
        }
    }

    @EventRegister(registerClass = PrepareSearchEvent.class)
    private static void prepareSearchEvent(Event event) {
        var prepareSearchEvent = (PrepareSearchEvent) event;
        if (prepareSearchEvent.searchText.get().length() > Constants.MAX_SEARCH_TEXT_LENGTH) {
            log.warn("关键字太长，取消搜索");
            throw new RuntimeException("关键字太长，取消搜索");
        }
        String[] searchCaseArray = prepareSearchEvent.searchCase.get();
        if (searchCaseArray != null && List.of(searchCaseArray).contains(PathMatchUtil.SearchCase.PATTERN)) {
            try {
                RegexUtil.getPattern(prepareSearchEvent.searchText.get(), 0);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new RuntimeException("正则表达式输入错误");
            }
        }
        var startWaiting = System.currentTimeMillis();
        final long timeout = 3000;
        var databaseService = getInstance();
        while (databaseService.getStatus() != Constants.Enums.DatabaseStatus.NORMAL) {
            if (System.currentTimeMillis() - startWaiting > timeout) {
                log.warn("prepareSearch，等待数据库状态超时");
                return;
            }
            Thread.onSpinWait();
        }
        var searchInfo = prepareSearchKeywords(prepareSearchEvent.searchText,
                prepareSearchEvent.searchCase,
                prepareSearchEvent.keywords,
                prepareSearchEvent.maxResultNum);
        var searchTask = prepareSearch(searchInfo);
        prepareTasksMap.put(searchInfo, searchTask);
        event.setReturnValue(searchTask);
    }

    @EventRegister(registerClass = StartSearchEvent.class)
    private static void startSearchEvent(Event event) {
        var startSearchEvent = (StartSearchEvent) event;
        if (startSearchEvent.searchText.get().length() > Constants.MAX_SEARCH_TEXT_LENGTH) {
            log.warn("关键字太长，取消搜索");
            throw new RuntimeException("关键字太长，取消搜索");
        }
        String[] searchCaseArray = startSearchEvent.searchCase.get();
        if (searchCaseArray != null && List.of(searchCaseArray).contains(PathMatchUtil.SearchCase.PATTERN)) {
            try {
                RegexUtil.getPattern(startSearchEvent.searchText.get(), 0);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                throw new RuntimeException("正则表达式输入错误");
            }
        }
        DatabaseService databaseService = getInstance();
        final long startWaiting = System.currentTimeMillis();
        final long timeout = 3000;
        while (databaseService.getStatus() != Constants.Enums.DatabaseStatus.NORMAL) {
            if (System.currentTimeMillis() - startWaiting > timeout) {
                log.info("等待数据库状态为NORMAL超时");
                return;
            }
            Thread.onSpinWait();
        }
        // 检查prepareTaskMap中是否有过期任务
        for (var eachTask : prepareTasksMap.entrySet()) {
            if (System.currentTimeMillis() - eachTask.getValue().taskCreateTimeMills > SearchTask.maxTaskValidThreshold) {
                prepareTasksMap.remove(eachTask.getKey());
            }
        }
        var searchInfo = prepareSearchKeywords(startSearchEvent.searchText,
                startSearchEvent.searchCase,
                startSearchEvent.keywords,
                startSearchEvent.maxResultNum);
        var searchTask = prepareTasksMap.get(searchInfo);
        if (searchTask == null) {
            searchTask = prepareSearch(searchInfo);
        } else {
            prepareTasksMap.remove(searchInfo);
        }
        if (!searchTask.searchDoneFlag) {
            databaseService.startSearchInThreadPool(searchTask);
        }
        event.setReturnValue(searchTask);
    }

    /**
     * 预搜索任务
     *
     * @param searchInfo searchInfo
     */
    private static SearchTask prepareSearch(SearchInfo searchInfo) {
        var databaseService = getInstance();

        long preparedSearchInfo = PathMatcher.INSTANCE.prepareSearchInfo(searchInfo.searchCase,
                searchInfo.isIgnoreCase,
                searchInfo.searchText,
                searchInfo.keywords,
                searchInfo.keywordsLowerCase,
                searchInfo.isKeywordPath,
                AllConfigs.getInstance().getConfigEntity().isEnableFuzzyMatch());
        if (preparedSearchInfo == 0) {
            throw new RuntimeException("Prepare search info failed");
        } else {
            if (IsDebug.isDebug) {
                log.info("Prepare search info id: {}", preparedSearchInfo);
            }
        }
        var searchTask = new SearchTask(searchInfo, preparedSearchInfo);

        databaseService.prepareResultContainer(searchTask);

        var threadPoolUtil = ThreadPoolUtil.getInstance();
        databaseService.searchCache(searchTask);
        threadPoolUtil.executeTask(() -> {
            try {
                databaseService.searchFolder(AllConfigs.getInstance().getConfigEntity().getPriorityFolder(), searchTask);
            } catch (Exception e) {
                log.error("error {}", e.getMessage(), e);
            }
        });
        threadPoolUtil.executeTask(() -> {
            // start menu
            try {
                String startMenu = GetWindowsKnownFolder.INSTANCE.getKnownFolder("{A4115719-D62E-491D-AA7C-E74B8BE3B067}");
                databaseService.searchFolder(startMenu, searchTask);
                startMenu = GetWindowsKnownFolder.INSTANCE.getKnownFolder("{625B53C3-AB48-4EC1-BA1F-A1EF4146FC19}");
                databaseService.searchFolder(startMenu, searchTask);
            } catch (Exception e) {
                log.error("error {}", e.getMessage(), e);
            }
        });
        threadPoolUtil.executeTask(() -> {
            // desktop
            try {
                String desktop = GetWindowsKnownFolder.INSTANCE.getKnownFolder("{B4BFCC3A-DB2C-424C-B029-7FE99A87C641}");
                databaseService.searchFolder(desktop, searchTask);
                desktop = GetWindowsKnownFolder.INSTANCE.getKnownFolder("{C4AA340D-F20F-4863-AFEF-F87EF2E6BA25}");
                databaseService.searchFolder(desktop, searchTask);
            } catch (Exception e) {
                log.error("error {}", e.getMessage(), e);
            }
        });
        threadPoolUtil.executeTask(() -> {
            try {
                PathMatcher.INSTANCE.iterateUwpApps(uwpResult -> {
                    if (uwpResult == null) return;
                    databaseService.checkIsMatchedAndAddToUwpList(uwpResult, searchTask);
                });
            } catch (Exception e) {
                log.error("error {}", e.getMessage(), e);
            }
        }, false);
        databaseService.prepareSearchTasks(searchTask);
        boolean isPatternMatch = searchTask.searchInfo.searchCase != null && List.of(searchTask.searchInfo.searchCase).contains(PathMatchUtil.SearchCase.PATTERN);
        if (isEnableGPUAccelerate && !searchTask.shouldStopSearch() && !isPatternMatch) {
            threadPoolUtil.executeTask(() -> {
                try {
                    // 退出上一次搜索
                    final var timeout = 3000;
                    GPUAccelerator.INSTANCE.stopCollectResults();
                    final long start = System.currentTimeMillis();
                    while (!SearchTask.isGpuThreadRunning.compareAndSet(false, true)) {
                        if (System.currentTimeMillis() - start > timeout) {
                            log.warn("等待上一次gpu加速完成超时");
                            return;
                        }
                        if (searchTask.shouldStopSearch()) {
                            return;
                        }
                        Thread.onSpinWait();
                    }
                    // 开始进行搜索
                    GPUAccelerator.INSTANCE.resetAllResultStatus();

                    boolean isCaseMatch = searchTask.searchInfo.searchCase != null &&
                                          List.of(searchTask.searchInfo.searchCase).contains(PathMatchUtil.SearchCase.CASE);
                    boolean enableFuzzyMatch = AllConfigs.getInstance().getConfigEntity().isEnableFuzzyMatch();

                    var threadNumber = Runtime.getRuntime().availableProcessors();
                    // collectThreadNumber根据threadNumber动态调整，最大不超过8线程，最小为2线程
                    int collectThreadNumber = Math.min(Math.max(threadNumber / 4, 2), 8);
                    GPUAccelerator.INSTANCE.match(
                            searchInfo.searchCase,
                            searchInfo.isIgnoreCase,
                            searchInfo.searchText,
                            searchInfo.keywords,
                            searchInfo.keywordsLowerCase,
                            searchInfo.isKeywordPath,
                            searchInfo.maxResultNum,
                            collectThreadNumber,
                            enableFuzzyMatch,
                            (key, resultMatched) -> {
                                if (IsDebug.isDebug) {
                                    log.info("GPU matched: {}", resultMatched);
                                }
                                if (searchTask.shouldStopSearch()) {
                                    return;
                                }
                                if (FileUtil.isFileNotExist(resultMatched)) {
                                    databaseService.removeFileFromDatabase(resultMatched);
                                    return;
                                }
                                if (searchTask.tempResultsSet.add(resultMatched)) {
                                    String fileName = FileUtil.getFileName(resultMatched);

                                    boolean recordContainsKeyword;
                                    if (Constants.IS_COMPLEX_GPU_KERNEL_FUNCTION_ENABLED) {
                                        recordContainsKeyword = isCaseMatch ?
                                                Arrays.stream(searchTask.searchInfo.keywords).anyMatch(fileName::contains) :
                                                Arrays.stream(searchTask.searchInfo.keywordsLowerCase).anyMatch(fileName::contains);
                                    } else {
                                        recordContainsKeyword = true;
                                    }
                                    SearchResult.buildFromPath(false, resultMatched, false, !recordContainsKeyword).ifPresent(searchResult -> {
                                        searchTask.resultCounter.getAndIncrement();
                                        databaseService.addSearchResultToContainer(searchTask.tempResults, searchResult);
                                    });
                                }
                            });
                } finally {
                    SearchTask.isGpuThreadRunning.set(false);
                }
            }, false);
        }
        return searchTask;
    }

    @EventRegister(registerClass = StopSearchEvent.class)
    private static void stopSearchEvent(Event event) {
        DatabaseService databaseService = getInstance();
        databaseService.stopAllSearch();
    }

    @EventListener(listenClass = BootSystemEvent.class)
    private static void databaseServiceInit(Event event) {
        DatabaseService databaseService = getInstance();
        databaseService.priorityMap = databaseService.getPriority();
        databaseService.initTableMap();
        databaseService.prepareDatabaseCache();
        var allConfigs = AllConfigs.getInstance();
        for (String diskPath : RegexUtil.comma.split(allConfigs.getAvailableDisks())) {
            for (int i = 0; i <= Constants.MAX_TABLE_NUM; i++) {
                for (var suffixPriorityPair : databaseService.priorityMap) {
                    var key = diskPath.charAt(0) + "," + "list" + i + "," + suffixPriorityPair.priority;
                    databaseService.tableCache.put(key, new Cache());
                }
            }
        }
        databaseService.syncFileChangesThread();
        databaseService.checkTimeAndSendExecuteSqlSignalThread();
        databaseService.executeAllCommands();
        databaseService.saveTableCacheThread();
        databaseService.addRestartMonitorThread();
        //防止第一次搜索时卡顿
        String[] keywordsWarmupArr = {"warmup"};
        var warmupEvent = new PrepareSearchEvent(() -> keywordsWarmupArr[0],
                () -> null,
                () -> keywordsWarmupArr);
        EventManagement.getInstance().putEvent(warmupEvent, e -> {
            var generatedSearchTask = e.getReturnValue();
            generatedSearchTask.ifPresent(o -> {
                var task = (SearchTask) o;
                PathMatcher.INSTANCE.closeConnections(task.connectionId);
            });
        }, null);

        if (IsDebug.isDebug) {
            databaseService.testSearchThread();
        }
    }

    @EventRegister(registerClass = AddToCacheEvent.class)
    private static void addToCacheEvent(Event event) {
        DatabaseService databaseService = getInstance();
        String path = ((AddToCacheEvent) event).path;
        Integer count;
        if ((count = databaseService.databaseCacheMap.get(path)) == null) {
            databaseService.databaseCacheMap.put(path, 1);
        } else {
            databaseService.databaseCacheMap.put(path, count + 1);
        }
        if (databaseService.status.get() == Constants.Enums.DatabaseStatus._TEMP) {
            return;
        }
        if (databaseService.databaseCacheMap.size() < AllConfigs.getInstance().getConfigEntity().getCacheNumLimit()) {
            databaseService.addFileToCache(path);
        } else {
            try (var connection = SQLiteUtil.getDbConnection("cache");
                 Statement statement = connection.createStatement();
                 ResultSet resultSet = statement.executeQuery("select cache.PATH, COUNT from cache left join statistics s on cache.PATH = s.PATH order by COUNT limit 1;")) {
                if (resultSet.next()) {
                    String eachLine = resultSet.getString("PATH");
                    databaseService.removeFileFromCache(eachLine);
                }
            } catch (SQLException e) {
                log.error(e.getMessage(), e);
            }
            databaseService.addFileToCache(path);
        }
    }

    @EventRegister(registerClass = DeleteFromCacheEvent.class)
    private static void deleteFromCacheEvent(Event event) {
        DatabaseService databaseService = getInstance();
        String path = ((DeleteFromCacheEvent) event).path;
        databaseService.databaseCacheMap.remove(path);
        if (databaseService.status.get() == Constants.Enums.DatabaseStatus._TEMP) {
            return;
        }
        databaseService.removeFileFromCache(path);
    }

    @EventRegister(registerClass = UpdateDatabaseEvent.class)
    private static void updateDatabaseEvent(Event event) {
        LuceneIndexService.stopAllIndex();
        long startTimeMills = System.currentTimeMillis();
        while (!LuceneIndexService.isIndexStopped() && System.currentTimeMillis() - startTimeMills <= 3000) {
            try {
                TimeUnit.MILLISECONDS.sleep(100);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        DatabaseService databaseService = getInstance();

        // 清除数据库结果统计缓存，因为数据库即将更新
        databaseService.clearDatabaseResultsCountCache();

        UpdateDatabaseEvent updateDatabaseEvent = (UpdateDatabaseEvent) event;
        // 在这里设置数据库状态为manual update
        try {
            if (!databaseService.updateLists(AllConfigs.getInstance().getConfigEntity().getIgnorePath(), updateDatabaseEvent.isDropPrevious)) {
                throw new RuntimeException("search failed");
            }
        } catch (IOException | InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    @EventRegister(registerClass = OptimizeDatabaseEvent.class)
    private static void optimizeDatabaseEvent(Event event) {
        DatabaseService databaseService = getInstance();
        if (databaseService.status.get() == Constants.Enums.DatabaseStatus._TEMP) {
            return;
        }
        if (!databaseService.casSetStatus(Constants.Enums.DatabaseStatus.NORMAL, Constants.Enums.DatabaseStatus.VACUUM)) {
            throw new RuntimeException("databaseService status设置VACUUM状态失败");
        }
        //执行VACUUM命令
        String[] splitDisks = RegexUtil.comma.split(AllConfigs.getInstance().getAvailableDisks());
        for (String eachDisk : splitDisks) {
            try (var connection = SQLiteUtil.getDbConnection(String.valueOf(eachDisk.charAt(0)));
                 Statement stmt = connection.createStatement()) {
                stmt.execute("VACUUM;");
                stmt.execute("PRAGMA optimize;");
            } catch (Exception ex) {
                log.error("error: {}", ex.getMessage(), ex);
            } finally {
                if (IsDebug.isDebug) {
                    log.info("结束优化");
                }
            }
        }
        if (!databaseService.casSetStatus(Constants.Enums.DatabaseStatus.VACUUM, Constants.Enums.DatabaseStatus.NORMAL)) {
            throw new RuntimeException("databaseService status从VACUUM修改为NORMAL失败");
        }
    }

    @EventRegister(registerClass = AddToSuffixPriorityMapEvent.class)
    private static void addToSuffixPriorityMapEvent(Event event) {
        DatabaseService databaseService = getInstance();
        if (databaseService.status.get() == Constants.Enums.DatabaseStatus._TEMP) {
            return;
        }
        AddToSuffixPriorityMapEvent event1 = (AddToSuffixPriorityMapEvent) event;
        String suffix = event1.suffix.toLowerCase();
        int priority = event1.priority;
        databaseService.addToCommandQueue(
                new SQLWithTaskId(String.format("INSERT INTO priority VALUES('%s', %d);", suffix, priority), SqlTaskIds.UPDATE_SUFFIX, "cache"));
    }

    @EventRegister(registerClass = ClearSuffixPriorityMapEvent.class)
    private static void clearSuffixPriorityMapEvent(Event event) {
        DatabaseService databaseService = getInstance();
        if (databaseService.status.get() == Constants.Enums.DatabaseStatus._TEMP) {
            return;
        }
        databaseService.addToCommandQueue(new SQLWithTaskId("DELETE FROM priority;", SqlTaskIds.UPDATE_SUFFIX, "cache"));
        databaseService.addToCommandQueue(
                new SQLWithTaskId("INSERT INTO priority VALUES('defaultPriority', 0);", SqlTaskIds.UPDATE_SUFFIX, "cache"));
        databaseService.addToCommandQueue(
                new SQLWithTaskId("INSERT INTO priority VALUES('dirPriority', -1);", SqlTaskIds.UPDATE_SUFFIX, "cache"));
    }

    @EventRegister(registerClass = DeleteFromSuffixPriorityMapEvent.class)
    private static void deleteFromSuffixPriorityMapEvent(Event event) {
        DeleteFromSuffixPriorityMapEvent delete = (DeleteFromSuffixPriorityMapEvent) event;
        DatabaseService databaseService = getInstance();
        if (databaseService.status.get() == Constants.Enums.DatabaseStatus._TEMP) {
            return;
        }
        if ("dirPriority".equals(delete.suffix) || "defaultPriority".equals(delete.suffix)) {
            return;
        }
        databaseService.addToCommandQueue(new SQLWithTaskId(String.format("DELETE FROM priority where SUFFIX='%s'", delete.suffix), SqlTaskIds.UPDATE_SUFFIX, "cache"));
    }

    @EventRegister(registerClass = UpdateSuffixPriorityEvent.class)
    private static void updateSuffixPriorityEvent(Event event) {
        DatabaseService databaseService = DatabaseService.getInstance();
        if (databaseService.status.get() == Constants.Enums.DatabaseStatus._TEMP) {
            return;
        }
        EventManagement eventManagement = EventManagement.getInstance();
        UpdateSuffixPriorityEvent update = (UpdateSuffixPriorityEvent) event;
        String origin = update.originSuffix;
        String newSuffix = update.newSuffix;
        int newNum = update.newPriority;
        eventManagement.putEvent(new DeleteFromSuffixPriorityMapEvent(origin));
        eventManagement.putEvent(new AddToSuffixPriorityMapEvent(newSuffix, newNum));
    }

    @EventListener(listenClass = CloseEvent.class)
    private static void closeEvent(Event event) {
        stopMonitorDisks(AllConfigs.getInstance().
                getConfigEntity().
                getAdvancedConfigEntity().
                isDeleteUsnOnExit());
        var databaseService = getInstance();
        databaseService.executeAllCommands();
        databaseService.stopAllSearch();

        SQLiteUtil.closeAll();
        if (isEnableGPUAccelerate) {
            GPUAccelerator.INSTANCE.stopCollectResults();
            GPUAccelerator.INSTANCE.release();
        }
        PathMatcher.INSTANCE.cleanupUwpApps();
    }

    @Data
    @EqualsAndHashCode
    private static class SQLWithTaskId {
        private final String sql;
        private final SqlTaskIds taskId;
        private final String diskStr;
        private volatile String key;
    }

    private enum SqlTaskIds {
        DELETE_FROM_LIST, DELETE_FROM_CACHE, DELETE_FROM_STATISTICS, INSERT_TO_LIST, INSERT_TO_CACHE, INSERT_TO_STATISTICS, UPDATE_STATISTICS,
        CREATE_INDEX, CREATE_TABLE, DROP_TABLE, DROP_INDEX, UPDATE_SUFFIX, UPDATE_WEIGHT, UPDATE_FILE_INFO
    }

    @SuppressWarnings("unused")
    private static class GPUCacheService {
        private static final Set<String> invalidCacheKeys = ConcurrentHashMap.newKeySet();
        private static final ConcurrentHashMap<String, Set<String>> recordsToAdd = new ConcurrentHashMap<>();
        private static final ConcurrentHashMap<String, Set<String>> recordsToRemove = new ConcurrentHashMap<>();

        private static void clearInvalidCacheThread() {
            //检测缓存是否有效并删除缓存
            ThreadPoolUtil.getInstance().executeTask(() -> {
                EventManagement eventManagement = EventManagement.getInstance();
                DatabaseService databaseService = DatabaseService.getInstance();
                long startCheckInvalidCacheTime = System.currentTimeMillis();
                final long checkInterval = 10 * 60 * 1000; // 10min
                while (eventManagement.notMainExit()) {
                    if (System.currentTimeMillis() - startCheckInvalidCacheTime > checkInterval && !WindowCheck.INSTANCE.isForegroundFullscreen()) {
                        startCheckInvalidCacheTime = System.currentTimeMillis();
                        HashSet<String> keysToRemove = new HashSet<>(invalidCacheKeys);
                        for (var eachKey : keysToRemove) {
                            if (IsDebug.isDebug) {
                                log.info("清除GPU缓存，key：" + eachKey);
                            }
                            GPUAccelerator.INSTANCE.clearCache(eachKey);
                        }
                        invalidCacheKeys.removeAll(keysToRemove);
                    }
                    try {
                        TimeUnit.MILLISECONDS.sleep(100);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }, false);
        }

        private static void execWorkQueueThread() {
            ThreadPoolUtil.getInstance().executeTask(() -> {
                EventManagement eventManagement = EventManagement.getInstance();
                DatabaseService databaseService = DatabaseService.getInstance();
                final int removeRecordsThreshold = 100;
                AllConfigs allConfigs = AllConfigs.getInstance();
                long lastCheckTime = System.currentTimeMillis();
                while (eventManagement.notMainExit()) {
                    if (databaseService.getStatus() == Constants.Enums.DatabaseStatus.NORMAL &&
                        !WindowCheck.INSTANCE.isForegroundFullscreen() &&
                        (!recordsToAdd.isEmpty() || !recordsToRemove.isEmpty()) &&
                        System.currentTimeMillis() - lastCheckTime > allConfigs.getConfigEntity().getUpdateTimeLimit() * 1000L) {
                        lastCheckTime = System.currentTimeMillis();
                        for (var entry : recordsToAdd.entrySet()) {
                            String k = entry.getKey();
                            Set<String> container = entry.getValue();
                            if (container.isEmpty()) continue;
                            var records = container.toArray();
                            GPUAccelerator.INSTANCE.addRecordsToCache(k, records);
                            for (Object record : records) {
                                container.remove((String) record);
                            }
                            if (GPUAccelerator.INSTANCE.isCacheExist(k) && !GPUAccelerator.INSTANCE.isCacheValid(k)) {
                                invalidCacheKeys.add(k);
                            }
                        }
                        for (var entry : recordsToRemove.entrySet()) {
                            String key = entry.getKey();
                            Set<String> container = entry.getValue();
                            if (container.size() < removeRecordsThreshold) continue;
                            var records = container.toArray();
                            GPUAccelerator.INSTANCE.removeRecordsFromCache(key, records);
                            for (Object record : records) {
                                container.remove((String) record);
                            }
                            if (GPUAccelerator.INSTANCE.isCacheExist(key) && !GPUAccelerator.INSTANCE.isCacheValid(key)) {
                                invalidCacheKeys.add(key);
                            }
                        }
                    }
                    try {
                        TimeUnit.SECONDS.sleep(1);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }, false);
        }

        private static void addRecord(String key, String fileRecord) {
            Set<String> container;
            container = recordsToAdd.get(key);
            if (container == null) {
                synchronized (recordsToAdd) {
                    container = recordsToAdd.computeIfAbsent(key, k -> ConcurrentHashMap.newKeySet());
                }
            }
            container.add(fileRecord);
        }

        private static void removeRecord(String key, String fileRecord) {
            Set<String> container = recordsToRemove.get(key);
            if (container == null) {
                synchronized (recordsToRemove) {
                    container = recordsToRemove.computeIfAbsent(key, k -> ConcurrentHashMap.newKeySet());
                }
            }
            container.add(fileRecord);
        }

        @EventListener(listenClass = BootSystemEvent.class)
        private static void startThread(Event event) {
            if (!isEnableGPUAccelerate) {
                return;
            }
            clearInvalidCacheThread();
            //向gpu缓存添加或删除记录线程
            execWorkQueueThread();
        }

        @EventRegister(registerClass = GPUAddRecordEvent.class)
        private static void addToGPUMemory(Event event) {
            if (!isEnableGPUAccelerate) {
                return;
            }
            GPUAddRecordEvent gpuAddRecordEvent = (GPUAddRecordEvent) event;
            addRecord(gpuAddRecordEvent.key, gpuAddRecordEvent.record);
        }

        @EventRegister(registerClass = GPURemoveRecordEvent.class)
        private static void removeFromGPUMemory(Event event) {
            if (!isEnableGPUAccelerate) {
                return;
            }
            GPURemoveRecordEvent gpuRemoveRecordEvent = (GPURemoveRecordEvent) event;
            removeRecord(gpuRemoveRecordEvent.key, gpuRemoveRecordEvent.record);
        }

        @EventRegister(registerClass = GPUClearCacheEvent.class)
        private static void clearCacheGPU(Event event) {
            if (!isEnableGPUAccelerate) {
                return;
            }
            ThreadPoolUtil.getInstance().executeTask(GPUAccelerator.INSTANCE::clearAllCache, false);

        }
    }

    /**
     * 保存每个key[C,list1,-1]中数据所占用的内存大小，记录个数
     *
     * @param totalBytes  占用内存大小
     * @param recordCount 记录个数
     */
    private record DatabaseRecordInfo(AtomicInteger totalBytes, AtomicInteger recordCount) {
    }

    /**
     * 用于序列化的数据库记录信息
     */
    @Data
    private static class SerializableDatabaseRecordInfo {
        private int totalBytes;
        private int recordCount;

        public SerializableDatabaseRecordInfo(int totalBytes, int recordCount) {
            this.totalBytes = totalBytes;
            this.recordCount = recordCount;
        }

        public static SerializableDatabaseRecordInfo from(DatabaseRecordInfo info) {
            return new SerializableDatabaseRecordInfo(info.totalBytes().get(), info.recordCount().get());
        }

        public DatabaseRecordInfo toDatabaseRecordInfo() {
            return new DatabaseRecordInfo(new AtomicInteger(totalBytes), new AtomicInteger(recordCount));
        }
    }

    /**
     * 保存数据库结果统计到加密的缓存文件
     */
    private void saveDatabaseResultsCountCache() {
        try {
            Map<String, SerializableDatabaseRecordInfo> cacheMap = new HashMap<>();
            for (Map.Entry<String, DatabaseRecordInfo> entry : databaseResultsCount.entrySet()) {
                cacheMap.put(entry.getKey(), SerializableDatabaseRecordInfo.from(entry.getValue()));
            }

            Gson gson = GsonUtil.INSTANCE.getGson();
            String json = gson.toJson(cacheMap);

            // 使用Encryption API获取密钥并加密JSON
            String encryptionKey = Encryption.INSTANCE.getkey();
            String encryptedJson = encryptString(json, encryptionKey);

            try (var writer = new BufferedWriter(new OutputStreamWriter(
                    new FileOutputStream(Constants.DATABASE_RESULTS_COUNT_CACHE_FILE), StandardCharsets.UTF_8))) {
                writer.write(encryptedJson);
            }

            if (IsDebug.isDebug) {
                log.info("数据库结果统计已加密保存到缓存文件: {}", Constants.DATABASE_RESULTS_COUNT_CACHE_FILE);
            }
        } catch (Exception e) {
            log.warn("保存数据库结果统计缓存失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 从加密的缓存文件加载数据库结果统计
     */
    private void loadDatabaseResultsCountCache() {
        try {
            if (!FileUtil.isFileExist(Constants.DATABASE_RESULTS_COUNT_CACHE_FILE)) {
                if (IsDebug.isDebug) {
                    log.info("数据库结果统计缓存文件不存在，将重新扫描数据库");
                }
                return;
            }

            // 读取加密的文件内容
            String encryptedContent;
            try (var reader = new BufferedReader(new InputStreamReader(
                    new FileInputStream(Constants.DATABASE_RESULTS_COUNT_CACHE_FILE), StandardCharsets.UTF_8))) {
                StringBuilder sb = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    sb.append(line);
                }
                encryptedContent = sb.toString();
            }

            // 使用Encryption API获取密钥并解密JSON
            String encryptionKey = Encryption.INSTANCE.getkey();
            String decryptedJson = decryptString(encryptedContent, encryptionKey);

            Gson gson = GsonUtil.INSTANCE.getGson();
            Type type = new TypeToken<Map<String, SerializableDatabaseRecordInfo>>() {
            }.getType();

            Map<String, SerializableDatabaseRecordInfo> cacheMap = gson.fromJson(decryptedJson, type);

            if (cacheMap != null) {
                databaseResultsCount.clear();
                for (Map.Entry<String, SerializableDatabaseRecordInfo> entry : cacheMap.entrySet()) {
                    databaseResultsCount.put(entry.getKey(), entry.getValue().toDatabaseRecordInfo());
                }

                if (IsDebug.isDebug) {
                    log.info("成功从加密缓存文件加载数据库结果统计，共{}条记录", databaseResultsCount.size());
                }
            }
        } catch (Exception e) {
            log.warn("加载数据库结果统计缓存失败: {}", e.getMessage(), e);
            // 清除可能损坏的缓存文件
            try {
                Files.deleteIfExists(Path.of(Constants.DATABASE_RESULTS_COUNT_CACHE_FILE));
            } catch (IOException ex) {
                log.warn("删除损坏的缓存文件失败: {}", ex.getMessage());
            }
        }
    }

    /**
     * 清除数据库结果统计缓存文件
     */
    private void clearDatabaseResultsCountCache() {
        try {
            Files.deleteIfExists(Path.of(Constants.DATABASE_RESULTS_COUNT_CACHE_FILE));
            if (IsDebug.isDebug) {
                log.info("已清除数据库结果统计缓存文件");
            }
        } catch (IOException e) {
            log.warn("清除数据库结果统计缓存文件失败: {}", e.getMessage(), e);
        }
    }

    private record SuffixPriorityPair(String suffix, int priority) {
    }

    /**
     * 内存缓存包装类
     * 数据库缓存分为GPU加速缓存和内存缓存
     * 当GPU加速不可用或者关闭时，将会启用内存缓存
     */
    private static class Cache {
        private final AtomicBoolean isCached = new AtomicBoolean(false);
        private final AtomicBoolean isFileLost = new AtomicBoolean(false);
        private SoftReference<CopyOnWriteArraySet<RecordCache>> data = null;

        private static final ConcurrentHashMap<Integer, String> parentPathCacheMap = new ConcurrentHashMap<>();
        private static final ConcurrentHashMap<String, Integer> parentPathCacheMapInverse = new ConcurrentHashMap<>();
        private static final ConcurrentHashMap<String, AtomicInteger> parentPathCacheRefCountMap = new ConcurrentHashMap<>();
        private static final AtomicInteger parentPathCacheIdGenerator = new AtomicInteger();

        private boolean isCacheValid() {
            var dataRef = data;
            if (dataRef == null) {
                return false;
            }
            CopyOnWriteArraySet<RecordCache> recordCaches = dataRef.get();
            return isCached.get() && !isFileLost.get() && recordCaches != null && !recordCaches.isEmpty();
        }

        private long parallelSearch(Function<RecordCache, Boolean> matchFunc) {
            var dataRef = data;
            if (dataRef == null) {
                return 0;
            }
            var listRef = dataRef.get();
            if (listRef == null) {
                return 0;
            }
            return listRef.parallelStream()
                    .filter(matchFunc::apply)
                    .count();
        }

        private void removePathFromCache(String path) {
            if (path == null || path.isEmpty()) {
                return;
            }
            String parentPath = FileUtil.getParentPath(path);
            String fileName = FileUtil.getFileName(path);

            var dataRef = data;
            if (dataRef == null) {
                return;
            }
            CopyOnWriteArraySet<RecordCache> recordCaches = dataRef.get();
            if (recordCaches == null) {
                return;
            }

            Integer parentPathKey = parentPathCacheMapInverse.get(parentPath);
            if (parentPathKey == null) {
                return;
            }
            synchronized (Cache.class) {
                boolean removed = recordCaches.removeIf(each -> each.fileName().equals(fileName) && each.parentPathId() == parentPathKey);
                if (removed) {
                    AtomicInteger refCounter = parentPathCacheRefCountMap.get(parentPath);
                    int parentRefCount = refCounter.decrementAndGet();
                    if (parentRefCount == 0) {
                        parentPathCacheMap.remove(parentPathKey);
                        parentPathCacheMapInverse.remove(parentPath);
                        parentPathCacheRefCountMap.remove(parentPath);
                    }
                }
            }
        }

        private void savePathCache(String path) {
            if (path == null || path.isEmpty()) {
                return;
            }
            String parentPath = FileUtil.getParentPath(path);
            String fileName = FileUtil.getFileName(path);

            synchronized (Cache.class) {
                Integer parentPathIdKey = parentPathCacheMapInverse.get(parentPath);
                if (parentPathIdKey == null) {
                    parentPathIdKey = parentPathCacheIdGenerator.getAndIncrement();
                    parentPathCacheMap.put(parentPathIdKey, parentPath);
                    parentPathCacheMapInverse.put(parentPath, parentPathIdKey);
                    parentPathCacheRefCountMap.put(parentPath, new AtomicInteger(1));
                } else {
                    AtomicInteger refCounter = parentPathCacheRefCountMap.get(parentPath);
                    refCounter.getAndIncrement();
                }

                if (data == null) {
                    data = new SoftReference<>(null);
                }
                CopyOnWriteArraySet<RecordCache> recordCaches = data.get();
                if (recordCaches == null) {
                    recordCaches = new CopyOnWriteArraySet<>();
                    data = new SoftReference<>(recordCaches);
                }
                recordCaches.add(new RecordCache(fileName, parentPathIdKey));
            }
        }

        private void savePathCacheBatch(List<String> paths) {
            if (paths == null || paths.isEmpty()) {
                return;
            }

            synchronized (Cache.class) {
                // 预处理所有路径，统计父路径引用次数
                Map<String, Integer> parentPathRefCounts = new HashMap<>();
                List<Map.Entry<String, String>> pathEntries = new ArrayList<>();

                for (String path : paths) {
                    String parentPath = FileUtil.getParentPath(path);
                    String fileName = FileUtil.getFileName(path);
                    pathEntries.add(Map.entry(parentPath, fileName));
                    parentPathRefCounts.merge(parentPath, 1, Integer::sum);
                }

                // 批量处理父路径ID分配和引用计数更新
                for (Map.Entry<String, Integer> entry : parentPathRefCounts.entrySet()) {
                    String parentPath = entry.getKey();
                    Integer refCount = entry.getValue();

                    Integer parentPathIdKey = parentPathCacheMapInverse.get(parentPath);
                    if (parentPathIdKey == null) {
                        parentPathIdKey = parentPathCacheIdGenerator.getAndIncrement();
                        parentPathCacheMap.put(parentPathIdKey, parentPath);
                        parentPathCacheMapInverse.put(parentPath, parentPathIdKey);
                        parentPathCacheRefCountMap.put(parentPath, new AtomicInteger(refCount));
                    } else {
                        AtomicInteger refCounter = parentPathCacheRefCountMap.get(parentPath);
                        refCounter.addAndGet(refCount);
                    }
                }

                // 确保data容器已初始化
                if (data == null) {
                    data = new SoftReference<>(null);
                }
                CopyOnWriteArraySet<RecordCache> recordCaches = data.get();
                if (recordCaches == null) {
                    recordCaches = new CopyOnWriteArraySet<>();
                    data = new SoftReference<>(recordCaches);
                }

                // 批量添加记录缓存
                for (Map.Entry<String, String> pathEntry : pathEntries) {
                    String parentPath = pathEntry.getKey();
                    String fileName = pathEntry.getValue();
                    Integer parentPathIdKey = parentPathCacheMapInverse.get(parentPath);
                    recordCaches.add(new RecordCache(fileName, parentPathIdKey));
                }
            }
        }

        /**
         * 清除缓存并更新父路径引用计数
         */
        private void clearCache() {
            synchronized (Cache.class) {
                var dataRef = data;
                if (dataRef != null) {
                    CopyOnWriteArraySet<RecordCache> recordCaches = dataRef.get();
                    if (recordCaches != null) {
                        // 统计需要减少引用计数的父路径
                        Map<Integer, Integer> parentPathRefCounts = new HashMap<>();
                        for (RecordCache record : recordCaches) {
                            parentPathRefCounts.merge(record.parentPathId(), 1, Integer::sum);
                        }

                        // 批量更新父路径引用计数
                        for (Map.Entry<Integer, Integer> entry : parentPathRefCounts.entrySet()) {
                            Integer parentPathId = entry.getKey();
                            Integer refCount = entry.getValue();
                            String parentPath = parentPathCacheMap.get(parentPathId);

                            if (parentPath != null) {
                                AtomicInteger refCounter = parentPathCacheRefCountMap.get(parentPath);
                                if (refCounter != null) {
                                    int newRefCount = refCounter.addAndGet(-refCount);
                                    if (newRefCount <= 0) {
                                        // 引用计数为0，清理相关缓存
                                        parentPathCacheMap.remove(parentPathId);
                                        parentPathCacheMapInverse.remove(parentPath);
                                        parentPathCacheRefCountMap.remove(parentPath);
                                    }
                                }
                            }
                        }
                    }
                }

                // 清空缓存数据
                data = null;
                isCached.set(false);
                isFileLost.set(false);
            }
        }
    }

    private record RecordCache(String fileName, int parentPathId) {
    }

    private static class TableNameWeightInfo {
        private final String tableName;
        private final AtomicLong weight;

        private TableNameWeightInfo(String tableName, int weight) {
            this.tableName = tableName;
            this.weight = new AtomicLong(weight);
        }
    }

    /**
     * 搜索任务封装
     * 根据list0-40，以及后缀优先级生成任务，放入taskQueue中
     * 在收到startSearchEvent之后将会遍历taskQueue执行搜索任务
     *
     * @see #startSearch(SearchTask)
     * <p>
     * 搜索结果将会被暂存到priorityContainer中，key为后缀优先级，value为该后缀的文件路径，同时也会存入tempResultsSet中用于去重
     * 在等待搜索完成时，priorityContainer中的数据会被不断转存到tempResults中，按照后缀优先级降序排列，优先级高的文件将会先转存
     * <p>
     * taskStatus和allTaskStatus是每个任务的标志，每个任务分配一个位，当某一个任务完成，在taskStatus上该任务的位将会被设置为1
     * 当taskstauts和allTaskStatus相等则表示任务全部完成
     * @see #waitForTasks(SearchTask, CountDownLatch)
     */
    @RequiredArgsConstructor
    public static class SearchTask {
        //taskMap任务队列，key为磁盘盘符，value为任务
        private final ConcurrentHashMap<String, ConcurrentLinkedQueue<Runnable>> taskMap = new ConcurrentHashMap<>();
        @Getter
        private final SearchInfo searchInfo;
        private final long preparedSearchInfoId;
        @Getter
        private final ConcurrentHashMap<String, ConcurrentLinkedQueue<SearchResult>> tempResults = new ConcurrentHashMap<>();
        @Getter
        private final ConcurrentLinkedQueue<UwpResult> uwpResults = new ConcurrentLinkedQueue<>();
        private final Set<String> tempResultsSet = ConcurrentHashMap.newKeySet();
        private final AtomicInteger resultCounter = new AtomicInteger();
        @Getter
        private final UUID uuid = UUID.randomUUID();
        private volatile boolean searchDoneFlag = false;
        @Setter
        private volatile boolean fullTextSearchDone = false;
        @Getter
        private final long taskCreateTimeMills = System.currentTimeMillis();
        private volatile boolean shouldStopSearchFlag = false;
        @Getter
        private long taskExecutionDuration = 0;
        @Setter
        private volatile long connectionId;


        private static final AtomicBoolean isGpuThreadRunning = new AtomicBoolean();
        private static final long maxTaskValidThreshold = 10_000;

        public boolean isSearchDone() {
            return searchDoneFlag && fullTextSearchDone;
        }

        public void stopSearch() {
            shouldStopSearchFlag = true;
        }

        private boolean shouldStopSearch() {
            return resultCounter.get() > searchInfo.maxResultNum || shouldStopSearchFlag;
        }

        public void addResultsToTempList(List<String> results, boolean contentMatch) {
            DatabaseService databaseService = DatabaseService.getInstance();
            for (String result : results) {
                Optional<SearchResult> searchResultOpt = SearchResult.buildFromPath(false, result, contentMatch, false);
                searchResultOpt.ifPresent(searchResult ->
                        databaseService.addSearchResultToContainer(tempResults, searchResult));
            }
        }
    }

    public record SearchInfo(String[] searchCase, boolean isIgnoreCase, String searchText, String[] keywords,
                             String[] keywordsLowerCase, boolean[] isKeywordPath, Integer maxResultNum) {

        @Override
        public boolean equals(Object o) {
            if (o == null || getClass() != o.getClass()) return false;
            SearchInfo that = (SearchInfo) o;
            return isIgnoreCase == that.isIgnoreCase && Objects.equals(searchText, that.searchText) && Objects.deepEquals(keywords, that.keywords) && Objects.deepEquals(searchCase, that.searchCase) && Objects.equals(maxResultNum, that.maxResultNum) && Objects.deepEquals(isKeywordPath, that.isKeywordPath) && Objects.deepEquals(keywordsLowerCase, that.keywordsLowerCase);
        }

        @Override
        public int hashCode() {
            return Objects.hash(Arrays.hashCode(searchCase), isIgnoreCase, searchText, Arrays.hashCode(keywords), Arrays.hashCode(keywordsLowerCase), Arrays.hashCode(isKeywordPath), maxResultNum);
        }
    }

    private record SearchTableAndPriority(String table, Integer priority, String otherCondition) {

        @Override
        public String toString() {
            if (otherCondition == null || otherCondition.isEmpty()) {
                return "SELECT %s FROM " + this.table +
                       " INNER JOIN folder ON folder.ID = " + this.table + ".FOLDER_ID" +
                       " WHERE PRIORITY=" + this.priority;
            }
            return "SELECT %s FROM " + this.table +
                   " INNER JOIN folder ON folder.ID = " + this.table + ".FOLDER_ID" +
                   " WHERE PRIORITY=" + this.priority +
                   " AND " +
                   this.otherCondition;
        }
    }

    /**
     * 使用AES加密字符串
     *
     * @param plainText 明文
     * @param key       密钥
     * @return 加密后的Base64字符串
     */
    private String encryptString(String plainText, String key) throws Exception {
        // 确保密钥长度为16字节（AES-128）
        String aesKey = key;
        if (aesKey.length() > 16) {
            aesKey = aesKey.substring(0, 16);
        } else if (aesKey.length() < 16) {
            // 如果密钥不足16位，用0填充
            StringBuilder sb = new StringBuilder(aesKey);
            while (sb.length() < 16) {
                sb.append('0');
            }
            aesKey = sb.toString();
        }

        SecretKeySpec secretKey = new SecretKeySpec(aesKey.getBytes(StandardCharsets.UTF_8), "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    /**
     * 使用AES解密字符串
     *
     * @param encryptedText 加密的Base64字符串
     * @param key           密钥
     * @return 解密后的明文
     */
    private String decryptString(String encryptedText, String key) throws Exception {
        // 确保密钥长度为16字节（AES-128）
        String aesKey = key;
        if (aesKey.length() > 16) {
            aesKey = aesKey.substring(0, 16);
        } else if (aesKey.length() < 16) {
            // 如果密钥不足16位，用0填充
            StringBuilder sb = new StringBuilder(aesKey);
            while (sb.length() < 16) {
                sb.append('0');
            }
            aesKey = sb.toString();
        }

        SecretKeySpec secretKey = new SecretKeySpec(aesKey.getBytes(StandardCharsets.UTF_8), "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedText));
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }
}

